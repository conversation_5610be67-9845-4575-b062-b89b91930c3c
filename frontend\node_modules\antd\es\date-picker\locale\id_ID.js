import CalendarLocale from "rc-picker/es/locale/id_ID";
import TimePickerLocale from '../../time-picker/locale/id_ID';
// Merge into a locale object
const locale = {
  lang: Object.assign({
    placeholder: '<PERSON><PERSON><PERSON> tanggal',
    yearPlaceholder: '<PERSON><PERSON><PERSON> tahun',
    quarterPlaceholder: '<PERSON><PERSON><PERSON> kuartal',
    monthPlaceholder: '<PERSON><PERSON><PERSON> bulan',
    weekPlaceholder: '<PERSON><PERSON>h minggu',
    rangePlaceholder: ['Tanggal awal', 'Tanggal akhir'],
    rangeYearPlaceholder: ['<PERSON>hun awal', '<PERSON>hun akhir'],
    rangeQuarterPlaceholder: ['<PERSON>artal awal', '<PERSON><PERSON>al akhir'],
    rangeMonthPlaceholder: ['<PERSON><PERSON><PERSON> awal', '<PERSON><PERSON><PERSON> akhir'],
    rangeWeekPlaceholder: ['<PERSON><PERSON> awal', '<PERSON>gu akhir']
  }, CalendarLocale),
  timePickerLocale: Object.assign({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;