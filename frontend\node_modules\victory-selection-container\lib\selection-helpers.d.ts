/// <reference types="lodash" />
declare class SelectionHelpersClass {
    getDimension(props: any): any;
    getDatasets(props: any): {
        childName: any;
        data: any;
    }[] | {
        data: any;
    }[];
    filterDatasets(props: any, datasets: any): any;
    getSelectedData(props: any, dataset: any): {
        eventKey: number[];
        data: any[];
    } | null;
    onMouseDown: (evt: any, targetProps: any) => {};
    private handleMouseMove;
    onMouseMove: import("lodash").DebouncedFunc<(evt: any, targetProps: any) => {
        target: string;
        mutation: () => {
            x2: number;
            y2: number;
            parentSVG: any;
        };
    } | null>;
    onMouseUp: (evt: any, targetProps: any) => {
        target: string;
        mutation: () => {
            select: boolean;
            x1: null;
            x2: null;
            y1: null;
            y2: null;
        };
    }[] | null;
}
export declare const SelectionHelpers: SelectionHelpersClass;
export {};
//# sourceMappingURL=selection-helpers.d.ts.map