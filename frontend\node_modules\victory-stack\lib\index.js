"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});

var _victoryStack = require("./victory-stack");

Object.keys(_victoryStack).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _victoryStack[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _victoryStack[key];
    }
  });
});