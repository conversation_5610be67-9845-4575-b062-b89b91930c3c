import CalendarLocale from "rc-picker/es/locale/sr_RS";
import TimePickerLocale from '../../time-picker/locale/sr_RS';
// Merge into a locale object
const locale = {
  lang: Object.assign({
    placeholder: 'Izaberi datum',
    yearPlaceholder: 'I<PERSON><PERSON>i godinu',
    quarterPlaceholder: 'Izaberi tromesečje',
    monthPlaceholder: 'Izaberi mesec',
    weekPlaceholder: 'I<PERSON><PERSON>i sedmicu',
    rangePlaceholder: ['<PERSON><PERSON> početka', '<PERSON><PERSON> završetka'],
    rangeYearPlaceholder: ['<PERSON><PERSON> po<PERSON>', '<PERSON><PERSON> završet<PERSON>'],
    rangeMonthPlaceholder: ['Mesec početka', 'Mesec završetka'],
    rangeWeekPlaceholder: ['Sedmica početka', 'Sedmica završetka']
  }, CalendarLocale),
  timePickerLocale: Object.assign({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;