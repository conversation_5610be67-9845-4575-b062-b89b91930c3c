!function(t,n){"object"==typeof exports&&"object"==typeof module?module.exports=n(require("react")):"function"==typeof define&&define.amd?define(["react"],n):"object"==typeof exports?exports.VictoryStack=n(require("react")):t.VictoryStack=n(t.React)}(self,(t=>(()=>{var n={2516:(t,n)=>{function e(t,n){var e=[],r=[];return null==n&&(n=function(t,n){return e[0]===n?"[Circular ~]":"[Circular ~."+r.slice(0,e.indexOf(n)).join(".")+"]"}),function(o,i){if(e.length>0){var a=e.indexOf(this);~a?e.splice(a+1):e.push(this),~a?r.splice(a,1/0,o):r.push(o),~e.indexOf(i)&&(i=n.call(this,o,i))}else e.push(i);return null==t?i:t.call(this,o,i)}}(t.exports=function(t,n,r,o){return JSON.stringify(t,e(n,o),r)}).getSerialize=e},3273:(t,n,e)=>{var r=e(163);t.exports=function(){if(!arguments.length)return[];var t=arguments[0];return r(t)?t:[t]}},8708:(t,n,e)=>{var r=e(1171),o=e(7838),i=e(4859),a=e(4073),u=e(8541);function c(t){var n=-1,e=null==t?0:t.length;for(this.clear();++n<e;){var r=t[n];this.set(r[0],r[1])}}c.prototype.clear=r,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},2596:(t,n,e)=>{var r=e(2373).Symbol;t.exports=r},6082:t=>{t.exports=function(t,n,e){switch(e.length){case 0:return t.call(n);case 1:return t.call(n,e[0]);case 2:return t.call(n,e[0],e[1]);case 3:return t.call(n,e[0],e[1],e[2])}return t.apply(n,e)}},6649:t=>{t.exports=function(t,n,e,r){for(var o=-1,i=null==t?0:t.length;++o<i;){var a=t[o];n(r,a,e(a),t)}return r}},5510:(t,n,e)=>{var r=e(6123);t.exports=function(t,n){return!(null==t||!t.length)&&r(t,n,0)>-1}},9955:t=>{t.exports=function(t,n,e){for(var r=-1,o=null==t?0:t.length;++r<o;)if(e(n,t[r]))return!0;return!1}},8644:t=>{t.exports=function(t,n){for(var e=-1,r=null==t?0:t.length,o=Array(r);++e<r;)o[e]=n(t[e],e,t);return o}},9559:t=>{t.exports=function(t,n){for(var e=-1,r=n.length,o=t.length;++e<r;)t[o+e]=n[e];return t}},1634:t=>{t.exports=function(t,n){for(var e=-1,r=null==t?0:t.length;++e<r;)if(n(t[e],e,t))return!0;return!1}},4132:(t,n,e)=>{var r=e(8347),o=e(788),i=Object.prototype.hasOwnProperty;t.exports=function(t,n,e){var a=t[n];i.call(t,n)&&o(a,e)&&(void 0!==e||n in t)||r(t,n,e)}},3162:(t,n,e)=>{var r=e(788);t.exports=function(t,n){for(var e=t.length;e--;)if(r(t[e][0],n))return e;return-1}},4508:t=>{t.exports=function(t,n,e,r){for(var o=-1,i=null==t?0:t.length;++o<i;){var a=t[o];n(r,a,e(a),t)}return r}},8347:(t,n,e)=>{var r=e(5525);t.exports=function(t,n,e){"__proto__"==n&&r?r(t,n,{configurable:!0,enumerable:!0,value:e,writable:!0}):t[n]=e}},6457:(t,n,e)=>{var r=e(9559),o=e(3608);t.exports=function t(n,e,i,a,u){var c=-1,l=n.length;for(i||(i=o),u||(u=[]);++c<l;){var s=n[c];e>0&&i(s)?e>1?t(s,e-1,i,a,u):r(u,s):a||(u[u.length]=s)}return u}},4432:(t,n,e)=>{var r=e(9026),o=e(3110);t.exports=function(t,n){for(var e=0,i=(n=r(n,t)).length;null!=t&&e<i;)t=t[o(n[e++])];return e&&e==i?t:void 0}},563:t=>{var n=Object.prototype.toString;t.exports=function(t){return n.call(t)}},6776:t=>{t.exports=function(t,n){return null!=t&&n in Object(t)}},6123:t=>{t.exports=function(t,n,e){for(var r=e-1,o=t.length;++r<o;)if(t[r]===n)return r;return-1}},7537:(t,n,e)=>{var r=e(563),o=e(3474);t.exports=function(t){return o(t)&&"[object Date]"==r(t)}},9070:(t,n,e)=>{var r=e(7028),o=e(3474);t.exports=function t(n,e,i,a,u){return n===e||(null==n||null==e||!o(n)&&!o(e)?n!=n&&e!=e:r(n,e,i,a,t,u))}},7028:(t,n,e)=>{var r=e(8708),o=e(8705),i=e(2813),a=e(6682),u=e(9667),c=e(163),l=e(4801),s=e(4289),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,n,e,g,v,y){var m=c(t),b=c(n),x=m?p:u(t),O=b?p:u(n),w=(x=x==f?h:x)==h,j=(O=O==f?h:O)==h,A=x==O;if(A&&l(t)){if(!l(n))return!1;m=!0,w=!1}if(A&&!w)return y||(y=new r),m||s(t)?o(t,n,e,g,v,y):i(t,n,x,e,g,v,y);if(!(1&e)){var M=w&&d.call(t,"__wrapped__"),k=j&&d.call(n,"__wrapped__");if(M||k){var E=M?t.value():t,S=k?n.value():n;return y||(y=new r),v(E,S,e,g,y)}}return!!A&&(y||(y=new r),a(t,n,e,g,v,y))}},9548:(t,n,e)=>{var r=e(8708),o=e(9070);t.exports=function(t,n,e,i){var a=e.length,u=a,c=!i;if(null==t)return!u;for(t=Object(t);a--;){var l=e[a];if(c&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<u;){var s=(l=e[a])[0],f=t[s],p=l[1];if(c&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new r;if(i)var d=i(f,p,s,t,n,h);if(!(void 0===d?o(p,f,3,i,h):d))return!1}}return!0}},3472:(t,n,e)=>{var r=e(758),o=e(5223),i=e(5346),a=e(163),u=e(8532);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):r(t):u(t)}},7190:(t,n,e)=>{var r=e(5125)(Object.keys,Object);t.exports=r},383:t=>{t.exports=function(t,n){for(var e=-1,r=null==t?0:t.length,o=Array(r);++e<r;)o[e]=n(t[e],e,t);return o}},758:(t,n,e)=>{var r=e(9548),o=e(597),i=e(6878);t.exports=function(t){var n=o(t);return 1==n.length&&n[0][2]?i(n[0][0],n[0][1]):function(e){return e===t||r(e,t,n)}}},5223:(t,n,e)=>{var r=e(9070),o=e(9201),i=e(5066),a=e(726),u=e(5385),c=e(6878),l=e(3110);t.exports=function(t,n){return a(t)&&u(n)?c(l(t),n):function(e){var a=o(e,t);return void 0===a&&a===n?i(e,t):r(n,a,3)}}},8245:(t,n,e)=>{var r=e(8644),o=e(4432),i=e(3472),a=e(383),u=e(9521),c=e(9110),l=e(9409),s=e(5346),f=e(163);t.exports=function(t,n,e){n=n.length?r(n,(function(t){return f(t)?function(n){return o(n,1===t.length?t[0]:t)}:t})):[s];var p=-1;n=r(n,c(i));var h=a(t,(function(t,e,o){return{criteria:r(n,(function(n){return n(t)})),index:++p,value:t}}));return u(h,(function(t,n){return l(t,n,e)}))}},5436:(t,n,e)=>{var r=e(6371),o=e(5066);t.exports=function(t,n){return r(t,n,(function(n,e){return o(t,e)}))}},6371:(t,n,e)=>{var r=e(4432),o=e(6539),i=e(9026);t.exports=function(t,n,e){for(var a=-1,u=n.length,c={};++a<u;){var l=n[a],s=r(t,l);e(s,l)&&o(c,i(l,t),s)}return c}},1600:t=>{t.exports=function(t){return function(n){return null==n?void 0:n[t]}}},3301:(t,n,e)=>{var r=e(4432);t.exports=function(t){return function(n){return r(n,t)}}},6317:(t,n,e)=>{var r=e(5346),o=e(4280),i=e(201);t.exports=function(t,n){return i(o(t,n,r),t+"")}},6539:(t,n,e)=>{var r=e(4132),o=e(9026),i=e(9099),a=e(7709),u=e(3110);t.exports=function(t,n,e,c){if(!a(t))return t;for(var l=-1,s=(n=o(n,t)).length,f=s-1,p=t;null!=p&&++l<s;){var h=u(n[l]),d=e;if("__proto__"===h||"constructor"===h||"prototype"===h)return t;if(l!=f){var g=p[h];void 0===(d=c?c(g,h,p):void 0)&&(d=a(g)?g:i(n[l+1])?[]:{})}r(p,h,d),p=p[h]}return t}},9521:t=>{t.exports=function(t,n){var e=t.length;for(t.sort(n);e--;)t[e]=t[e].value;return t}},7556:(t,n,e)=>{var r=e(788);t.exports=function(t,n){for(var e=-1,o=t.length,i=0,a=[];++e<o;){var u=t[e],c=n?n(u):u;if(!e||!r(c,l)){var l=c;a[i++]=0===u?0:u}}return a}},6316:(t,n,e)=>{var r=e(2596),o=e(8644),i=e(163),a=e(1995),u=r?r.prototype:void 0,c=u?u.toString:void 0;t.exports=function t(n){if("string"==typeof n)return n;if(i(n))return o(n,t)+"";if(a(n))return c?c.call(n):"";var e=n+"";return"0"==e&&1/n==-1/0?"-0":e}},9110:t=>{t.exports=function(t){return function(n){return t(n)}}},946:(t,n,e)=>{var r=e(3273),o=e(5510),i=e(9955),a=e(9914),u=e(6602),c=e(6226);t.exports=function(t,n,e){var l=-1,s=o,f=t.length,p=!0,h=[],d=h;if(e)p=!1,s=i;else if(f>=200){var g=n?null:u(t);if(g)return c(g);p=!1,s=a,d=new r}else d=n?[]:h;t:for(;++l<f;){var v=t[l],y=n?n(v):v;if(v=e||0!==v?v:0,p&&y==y){for(var m=d.length;m--;)if(d[m]===y)continue t;n&&d.push(y),h.push(v)}else s(d,y,e)||(d!==h&&d.push(y),h.push(v))}return h}},9914:(t,n,e)=>{var r=e(6123);t.exports=function(t,n){return!(null==t||!t.length)&&r(t,n,0)>-1}},9026:(t,n,e)=>{var r=e(163),o=e(726),i=e(7801),a=e(7010);t.exports=function(t,n){return r(t)?t:o(t,n)?[t]:i(a(t))}},522:(t,n,e)=>{var r=e(1995);t.exports=function(t,n){if(t!==n){var e=void 0!==t,o=null===t,i=t==t,a=r(t),u=void 0!==n,c=null===n,l=n==n,s=r(n);if(!c&&!s&&!a&&t>n||a&&u&&l&&!c&&!s||o&&u&&l||!e&&l||!i)return 1;if(!o&&!a&&!s&&t<n||s&&e&&i&&!o&&!a||c&&e&&i||!u&&i||!l)return-1}return 0}},9409:(t,n,e)=>{var r=e(522);t.exports=function(t,n,e){for(var o=-1,i=t.criteria,a=n.criteria,u=i.length,c=e.length;++o<u;){var l=r(i[o],a[o]);if(l)return o>=c?l:l*("desc"==e[o]?-1:1)}return t.index-n.index}},5659:(t,n,e)=>{var r=e(6649),o=e(4508),i=e(3472),a=e(163);t.exports=function(t,n){return function(e,u){var c=a(e)?r:o,l=n?n():{};return c(e,t,i(u,2),l)}}},6602:t=>{t.exports=function(){}},5525:(t,n,e)=>{var r=e(3743),o=function(){try{var t=r(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();t.exports=o},8705:(t,n,e)=>{var r=e(3273),o=e(1634),i=e(9914);t.exports=function(t,n,e,a,u,c){var l=1&e,s=t.length,f=n.length;if(s!=f&&!(l&&f>s))return!1;var p=c.get(t),h=c.get(n);if(p&&h)return p==n&&h==t;var d=-1,g=!0,v=2&e?new r:void 0;for(c.set(t,n),c.set(n,t);++d<s;){var y=t[d],m=n[d];if(a)var b=l?a(m,y,d,n,t,c):a(y,m,d,t,n,c);if(void 0!==b){if(b)continue;g=!1;break}if(v){if(!o(n,(function(t,n){if(!i(v,n)&&(y===t||u(y,t,e,a,c)))return v.push(n)}))){g=!1;break}}else if(y!==m&&!u(y,m,e,a,c)){g=!1;break}}return c.delete(t),c.delete(n),g}},2813:t=>{t.exports=function(t,n){return t===n||t!=t&&n!=n}},6682:(t,n,e)=>{var r=e(8143),o=Object.prototype.hasOwnProperty;t.exports=function(t,n,e,i,a,u){var c=1&e,l=r(t),s=l.length;if(s!=r(n).length&&!c)return!1;for(var f=s;f--;){var p=l[f];if(!(c?p in n:o.call(n,p)))return!1}var h=u.get(t),d=u.get(n);if(h&&d)return h==n&&d==t;var g=!0;u.set(t,n),u.set(n,t);for(var v=c;++f<s;){var y=t[p=l[f]],m=n[p];if(i)var b=c?i(m,y,p,n,t,u):i(y,m,p,t,n,u);if(!(void 0===b?y===m||a(y,m,e,i,u):b)){g=!1;break}v||(v="constructor"==p)}if(g&&!v){var x=t.constructor,O=n.constructor;x==O||!("constructor"in t)||!("constructor"in n)||"function"==typeof x&&x instanceof x&&"function"==typeof O&&O instanceof O||(g=!1)}return u.delete(t),u.delete(n),g}},5557:(t,n,e)=>{var r=e(2645),o=e(4280),i=e(201);t.exports=function(t){return i(o(t,void 0,r),t+"")}},2117:(t,n,e)=>{var r="object"==typeof e.g&&e.g&&e.g.Object===Object&&e.g;t.exports=r},8143:(t,n,e)=>{var r=e(5125)(Object.keys,Object);t.exports=r},7271:t=>{t.exports=function(t){var n=[];if(null!=t)for(var e in Object(t))n.push(e);return n}},597:(t,n,e)=>{var r=e(5385),o=e(7747);t.exports=function(t){for(var n=o(t),e=n.length;e--;){var i=n[e],a=t[i];n[e]=[i,a,r(a)]}return n}},3743:t=>{t.exports=function(t,n){return null==t?void 0:t[n]}},9817:(t,n,e)=>{var r=e(5125)(Object.getPrototypeOf,Object);t.exports=r},9667:t=>{var n=Object.prototype.toString;t.exports=function(t){return n.call(t)}},3096:(t,n,e)=>{var r=e(9026),o=e(5075),i=e(163),a=e(9099),u=e(8454),c=e(3110);t.exports=function(t,n,e){for(var l=-1,s=(n=r(n,t)).length,f=!1;++l<s;){var p=c(n[l]);if(!(f=null!=t&&e(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&u(s)&&a(p,s)&&(i(t)||o(t))}},3608:(t,n,e)=>{var r=e(2596),o=e(5075),i=e(163),a=r?r.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},9099:t=>{var n=/^(?:0|[1-9]\d*)$/;t.exports=function(t,e){var r=typeof t;return!!(e=null==e?9007199254740991:e)&&("number"==r||"symbol"!=r&&n.test(t))&&t>-1&&t%1==0&&t<e}},8286:t=>{t.exports=function(){return!1}},726:(t,n,e)=>{var r=e(163),o=e(1995),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,n){if(r(t))return!1;var e=typeof t;return!("number"!=e&&"symbol"!=e&&"boolean"!=e&&null!=t&&!o(t))||a.test(t)||!i.test(t)||null!=n&&t in Object(n)}},5782:t=>{t.exports=function(){return!1}},5385:(t,n,e)=>{var r=e(7709);t.exports=function(t){return t==t&&!r(t)}},1171:t=>{t.exports=function(){this.__data__=[],this.size=0}},7838:(t,n,e)=>{var r=e(3162),o=Array.prototype.splice;t.exports=function(t){var n=this.__data__,e=r(n,t);return!(e<0||(e==n.length-1?n.pop():o.call(n,e,1),--this.size,0))}},4859:(t,n,e)=>{var r=e(3162);t.exports=function(t){var n=this.__data__,e=r(n,t);return e<0?void 0:n[e][1]}},4073:(t,n,e)=>{var r=e(3162);t.exports=function(t){return r(this.__data__,t)>-1}},8541:(t,n,e)=>{var r=e(3162);t.exports=function(t,n){var e=this.__data__,o=r(e,t);return o<0?(++this.size,e.push([t,n])):e[o][1]=n,this}},6878:t=>{t.exports=function(t,n){return function(e){return null!=e&&e[t]===n&&(void 0!==n||t in Object(e))}}},2453:t=>{t.exports=function(t){return t}},2348:(t,n,e)=>{t=e.nmd(t);var r=e(2117),o=n&&!n.nodeType&&n,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&r.process,u=function(){try{return i&&i.require&&i.require("util").types||a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=u},5125:t=>{t.exports=function(t,n){return function(e){return t(n(e))}}},4280:(t,n,e)=>{var r=e(6082),o=Math.max;t.exports=function(t,n,e){return n=o(void 0===n?t.length-1:n,0),function(){for(var i=arguments,a=-1,u=o(i.length-n,0),c=Array(u);++a<u;)c[a]=i[n+a];a=-1;for(var l=Array(n+1);++a<n;)l[a]=i[a];return l[n]=e(c),r(t,this,l)}}},2373:(t,n,e)=>{var r=e(2117),o="object"==typeof self&&self&&self.Object===Object&&self,i=r||o||Function("return this")();t.exports=i},6226:t=>{t.exports=function(){return[]}},201:t=>{t.exports=function(t){return t}},7801:(t,n,e)=>{var r=e(2453),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=r((function(t){var n=[];return 46===t.charCodeAt(0)&&n.push(""),t.replace(o,(function(t,e,r,o){n.push(r?o.replace(i,"$1"):e||t)})),n}));t.exports=a},3110:t=>{t.exports=function(t){return t}},1893:(t,n,e)=>{var r=e(6317),o=e(788),i=e(8286),a=e(8855),u=Object.prototype,c=u.hasOwnProperty,l=r((function(t,n){t=Object(t);var e=-1,r=n.length,l=r>2?n[2]:void 0;for(l&&i(n[0],n[1],l)&&(r=1);++e<r;)for(var s=n[e],f=a(s),p=-1,h=f.length;++p<h;){var d=f[p],g=t[d];(void 0===g||o(g,u[d])&&!c.call(t,d))&&(t[d]=s[d])}return t}));t.exports=l},788:t=>{t.exports=function(t,n){return t===n||t!=t&&n!=n}},2645:(t,n,e)=>{var r=e(6457);t.exports=function(t){return null!=t&&t.length?r(t,1):[]}},7314:t=>{t.exports=function(t){for(var n=-1,e=null==t?0:t.length,r={};++n<e;){var o=t[n];r[o[0]]=o[1]}return r}},9201:(t,n,e)=>{var r=e(4432);t.exports=function(t,n,e){var o=null==t?void 0:r(t,n);return void 0===o?e:o}},9056:(t,n,e)=>{var r=e(8347),o=e(5659),i=Object.prototype.hasOwnProperty,a=o((function(t,n,e){i.call(t,e)?t[e].push(n):r(t,e,[n])}));t.exports=a},5066:(t,n,e)=>{var r=e(6776),o=e(3096);t.exports=function(t,n){return null!=t&&o(t,n,r)}},5346:t=>{t.exports=function(t){return t}},5075:t=>{t.exports=function(){return!1}},163:t=>{var n=Array.isArray;t.exports=n},981:(t,n,e)=>{var r=e(9642),o=e(8454);t.exports=function(t){return null!=t&&o(t.length)&&!r(t)}},4801:t=>{t.exports=function(){return!1}},4295:(t,n,e)=>{var r=e(7537),o=e(9110),i=e(2348),a=i&&i.isDate,u=a?o(a):r;t.exports=u},4155:(t,n,e)=>{var r=e(7190),o=e(9667),i=e(5075),a=e(163),u=e(981),c=e(4801),l=e(5782),s=e(4289),f=Object.prototype.hasOwnProperty;t.exports=function(t){if(null==t)return!0;if(u(t)&&(a(t)||"string"==typeof t||"function"==typeof t.splice||c(t)||s(t)||i(t)))return!t.length;var n=o(t);if("[object Map]"==n||"[object Set]"==n)return!t.size;if(l(t))return!r(t).length;for(var e in t)if(f.call(t,e))return!1;return!0}},8254:(t,n,e)=>{var r=e(9070);t.exports=function(t,n){return r(t,n)}},9642:(t,n,e)=>{var r=e(563),o=e(7709);t.exports=function(t){if(!o(t))return!1;var n=r(t);return"[object Function]"==n||"[object GeneratorFunction]"==n||"[object AsyncFunction]"==n||"[object Proxy]"==n}},8454:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},7709:t=>{t.exports=function(t){var n=typeof t;return null!=t&&("object"==n||"function"==n)}},3474:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},3849:(t,n,e)=>{var r=e(563),o=e(9817),i=e(3474),a=Function.prototype,u=Object.prototype,c=a.toString,l=u.hasOwnProperty,s=c.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=r(t))return!1;var n=o(t);if(null===n)return!0;var e=l.call(n,"constructor")&&n.constructor;return"function"==typeof e&&e instanceof e&&c.call(e)==s}},1995:t=>{t.exports=function(){return!1}},4289:t=>{t.exports=function(){return!1}},8999:t=>{t.exports=function(t){return void 0===t}},7747:(t,n,e)=>{var r=e(5125)(Object.keys,Object);t.exports=r},8855:t=>{t.exports=function(t){var n=[];if(null!=t)for(var e in Object(t))n.push(e);return n}},6051:t=>{t.exports=function(t){if("function"!=typeof t)throw new TypeError("Expected a function");return function(){var n=arguments;switch(n.length){case 0:return!t.call(this);case 1:return!t.call(this,n[0]);case 2:return!t.call(this,n[0],n[1]);case 3:return!t.call(this,n[0],n[1],n[2])}return!t.apply(this,n)}}},1616:(t,n,e)=>{var r=e(3472),o=e(6051),i=e(4912);t.exports=function(t,n){return i(t,o(r(n)))}},4143:(t,n,e)=>{var r=e(8245),o=e(163);t.exports=function(t,n,e,i){return null==t?[]:(o(n)||(n=null==n?[]:[n]),o(e=i?void 0:e)||(e=null==e?[]:[e]),r(t,n,e))}},9082:(t,n,e)=>{var r=e(5436),o=e(5557)((function(t,n){return null==t?{}:r(t,n)}));t.exports=o},4912:(t,n,e)=>{var r=e(8644),o=e(3472),i=e(6371),a=e(7271);t.exports=function(t,n){if(null==t)return{};var e=r(a(t),(function(t){return[t]}));return n=o(n),i(t,e,(function(t,e){return n(t,e[0])}))}},8532:(t,n,e)=>{var r=e(1600),o=e(3301),i=e(726),a=e(3110);t.exports=function(t){return i(t)?r(a(t)):o(t)}},2829:(t,n,e)=>{var r=e(7556);t.exports=function(t){return t&&t.length?r(t):[]}},7010:(t,n,e)=>{var r=e(6316);t.exports=function(t){return null==t?"":r(t)}},9602:(t,n,e)=>{var r=e(946);t.exports=function(t){return t&&t.length?r(t):[]}},8961:(t,n,e)=>{var r=e(3472),o=e(946);t.exports=function(t,n){return t&&t.length?o(t,r(n,2)):[]}},660:(t,n,e)=>{var r=e(7010),o=0;t.exports=function(t){var n=++o;return r(t)+n}},7491:t=>{var n="undefined"!=typeof Element,e="function"==typeof Map,r="function"==typeof Set,o="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;function i(t,a){if(t===a)return!0;if(t&&a&&"object"==typeof t&&"object"==typeof a){if(t.constructor!==a.constructor)return!1;var u,c,l,s;if(Array.isArray(t)){if((u=t.length)!=a.length)return!1;for(c=u;0!=c--;)if(!i(t[c],a[c]))return!1;return!0}if(e&&t instanceof Map&&a instanceof Map){if(t.size!==a.size)return!1;for(s=t.entries();!(c=s.next()).done;)if(!a.has(c.value[0]))return!1;for(s=t.entries();!(c=s.next()).done;)if(!i(c.value[1],a.get(c.value[0])))return!1;return!0}if(r&&t instanceof Set&&a instanceof Set){if(t.size!==a.size)return!1;for(s=t.entries();!(c=s.next()).done;)if(!a.has(c.value[0]))return!1;return!0}if(o&&ArrayBuffer.isView(t)&&ArrayBuffer.isView(a)){if((u=t.length)!=a.length)return!1;for(c=u;0!=c--;)if(t[c]!==a[c])return!1;return!0}if(t.constructor===RegExp)return t.source===a.source&&t.flags===a.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===a.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===a.toString();if((u=(l=Object.keys(t)).length)!==Object.keys(a).length)return!1;for(c=u;0!=c--;)if(!Object.prototype.hasOwnProperty.call(a,l[c]))return!1;if(n&&t instanceof Element)return!1;for(c=u;0!=c--;)if(("_owner"!==l[c]&&"__v"!==l[c]&&"__o"!==l[c]||!t.$$typeof)&&!i(t[l[c]],a[l[c]]))return!1;return!0}return t!=t&&a!=a}t.exports=function(t,n){try{return i(t,n)}catch(t){if((t.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw t}}},9787:n=>{"use strict";n.exports=t}},e={};function r(t){var o=e[t];if(void 0!==o)return o.exports;var i=e[t]={id:t,loaded:!1,exports:{}};return n[t](i,i.exports,r),i.loaded=!0,i.exports}r.n=t=>{var n=t&&t.__esModule?()=>t.default:()=>t;return r.d(n,{a:n}),n},r.d=(t,n)=>{for(var e in n)r.o(n,e)&&!r.o(t,e)&&Object.defineProperty(t,e,{enumerable:!0,get:n[e]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=(t,n)=>Object.prototype.hasOwnProperty.call(t,n),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.nmd=t=>(t.paths=[],t.children||(t.children=[]),t);var o={};return(()=>{"use strict";r.r(o),r.d(o,{VictoryStack:()=>mc});var t={};r.r(t),r.d(t,{scaleBand:()=>kn,scaleDiverging:()=>Da,scaleDivergingLog:()=>Ca,scaleDivergingPow:()=>Pa,scaleDivergingSqrt:()=>Ta,scaleDivergingSymlog:()=>_a,scaleIdentity:()=>fr,scaleImplicit:()=>An,scaleLinear:()=>sr,scaleLog:()=>xr,scaleOrdinal:()=>Mn,scalePoint:()=>Sn,scalePow:()=>Dr,scaleQuantile:()=>$r,scaleQuantize:()=>Br,scaleRadial:()=>Tr,scaleSequential:()=>Oa,scaleSequentialLog:()=>wa,scaleSequentialPow:()=>Aa,scaleSequentialQuantile:()=>ka,scaleSequentialSqrt:()=>Ma,scaleSequentialSymlog:()=>ja,scaleSqrt:()=>Cr,scaleSymlog:()=>Ar,scaleThreshold:()=>qr,scaleTime:()=>ya,scaleUtc:()=>ma,tickFormat:()=>cr});var n=r(4155),e=r.n(n),i=r(1893),a=r.n(i),u=r(9787),c=r.n(u),l=r(7709),s=r.n(l),f=r(660),p=r.n(f);function h(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function d(t,n){return d=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},d(t,n)}function g(t,n){if(n&&("object"==typeof n||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function v(t){return v=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},v(t)}var y=function(t){!function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),n&&d(t,n)}(a,t);var n,e,r,o,i=(r=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,n=v(r);if(o){var e=v(this).constructor;t=Reflect.construct(n,arguments,e)}else t=n.apply(this,arguments);return g(this,t)});function a(t){var n;return function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,a),(n=i.call(this,t)).map=void 0,n.index=void 0,n.portalRegister=function(){return++n.index},n.portalUpdate=function(t,e){n.map[t]=e,n.forceUpdate()},n.portalDeregister=function(t){delete n.map[t],n.forceUpdate()},n.map={},n.index=1,n}return n=a,(e=[{key:"getChildren",value:function(){var t=this;return Object.keys(this.map).map((function(n){var e=t.map[n];return e?c().cloneElement(e,{key:n}):e}))}},{key:"render",value:function(){return c().createElement("svg",this.props,this.getChildren())}}])&&h(n.prototype,e),Object.defineProperty(n,"prototype",{writable:!1}),a}(c().Component);y.displayName="Portal";var m=c().createContext({});m.displayName="PortalContext";var b,x,O=0,w=0,j=0,A=0,M=0,k=0,E="object"==typeof performance&&performance.now?performance:Date,S="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function D(){return M||(S(C),M=E.now()+k)}function C(){M=0}function _(){this._call=this._time=this._next=null}function P(t,n,e){var r=new _;return r.restart(t,n,e),r}function T(){M=(A=E.now())+k,O=w=0;try{!function(){D(),++O;for(var t,n=b;n;)(t=M-n._time)>=0&&n._call.call(void 0,t),n=n._next;--O}()}finally{O=0,function(){for(var t,n,e=b,r=1/0;e;)e._call?(r>e._time&&(r=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:b=n);x=t,U(r)}(),M=0}}function N(){var t=E.now(),n=t-A;n>1e3&&(k-=n,A=t)}function U(t){O||(w&&(w=clearTimeout(w)),t-M>24?(t<1/0&&(w=setTimeout(T,t-E.now()-k)),j&&(j=clearInterval(j))):(j||(A=E.now(),j=setInterval(N,1e3)),O=1,S(T)))}function F(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}_.prototype=P.prototype={constructor:_,restart:function(t,n,e){if("function"!=typeof t)throw new TypeError("callback is not a function");e=(null==e?D():+e)+(null==n?0:+n),this._next||x===this||(x?x._next=this:b=this,x=this),this._call=t,this._time=e,U()},stop:function(){this._call&&(this._call=null,this._time=1/0,U())}};var L=function(){function t(){var n=this;!function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,t),this.shouldAnimate=void 0,this.subscribers=void 0,this.activeSubscriptions=void 0,this.timer=void 0,this.loop=function(){n.subscribers.forEach((function(t){t.callback(D()-t.startTime,t.duration)}))},this.shouldAnimate=!0,this.subscribers=[],this.timer=null,this.activeSubscriptions=0}var n,e;return n=t,(e=[{key:"bypassAnimation",value:function(){this.shouldAnimate=!1}},{key:"resumeAnimation",value:function(){this.shouldAnimate=!0}},{key:"start",value:function(){this.timer||(this.timer=P(this.loop))}},{key:"stop",value:function(){this.timer&&(this.timer.stop(),this.timer=null)}},{key:"subscribe",value:function(t,n){var e=this.subscribers.push({startTime:D(),callback:t,duration:this.shouldAnimate?n:0});return this.activeSubscriptions++,this.start(),e}},{key:"unsubscribe",value:function(t){null!==t&&this.subscribers[t-1]&&(delete this.subscribers[t-1],this.activeSubscriptions--),0===this.activeSubscriptions&&this.stop()}}])&&F(n.prototype,e),Object.defineProperty(n,"prototype",{writable:!1}),t}(),I=c().createContext({transitionTimer:new L,animationTimer:new L});I.displayName="TimerContext";const R=I;var W=r(9082),$=r.n(W),B=r(8532),q=r.n(B);function z(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],e={};for(var r in t)n.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}function H(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"padding",e=t[n],r="number"==typeof e?e:0,o="object"==typeof e?e:{};return{top:o.top||r,bottom:o.bottom||r,left:o.left||r,right:o.right||r}}function Y(t,n){return Z(t)?t(n):t}function G(t){return"number"==typeof t?t*(Math.PI/180):t}function K(t){var n=H(t),e=n.left,r=n.right,o=n.top,i=n.bottom,a=t.width,u=t.height;return Math.min(a-e-r,u-o-i)/2}function V(t,n){return t.range&&t.range[n]?t.range[n]:t.range&&Array.isArray(t.range)?t.range:t.polar?function(t,n){return"x"===n?[G(t.startAngle||0),G(t.endAngle||360)]:[t.innerRadius||0,K(t)]}(t,n):function(t,n){var e="x"!==n,r=H(t);return e?[t.height-r.bottom,r.top]:[r.left,t.width-r.right]}(t,n)}function Z(t){return"function"==typeof t}function Q(t){return Z(t)?t:null==t?function(t){return t}:q()(t)}function X(t,n,e){var r=z(t.theme&&t.theme[e]?t.theme[e]:{},["style"]),o=function(t){if(void 0!==t.horizontal||!t.children)return t.horizontal;var n=function(t){return t.reduce((function(t,e){var r=e.props||{};return t||r.horizontal||!r.children?t||r.horizontal:n(c().Children.toArray(r.children))}),!1)};return n(c().Children.toArray(t.children))}(t),i=void 0===o?{}:{horizontal:o};return a()(i,t,r,n)}function J(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:function(t,n){return t.concat(n)},i=["data","domain","categories","polar","startAngle","endAngle","minDomain","maxDomain","horizontal"],a=function(t,u,l){return t.reduce((function(t,r,s){var f=t,p=r.type&&r.type.role,h=r.props.name||"".concat(p,"-").concat(u[s]);if(r.props&&r.props.children){var d=Object.assign({},r.props,$()(e,i)),g=r.type&&"stack"===r.type.role&&Z(r.type.getChildren)?r.type.getChildren(d):c().Children.toArray(r.props.children).map((function(t){var n=Object.assign({},t.props,$()(d,i));return c().cloneElement(t,n)})),v=g.map((function(t,n){return"".concat(h,"-").concat(n)})),y=a(g,v,r);f=o(f,y)}else{var m=n(r,h,l);m&&(f=o(f,m))}return f}),r)},l=t.filter(u.isValidElement),s=l.map((function(t,n){return n}));return a(l,s)}function tt(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,o,i=[],a=!0,u=!1;try{for(e=e.call(t);!(a=(r=e.next()).done)&&(i.push(r.value),!n||i.length!==n);a=!0);}catch(t){u=!0,o=t}finally{try{a||null==e.return||e.return()}finally{if(u)throw o}}return i}}(t,n)||function(t,n){if(t){if("string"==typeof t)return nt(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?nt(t,n):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nt(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function et(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),e.push.apply(e,r)}return e}function rt(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}var ot={startsWith:["data-","aria-"],exactMatch:[]},it=function(t){var n=function(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?et(Object(e),!0).forEach((function(n){rt(t,n,e[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):et(Object(e)).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}))}return t}({},t);return Object.fromEntries(Object.entries(n).filter((function(t){return function(t){return!(!function(t){var n=!1;return ot.startsWith.forEach((function(e){new RegExp("\\b(".concat(e,")(\\w|-)+"),"g").test(t)&&(n=!0)})),n}(t)&&!function(t){return ot.exactMatch.includes(t)}(t))}(tt(t,1)[0])})).map((function(n){var e=tt(n,2);return[e[0],Y(e[1],t)]})))};function at(){return at=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var e=arguments[n];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}return t},at.apply(this,arguments)}function ut(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),e.push.apply(e,r)}return e}function ct(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?ut(Object(e),!0).forEach((function(n){lt(t,n,e[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):ut(Object(e)).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}))}return t}function lt(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function st(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function ft(t,n){return ft=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},ft(t,n)}function pt(t,n){if(n&&("object"==typeof n||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function ht(t){return ht=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ht(t)}var dt=function(t){!function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),n&&ft(t,n)}(u,t);var n,e,r,o,i=(r=u,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,n=ht(r);if(o){var e=ht(this).constructor;t=Reflect.construct(n,arguments,e)}else t=n.apply(this,arguments);return pt(this,t)});function u(t){var n;return function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,u),(n=i.call(this,t)).containerId=void 0,n.portalRef=void 0,n.containerRef=void 0,n.shouldHandleWheel=void 0,n.savePortalRef=function(t){return n.portalRef=t,t},n.portalUpdate=function(t,e){return n.portalRef.portalUpdate(t,e)},n.portalRegister=function(){return n.portalRef.portalRegister()},n.portalDeregister=function(t){return n.portalRef.portalDeregister(t)},n.saveContainerRef=function(t){return Z(n.props.containerRef)&&n.props.containerRef(t),n.containerRef=t,t},n.handleWheel=function(t){return t.preventDefault()},n.containerId=s()(t)&&void 0!==t.containerId?t.containerId:p()("victory-container-"),n.shouldHandleWheel=!!(t&&t.events&&t.events.onWheel),n}return n=u,(e=[{key:"componentDidMount",value:function(){this.shouldHandleWheel&&this.containerRef&&this.containerRef.addEventListener("wheel",this.handleWheel)}},{key:"componentWillUnmount",value:function(){this.shouldHandleWheel&&this.containerRef&&this.containerRef.removeEventListener("wheel",this.handleWheel)}},{key:"getIdForElement",value:function(t){return"".concat(this.containerId,"-").concat(t)}},{key:"getChildren",value:function(t){return t.children}},{key:"getOUIAProps",value:function(t){var n=t.ouiaId,e=t.ouiaSafe,r=t.ouiaType;return ct(ct(ct({},n&&{"data-ouia-component-id":n}),r&&{"data-ouia-component-type":r}),void 0!==e&&{"data-ouia-safe":e})}},{key:"renderContainer",value:function(t,n,e){var r=t.title,o=t.desc,i=t.portalComponent,u=t.className,l=t.width,s=t.height,f=t.portalZIndex,p=t.responsive,h=this.getChildren(t),d=p?{width:"100%",height:"100%"}:{width:l,height:s},g=Object.assign({pointerEvents:"none",touchAction:"none",position:"relative"},d),v=Object.assign({zIndex:f,position:"absolute",top:0,left:0},d),y=Object.assign({pointerEvents:"all"},d),b=Object.assign({overflow:"visible"},d),x={width:l,height:s,viewBox:n.viewBox,preserveAspectRatio:n.preserveAspectRatio,style:b};return c().createElement(m.Provider,{value:{portalUpdate:this.portalUpdate,portalRegister:this.portalRegister,portalDeregister:this.portalDeregister}},c().createElement("div",at({style:a()({},e,g),className:u,ref:this.saveContainerRef},this.getOUIAProps(t)),c().createElement("svg",at({},n,{style:y}),r?c().createElement("title",{id:this.getIdForElement("title")},r):null,o?c().createElement("desc",{id:this.getIdForElement("desc")},o):null,h),c().createElement("div",{style:v},c().cloneElement(i,ct(ct({},x),{},{ref:this.savePortalRef})))))}},{key:"render",value:function(){var t=this.props,n=t.width,e=t.height,r=t.responsive,o=t.events,i=t.title,a=t.desc,u=t.tabIndex,c=t.preserveAspectRatio,l=t.role,s=r?this.props.style:z(this.props.style,["height","width"]),f=it(this.props),p=Object.assign(ct({width:n,height:e,tabIndex:u,role:l,"aria-labelledby":[i&&this.getIdForElement("title"),this.props["aria-labelledby"]].filter(Boolean).join(" ")||void 0,"aria-describedby":[a&&this.getIdForElement("desc"),this.props["aria-describedby"]].filter(Boolean).join(" ")||void 0,viewBox:r?"0 0 ".concat(n," ").concat(e):void 0,preserveAspectRatio:r?c:void 0},f),o);return this.renderContainer(this.props,p,s)}}])&&st(n.prototype,e),Object.defineProperty(n,"prototype",{writable:!1}),u}(c().Component);dt.displayName="VictoryContainer",dt.role="container",dt.defaultProps={className:"VictoryContainer",portalComponent:c().createElement(y,null),portalZIndex:99,responsive:!0,role:"img"},dt.contextType=R;var gt=["#252525","#525252","#737373","#969696","#bdbdbd","#d9d9d9","#f0f0f0"],vt="#252525",yt="#969696",mt={width:450,height:300,padding:50,colorScale:gt},bt={fontFamily:"'Gill Sans', 'Seravek', 'Trebuchet MS', sans-serif",fontSize:14,letterSpacing:"normal",padding:10,fill:vt,stroke:"transparent"},xt=Object.assign({textAnchor:"middle"},bt),Ot={area:Object.assign({style:{data:{fill:vt},labels:bt}},mt),axis:Object.assign({style:{axis:{fill:"transparent",stroke:vt,strokeWidth:1,strokeLinecap:"round",strokeLinejoin:"round"},axisLabel:Object.assign({},xt,{padding:25}),grid:{fill:"none",stroke:"none",pointerEvents:"painted"},ticks:{fill:"transparent",size:1,stroke:"transparent"},tickLabels:bt}},mt),bar:Object.assign({style:{data:{fill:vt,padding:8,strokeWidth:0},labels:bt}},mt),boxplot:Object.assign({style:{max:{padding:8,stroke:vt,strokeWidth:1},maxLabels:Object.assign({},bt,{padding:3}),median:{padding:8,stroke:vt,strokeWidth:1},medianLabels:Object.assign({},bt,{padding:3}),min:{padding:8,stroke:vt,strokeWidth:1},minLabels:Object.assign({},bt,{padding:3}),q1:{padding:8,fill:yt},q1Labels:Object.assign({},bt,{padding:3}),q3:{padding:8,fill:yt},q3Labels:Object.assign({},bt,{padding:3})},boxWidth:20},mt),candlestick:Object.assign({style:{data:{stroke:vt,strokeWidth:1},labels:Object.assign({},bt,{padding:5})},candleColors:{positive:"#ffffff",negative:vt}},mt),chart:mt,errorbar:Object.assign({borderWidth:8,style:{data:{fill:"transparent",stroke:vt,strokeWidth:2},labels:bt}},mt),group:Object.assign({colorScale:gt},mt),histogram:Object.assign({style:{data:{fill:yt,stroke:vt,strokeWidth:2},labels:bt}},mt),legend:{colorScale:gt,gutter:10,orientation:"vertical",titleOrientation:"top",style:{data:{type:"circle"},labels:bt,title:Object.assign({},bt,{padding:5})}},line:Object.assign({style:{data:{fill:"transparent",stroke:vt,strokeWidth:2},labels:bt}},mt),pie:{style:{data:{padding:10,stroke:"transparent",strokeWidth:1},labels:Object.assign({},bt,{padding:20})},colorScale:gt,width:400,height:400,padding:50},scatter:Object.assign({style:{data:{fill:vt,stroke:"transparent",strokeWidth:0},labels:bt}},mt),stack:Object.assign({colorScale:gt},mt),tooltip:{style:Object.assign({},bt,{padding:0,pointerEvents:"none"}),flyoutStyle:{stroke:vt,strokeWidth:1,fill:"#f0f0f0",pointerEvents:"none"},flyoutPadding:5,cornerRadius:5,pointerLength:10},voronoi:Object.assign({style:{data:{fill:"transparent",stroke:"transparent",strokeWidth:0},labels:Object.assign({},bt,{padding:5,pointerEvents:"none"}),flyout:{stroke:vt,strokeWidth:1,fill:"#f0f0f0",pointerEvents:"none"}}},mt)},wt=["#F4511E","#FFF59D","#DCE775","#8BC34A","#00796B","#006064"],jt="#ECEFF1",At="#90A4AE",Mt="#455A64",kt="#212121",Et={width:350,height:350,padding:50},St={fontFamily:"'Helvetica Neue', 'Helvetica', sans-serif",fontSize:12,letterSpacing:"normal",padding:8,fill:Mt,stroke:"transparent",strokeWidth:0},Dt=Object.assign({textAnchor:"middle"},St),Ct="round",_t="round",Pt={grayscale:Ot,material:{area:Object.assign({style:{data:{fill:kt},labels:St}},Et),axis:Object.assign({style:{axis:{fill:"transparent",stroke:At,strokeWidth:2,strokeLinecap:Ct,strokeLinejoin:_t},axisLabel:Object.assign({},Dt,{padding:8,stroke:"transparent"}),grid:{fill:"none",stroke:jt,strokeDasharray:"10, 5",strokeLinecap:Ct,strokeLinejoin:_t,pointerEvents:"painted"},ticks:{fill:"transparent",size:5,stroke:At,strokeWidth:1,strokeLinecap:Ct,strokeLinejoin:_t},tickLabels:Object.assign({},St,{fill:Mt})}},Et),polarDependentAxis:Object.assign({style:{ticks:{fill:"transparent",size:1,stroke:"transparent"}}}),bar:Object.assign({style:{data:{fill:Mt,padding:8,strokeWidth:0},labels:St}},Et),boxplot:Object.assign({style:{max:{padding:8,stroke:Mt,strokeWidth:1},maxLabels:Object.assign({},St,{padding:3}),median:{padding:8,stroke:Mt,strokeWidth:1},medianLabels:Object.assign({},St,{padding:3}),min:{padding:8,stroke:Mt,strokeWidth:1},minLabels:Object.assign({},St,{padding:3}),q1:{padding:8,fill:Mt},q1Labels:Object.assign({},St,{padding:3}),q3:{padding:8,fill:Mt},q3Labels:Object.assign({},St,{padding:3})},boxWidth:20},Et),candlestick:Object.assign({style:{data:{stroke:Mt},labels:Object.assign({},St,{padding:5})},candleColors:{positive:"#ffffff",negative:Mt}},Et),chart:Et,errorbar:Object.assign({borderWidth:8,style:{data:{fill:"transparent",opacity:1,stroke:Mt,strokeWidth:2},labels:St}},Et),group:Object.assign({colorScale:wt},Et),histogram:Object.assign({style:{data:{fill:Mt,stroke:kt,strokeWidth:2},labels:St}},Et),legend:{colorScale:wt,gutter:10,orientation:"vertical",titleOrientation:"top",style:{data:{type:"circle"},labels:St,title:Object.assign({},St,{padding:5})}},line:Object.assign({style:{data:{fill:"transparent",opacity:1,stroke:Mt,strokeWidth:2},labels:St}},Et),pie:Object.assign({colorScale:wt,style:{data:{padding:8,stroke:jt,strokeWidth:1},labels:Object.assign({},St,{padding:20})}},Et),scatter:Object.assign({style:{data:{fill:Mt,opacity:1,stroke:"transparent",strokeWidth:0},labels:St}},Et),stack:Object.assign({colorScale:wt},Et),tooltip:{style:Object.assign({},St,{padding:0,pointerEvents:"none"}),flyoutStyle:{stroke:kt,strokeWidth:1,fill:"#f0f0f0",pointerEvents:"none"},flyoutPadding:5,cornerRadius:5,pointerLength:10},voronoi:Object.assign({style:{data:{fill:"transparent",stroke:"transparent",strokeWidth:0},labels:Object.assign({},St,{padding:5,pointerEvents:"none"}),flyout:{stroke:kt,strokeWidth:1,fill:"#f0f0f0",pointerEvents:"none"}}},Et)}};function Tt(t){return function(t){if(Array.isArray(t))return Nt(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(t){if("string"==typeof t)return Nt(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Nt(t,n):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Nt(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function Ut(t){return Array.isArray(t)&&t.some((function(t){return t instanceof Date}))}function Ft(t,n){return t&&n?t.filter((function(t){return!n.includes(t)})):[]}function Lt(t){return function(t){return Array.isArray(t)&&t.length>0}(t)&&t.every(Array.isArray)}function It(t){return t.filter((function(t){return void 0!==t}))}function Rt(t){for(var n=arguments.length,e=new Array(n>1?n-1:0),r=1;r<n;r++)e[r-1]=arguments[r];var o=t.concat(e);return Ut(o)?new Date(Math.max.apply(Math,Tt(o))):Math.max.apply(Math,Tt(o))}function Wt(t){for(var n=arguments.length,e=new Array(n>1?n-1:0),r=1;r<n;r++)e[r-1]=arguments[r];var o=t.concat(e);return Ut(o)?new Date(Math.min.apply(Math,Tt(o))):Math.min.apply(Math,Tt(o))}var $t=r(5346),Bt=r.n($t);function qt(t,n){return(t.key||n).toString()}function zt(t){return t.reduce((function(t,n,e){return t[qt(n,e)]=n,t}),{})}function Ht(t,n){var e=!1,r=Object.keys(t).reduce((function(t,r){return r in n||(e=!0,t[r]=!0),t}),{});return e&&r}function Yt(t){return t.type&&t.type.getData?t.type.getData(t.props):t.props&&t.props.data||!1}function Gt(t,n){var e=!1,r=!1,o=function(t,n){return t.map((function(i,a){return i&&i.props&&i.props.children&&n[a]?o(c().Children.toArray(t[a].props.children),c().Children.toArray(n[a].props.children)):function(t,n){if(!n||t.type!==n.type)return{};var o,i,a,u,c=(o=Yt(t),i=Yt(n),a=o&&zt(o),u=i&&zt(i),{entering:a&&Ht(u,a),exiting:u&&Ht(a,u)}||{}),l=c.entering,s=c.exiting;return e=e||!!s,r=r||!!l,{entering:l||!1,exiting:s||!1}}(i,n[a])}))},i=o(c().Children.toArray(t),c().Children.toArray(n));return{nodesWillExit:e,nodesWillEnter:r,childrenTransitions:i,nodesShouldEnter:!1}}function Kt(t,n,e){var r=n&&n.nodesWillExit,o=n&&n.nodesWillEnter,i=n&&n.nodesShouldEnter,u=n&&n.nodesShouldLoad,c=n&&n.nodesDoneLoad,l=n&&n.childrenTransitions||[],s={enter:t.animate&&t.animate.onEnter&&t.animate.onEnter.duration,exit:t.animate&&t.animate.onExit&&t.animate.onExit.duration,load:t.animate&&t.animate.onLoad&&t.animate.onLoad.duration,move:t.animate&&t.animate.duration},f=function(t,n,r){return u?function(t,n,r){var o=Object.assign({},t,{onEnd:function(){e({nodesShouldLoad:!1,nodesDoneLoad:!0})}});if(o&&o.onLoad&&!o.onLoad.duration)return{animate:t,data:n};var i=t.onLoad&&t.onLoad.after?t.onLoad.after:Bt();return{animate:o,data:n.map((function(t,e){return Object.assign({},t,i(t,e,n))}))}}(r,n):function(t,n,r,o){var i=Object.assign({},t,{onEnd:function(){e({nodesDoneLoad:!0})}});if(i&&i.onLoad&&!i.onLoad.duration)return{animate:i,data:r};var a=i.onLoad&&i.onLoad.before?i.onLoad.before:Bt();return{animate:i,data:r.map((function(t,n){return Object.assign({},t,a(t,n,r))})),clipWidth:0}}(r,0,n)},p=function(t,n,r,o){return function(t,n,r,o,i){var a=t&&t.onExit,u=Object.assign({},t,a),c=r;if(o){t.onEnd=function(){e({nodesWillExit:!1})};var l=t.onExit&&t.onExit.before?t.onExit.before:Bt();c=r.map((function(t,n){var e=(t.key||n).toString();return o[e]?Object.assign({},t,l(t,n,r)):t}))}return{animate:u,data:c}}(o,0,r,t)},h=function(t,n,r,o){return i?function(t,n,r,o){var i=t&&t.onEnter,a=Object.assign({},t,i),u=n;if(r){a.onEnd=function(){e({nodesWillEnter:!1})};var c=a.onEnter&&a.onEnter.after?a.onEnter.after:Bt();u=n.map((function(t,e){var o=qt(t,e);return r[o]?Object.assign({},t,c(t,e,n)):t}))}return{animate:a,data:u}}(o,r,t):function(t,n,r,o,i){var a=t,u=r;if(o){a=Object.assign({},t,{onEnd:function(){e({nodesShouldEnter:!0})}});var c=t.onEnter&&t.onEnter.before?t.onEnter.before:Bt();u=r.map((function(t,n){var e=(t.key||n).toString();return o[e]?Object.assign({},t,c(t,n,r)):t}))}return{animate:a,data:u}}(o,0,r,t)},d=function(t,n){var e=t.props.animate;if(!t.type)return{};var r=t.props&&t.props.polar&&t.type.defaultPolarTransitions||t.type.defaultTransitions;if(r){var o=e[n]&&e[n].duration;return void 0!==o?o:r[n]&&r[n].duration}return{}};return function(e,u){var g=Yt(e)||[],v=a()({},t.animate,e.props.animate),y=e.props.polar&&e.type.defaultPolarTransitions||e.type.defaultTransitions;v.onExit=a()({},v.onExit,y&&y.onExit),v.onEnter=a()({},v.onEnter,y&&y.onEnter),v.onLoad=a()({},v.onLoad,y&&y.onLoad);var m=l[u]||l[0];if(!c){var b={duration:void 0!==s.load?s.load:d(e,"onLoad")};return f(0,g,Object.assign({},v,b))}if(r){var x=m&&m.exiting,O=void 0!==s.exit?s.exit:d(e,"onExit"),w=x?{duration:O}:{delay:O};return p(x,0,g,Object.assign({},v,w))}if(o){var j=m&&m.entering,A=void 0!==s.enter?s.enter:d(e,"onEnter"),M=void 0!==s.move?s.move:e.props.animate&&e.props.animate.duration,k={duration:i&&j?A:M};return h(j,0,g,Object.assign({},v,k))}return!n&&v&&v.onExit?function(t,n){var e=t.onEnter&&t.onEnter.after?t.onEnter.after:Bt();return{data:n.map((function(t,r){return Object.assign({},t,e(t,r,n))}))}}(v,g):{animate:v,data:g}}}function Vt(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),e.push.apply(e,r)}return e}function Zt(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?Vt(Object(e),!0).forEach((function(n){Qt(t,n,e[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):Vt(Object(e)).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}))}return t}function Qt(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function Xt(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,o,i=[],a=!0,u=!1;try{for(e=e.call(t);!(a=(r=e.next()).done)&&(i.push(r.value),!n||i.length!==n);a=!0);}catch(t){u=!0,o=t}finally{try{a||null==e.return||e.return()}finally{if(u)throw o}}return i}}(t,n)||function(t,n){if(t){if("string"==typeof t)return Jt(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Jt(t,n):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Jt(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}var tn={nodesShouldLoad:!1,nodesDoneLoad:!1,animating:!0},nn=r(3849),en=r.n(nn),rn=r(8961),on=r.n(rn),an=r(9056),un=r.n(an),cn=r(9602),ln=r.n(cn),sn=r(4143),fn=r.n(sn);function pn(t){return t}function hn(t,n){return function(t,e){var r=function(t){return t.type.getAxis(t.props)===n}||pn,o=function(t){return t.reduce((function(t,n){return n.type&&"axis"===n.type.role&&r(n)?t.concat(n):n.props&&n.props.children?t.concat(o(c().Children.toArray(n.props.children))):t}),[])};return o(t)}(t)[0]}var dn=r(1616),gn=r.n(dn),vn=(r(8999),r(8254)),yn=r.n(vn);function mn(t,n,e){t=+t,n=+n,e=(o=arguments.length)<2?(n=t,t=0,1):o<3?1:+e;for(var r=-1,o=0|Math.max(0,Math.ceil((n-t)/e)),i=new Array(o);++r<o;)i[r]=t+r*e;return i}function bn(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t)}return this}function xn(t,n){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof n?this.interpolator(n):this.range(n)}return this}class On extends Map{constructor(t,n=jn){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}}),null!=t)for(const[n,e]of t)this.set(n,e)}get(t){return super.get(wn(this,t))}has(t){return super.has(wn(this,t))}set(t,n){return super.set(function({_intern:t,_key:n},e){const r=n(e);return t.has(r)?t.get(r):(t.set(r,e),e)}(this,t),n)}delete(t){return super.delete(function({_intern:t,_key:n},e){const r=n(e);return t.has(r)&&(e=t.get(r),t.delete(r)),e}(this,t))}}function wn({_intern:t,_key:n},e){const r=n(e);return t.has(r)?t.get(r):e}function jn(t){return null!==t&&"object"==typeof t?t.valueOf():t}Set;const An=Symbol("implicit");function Mn(){var t=new On,n=[],e=[],r=An;function o(o){let i=t.get(o);if(void 0===i){if(r!==An)return r;t.set(o,i=n.push(o)-1)}return e[i%e.length]}return o.domain=function(e){if(!arguments.length)return n.slice();n=[],t=new On;for(const r of e)t.has(r)||t.set(r,n.push(r)-1);return o},o.range=function(t){return arguments.length?(e=Array.from(t),o):e.slice()},o.unknown=function(t){return arguments.length?(r=t,o):r},o.copy=function(){return Mn(n,e).unknown(r)},bn.apply(o,arguments),o}function kn(){var t,n,e=Mn().unknown(void 0),r=e.domain,o=e.range,i=0,a=1,u=!1,c=0,l=0,s=.5;function f(){var e=r().length,f=a<i,p=f?a:i,h=f?i:a;t=(h-p)/Math.max(1,e-c+2*l),u&&(t=Math.floor(t)),p+=(h-p-t*(e-c))*s,n=t*(1-c),u&&(p=Math.round(p),n=Math.round(n));var d=mn(e).map((function(n){return p+t*n}));return o(f?d.reverse():d)}return delete e.unknown,e.domain=function(t){return arguments.length?(r(t),f()):r()},e.range=function(t){return arguments.length?([i,a]=t,i=+i,a=+a,f()):[i,a]},e.rangeRound=function(t){return[i,a]=t,i=+i,a=+a,u=!0,f()},e.bandwidth=function(){return n},e.step=function(){return t},e.round=function(t){return arguments.length?(u=!!t,f()):u},e.padding=function(t){return arguments.length?(c=Math.min(1,l=+t),f()):c},e.paddingInner=function(t){return arguments.length?(c=Math.min(1,t),f()):c},e.paddingOuter=function(t){return arguments.length?(l=+t,f()):l},e.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},e.copy=function(){return kn(r(),[i,a]).round(u).paddingInner(c).paddingOuter(l).align(s)},bn.apply(f(),arguments)}function En(t){var n=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return En(n())},t}function Sn(){return En(kn.apply(null,arguments).paddingInner(1))}var Dn=Math.sqrt(50),Cn=Math.sqrt(10),_n=Math.sqrt(2);function Pn(t,n,e){var r,o,i,a,u=-1;if(e=+e,(t=+t)==(n=+n)&&e>0)return[t];if((r=n<t)&&(o=t,t=n,n=o),0===(a=Tn(t,n,e))||!isFinite(a))return[];if(a>0){let e=Math.round(t/a),r=Math.round(n/a);for(e*a<t&&++e,r*a>n&&--r,i=new Array(o=r-e+1);++u<o;)i[u]=(e+u)*a}else{a=-a;let e=Math.round(t*a),r=Math.round(n*a);for(e/a<t&&++e,r/a>n&&--r,i=new Array(o=r-e+1);++u<o;)i[u]=(e+u)/a}return r&&i.reverse(),i}function Tn(t,n,e){var r=(n-t)/Math.max(0,e),o=Math.floor(Math.log(r)/Math.LN10),i=r/Math.pow(10,o);return o>=0?(i>=Dn?10:i>=Cn?5:i>=_n?2:1)*Math.pow(10,o):-Math.pow(10,-o)/(i>=Dn?10:i>=Cn?5:i>=_n?2:1)}function Nn(t,n,e){var r=Math.abs(n-t)/Math.max(0,e),o=Math.pow(10,Math.floor(Math.log(r)/Math.LN10)),i=r/o;return i>=Dn?o*=10:i>=Cn?o*=5:i>=_n&&(o*=2),n<t?-o:o}function Un(t,n){return null==t||null==n?NaN:t<n?-1:t>n?1:t>=n?0:NaN}function Fn(t,n){return null==t||null==n?NaN:n<t?-1:n>t?1:n>=t?0:NaN}function Ln(t){let n,e,r;function o(t,r,o=0,i=t.length){if(o<i){if(0!==n(r,r))return i;do{const n=o+i>>>1;e(t[n],r)<0?o=n+1:i=n}while(o<i)}return o}return 2!==t.length?(n=Un,e=(n,e)=>Un(t(n),e),r=(n,e)=>t(n)-e):(n=t===Un||t===Fn?t:In,e=t,r=t),{left:o,center:function(t,n,e=0,i=t.length){const a=o(t,n,e,i-1);return a>e&&r(t[a-1],n)>-r(t[a],n)?a-1:a},right:function(t,r,o=0,i=t.length){if(o<i){if(0!==n(r,r))return i;do{const n=o+i>>>1;e(t[n],r)<=0?o=n+1:i=n}while(o<i)}return o}}}function In(){return 0}function Rn(t){return null===t?NaN:+t}const Wn=Ln(Un),$n=Wn.right,Bn=(Wn.left,Ln(Rn).center,$n);function qn(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function zn(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function Hn(){}var Yn=.7,Gn=1/Yn,Kn="\\s*([+-]?\\d+)\\s*",Vn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Zn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Qn=/^#([0-9a-f]{3,8})$/,Xn=new RegExp(`^rgb\\(${Kn},${Kn},${Kn}\\)$`),Jn=new RegExp(`^rgb\\(${Zn},${Zn},${Zn}\\)$`),te=new RegExp(`^rgba\\(${Kn},${Kn},${Kn},${Vn}\\)$`),ne=new RegExp(`^rgba\\(${Zn},${Zn},${Zn},${Vn}\\)$`),ee=new RegExp(`^hsl\\(${Vn},${Zn},${Zn}\\)$`),re=new RegExp(`^hsla\\(${Vn},${Zn},${Zn},${Vn}\\)$`),oe={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function ie(){return this.rgb().formatHex()}function ae(){return this.rgb().formatRgb()}function ue(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=Qn.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?ce(n):3===e?new pe(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?le(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?le(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=Xn.exec(t))?new pe(n[1],n[2],n[3],1):(n=Jn.exec(t))?new pe(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=te.exec(t))?le(n[1],n[2],n[3],n[4]):(n=ne.exec(t))?le(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=ee.exec(t))?me(n[1],n[2]/100,n[3]/100,1):(n=re.exec(t))?me(n[1],n[2]/100,n[3]/100,n[4]):oe.hasOwnProperty(t)?ce(oe[t]):"transparent"===t?new pe(NaN,NaN,NaN,0):null}function ce(t){return new pe(t>>16&255,t>>8&255,255&t,1)}function le(t,n,e,r){return r<=0&&(t=n=e=NaN),new pe(t,n,e,r)}function se(t){return t instanceof Hn||(t=ue(t)),t?new pe((t=t.rgb()).r,t.g,t.b,t.opacity):new pe}function fe(t,n,e,r){return 1===arguments.length?se(t):new pe(t,n,e,null==r?1:r)}function pe(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function he(){return`#${ye(this.r)}${ye(this.g)}${ye(this.b)}`}function de(){const t=ge(this.opacity);return`${1===t?"rgb(":"rgba("}${ve(this.r)}, ${ve(this.g)}, ${ve(this.b)}${1===t?")":`, ${t})`}`}function ge(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function ve(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function ye(t){return((t=ve(t))<16?"0":"")+t.toString(16)}function me(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new xe(t,n,e,r)}function be(t){if(t instanceof xe)return new xe(t.h,t.s,t.l,t.opacity);if(t instanceof Hn||(t=ue(t)),!t)return new xe;if(t instanceof xe)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,o=Math.min(n,e,r),i=Math.max(n,e,r),a=NaN,u=i-o,c=(i+o)/2;return u?(a=n===i?(e-r)/u+6*(e<r):e===i?(r-n)/u+2:(n-e)/u+4,u/=c<.5?i+o:2-i-o,a*=60):u=c>0&&c<1?0:a,new xe(a,u,c,t.opacity)}function xe(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function Oe(t){return(t=(t||0)%360)<0?t+360:t}function we(t){return Math.max(0,Math.min(1,t||0))}function je(t,n,e){return 255*(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)}function Ae(t,n,e,r,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*n+(4-6*i+3*a)*e+(1+3*t+3*i-3*a)*r+a*o)/6}qn(Hn,ue,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:ie,formatHex:ie,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return be(this).formatHsl()},formatRgb:ae,toString:ae}),qn(pe,fe,zn(Hn,{brighter(t){return t=null==t?Gn:Math.pow(Gn,t),new pe(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?Yn:Math.pow(Yn,t),new pe(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new pe(ve(this.r),ve(this.g),ve(this.b),ge(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:he,formatHex:he,formatHex8:function(){return`#${ye(this.r)}${ye(this.g)}${ye(this.b)}${ye(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:de,toString:de})),qn(xe,(function(t,n,e,r){return 1===arguments.length?be(t):new xe(t,n,e,null==r?1:r)}),zn(Hn,{brighter(t){return t=null==t?Gn:Math.pow(Gn,t),new xe(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?Yn:Math.pow(Yn,t),new xe(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,o=2*e-r;return new pe(je(t>=240?t-240:t+120,o,r),je(t,o,r),je(t<120?t+240:t-120,o,r),this.opacity)},clamp(){return new xe(Oe(this.h),we(this.s),we(this.l),ge(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=ge(this.opacity);return`${1===t?"hsl(":"hsla("}${Oe(this.h)}, ${100*we(this.s)}%, ${100*we(this.l)}%${1===t?")":`, ${t})`}`}}));const Me=t=>()=>t;function ke(t,n){var e=n-t;return e?function(t,n){return function(e){return t+e*n}}(t,e):Me(isNaN(t)?n:t)}const Ee=function t(n){var e=function(t){return 1==(t=+t)?ke:function(n,e){return e-n?function(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(r){return Math.pow(t+r*n,e)}}(n,e,t):Me(isNaN(n)?e:n)}}(n);function r(t,n){var r=e((t=fe(t)).r,(n=fe(n)).r),o=e(t.g,n.g),i=e(t.b,n.b),a=ke(t.opacity,n.opacity);return function(n){return t.r=r(n),t.g=o(n),t.b=i(n),t.opacity=a(n),t+""}}return r.gamma=t,r}(1);function Se(t){return function(n){var e,r,o=n.length,i=new Array(o),a=new Array(o),u=new Array(o);for(e=0;e<o;++e)r=fe(n[e]),i[e]=r.r||0,a[e]=r.g||0,u[e]=r.b||0;return i=t(i),a=t(a),u=t(u),r.opacity=1,function(t){return r.r=i(t),r.g=a(t),r.b=u(t),r+""}}}function De(t,n){var e,r=n?n.length:0,o=t?Math.min(r,t.length):0,i=new Array(o),a=new Array(r);for(e=0;e<o;++e)i[e]=Le(t[e],n[e]);for(;e<r;++e)a[e]=n[e];return function(t){for(e=0;e<o;++e)a[e]=i[e](t);return a}}function Ce(t,n){var e=new Date;return t=+t,n=+n,function(r){return e.setTime(t*(1-r)+n*r),e}}function _e(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}}function Pe(t,n){var e,r={},o={};for(e in null!==t&&"object"==typeof t||(t={}),null!==n&&"object"==typeof n||(n={}),n)e in t?r[e]=Le(t[e],n[e]):o[e]=n[e];return function(t){for(e in r)o[e]=r[e](t);return o}}Se((function(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),o=t[r],i=t[r+1],a=r>0?t[r-1]:2*o-i,u=r<n-1?t[r+2]:2*i-o;return Ae((e-r/n)*n,a,o,i,u)}})),Se((function(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),o=t[(r+n-1)%n],i=t[r%n],a=t[(r+1)%n],u=t[(r+2)%n];return Ae((e-r/n)*n,o,i,a,u)}}));var Te=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Ne=new RegExp(Te.source,"g");function Ue(t,n){var e,r,o,i=Te.lastIndex=Ne.lastIndex=0,a=-1,u=[],c=[];for(t+="",n+="";(e=Te.exec(t))&&(r=Ne.exec(n));)(o=r.index)>i&&(o=n.slice(i,o),u[a]?u[a]+=o:u[++a]=o),(e=e[0])===(r=r[0])?u[a]?u[a]+=r:u[++a]=r:(u[++a]=null,c.push({i:a,x:_e(e,r)})),i=Ne.lastIndex;return i<n.length&&(o=n.slice(i),u[a]?u[a]+=o:u[++a]=o),u.length<2?c[0]?function(t){return function(n){return t(n)+""}}(c[0].x):function(t){return function(){return t}}(n):(n=c.length,function(t){for(var e,r=0;r<n;++r)u[(e=c[r]).i]=e.x(t);return u.join("")})}function Fe(t,n){n||(n=[]);var e,r=t?Math.min(n.length,t.length):0,o=n.slice();return function(i){for(e=0;e<r;++e)o[e]=t[e]*(1-i)+n[e]*i;return o}}function Le(t,n){var e,r,o=typeof n;return null==n||"boolean"===o?Me(n):("number"===o?_e:"string"===o?(e=ue(n))?(n=e,Ee):Ue:n instanceof ue?Ee:n instanceof Date?Ce:(r=n,!ArrayBuffer.isView(r)||r instanceof DataView?Array.isArray(n)?De:"function"!=typeof n.valueOf&&"function"!=typeof n.toString||isNaN(n)?Pe:_e:Fe))(t,n)}function Ie(t,n){return t=+t,n=+n,function(e){return Math.round(t*(1-e)+n*e)}}function Re(t){return+t}var We=[0,1];function $e(t){return t}function Be(t,n){return(n-=t=+t)?function(e){return(e-t)/n}:(e=isNaN(n)?NaN:.5,function(){return e});var e}function qe(t,n,e){var r=t[0],o=t[1],i=n[0],a=n[1];return o<r?(r=Be(o,r),i=e(a,i)):(r=Be(r,o),i=e(i,a)),function(t){return i(r(t))}}function ze(t,n,e){var r=Math.min(t.length,n.length)-1,o=new Array(r),i=new Array(r),a=-1;for(t[r]<t[0]&&(t=t.slice().reverse(),n=n.slice().reverse());++a<r;)o[a]=Be(t[a],t[a+1]),i[a]=e(n[a],n[a+1]);return function(n){var e=Bn(t,n,1,r)-1;return i[e](o[e](n))}}function He(t,n){return n.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function Ye(){var t,n,e,r,o,i,a=We,u=We,c=Le,l=$e;function s(){var t,n,e,c=Math.min(a.length,u.length);return l!==$e&&(t=a[0],n=a[c-1],t>n&&(e=t,t=n,n=e),l=function(e){return Math.max(t,Math.min(n,e))}),r=c>2?ze:qe,o=i=null,f}function f(n){return null==n||isNaN(n=+n)?e:(o||(o=r(a.map(t),u,c)))(t(l(n)))}return f.invert=function(e){return l(n((i||(i=r(u,a.map(t),_e)))(e)))},f.domain=function(t){return arguments.length?(a=Array.from(t,Re),s()):a.slice()},f.range=function(t){return arguments.length?(u=Array.from(t),s()):u.slice()},f.rangeRound=function(t){return u=Array.from(t),c=Ie,s()},f.clamp=function(t){return arguments.length?(l=!!t||$e,s()):l!==$e},f.interpolate=function(t){return arguments.length?(c=t,s()):c},f.unknown=function(t){return arguments.length?(e=t,f):e},function(e,r){return t=e,n=r,s()}}function Ge(){return Ye()($e,$e)}var Ke,Ve=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Ze(t){if(!(n=Ve.exec(t)))throw new Error("invalid format: "+t);var n;return new Qe({fill:n[1],align:n[2],sign:n[3],symbol:n[4],zero:n[5],width:n[6],comma:n[7],precision:n[8]&&n[8].slice(1),trim:n[9],type:n[10]})}function Qe(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function Xe(t,n){if((e=(t=n?t.toExponential(n-1):t.toExponential()).indexOf("e"))<0)return null;var e,r=t.slice(0,e);return[r.length>1?r[0]+r.slice(2):r,+t.slice(e+1)]}function Je(t){return(t=Xe(Math.abs(t)))?t[1]:NaN}function tr(t,n){var e=Xe(t,n);if(!e)return t+"";var r=e[0],o=e[1];return o<0?"0."+new Array(-o).join("0")+r:r.length>o+1?r.slice(0,o+1)+"."+r.slice(o+1):r+new Array(o-r.length+2).join("0")}Ze.prototype=Qe.prototype,Qe.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const nr={"%":(t,n)=>(100*t).toFixed(n),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,n)=>t.toExponential(n),f:(t,n)=>t.toFixed(n),g:(t,n)=>t.toPrecision(n),o:t=>Math.round(t).toString(8),p:(t,n)=>tr(100*t,n),r:tr,s:function(t,n){var e=Xe(t,n);if(!e)return t+"";var r=e[0],o=e[1],i=o-(Ke=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=r.length;return i===a?r:i>a?r+new Array(i-a+1).join("0"):i>0?r.slice(0,i)+"."+r.slice(i):"0."+new Array(1-i).join("0")+Xe(t,Math.max(0,n+i-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function er(t){return t}var rr,or,ir,ar=Array.prototype.map,ur=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function cr(t,n,e,r){var o,i=Nn(t,n,e);switch((r=Ze(null==r?",f":r)).type){case"s":var a=Math.max(Math.abs(t),Math.abs(n));return null!=r.precision||isNaN(o=function(t,n){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Je(n)/3)))-Je(Math.abs(t)))}(i,a))||(r.precision=o),ir(r,a);case"":case"e":case"g":case"p":case"r":null!=r.precision||isNaN(o=function(t,n){return t=Math.abs(t),n=Math.abs(n)-t,Math.max(0,Je(n)-Je(t))+1}(i,Math.max(Math.abs(t),Math.abs(n))))||(r.precision=o-("e"===r.type));break;case"f":case"%":null!=r.precision||isNaN(o=function(t){return Math.max(0,-Je(Math.abs(t)))}(i))||(r.precision=o-2*("%"===r.type))}return or(r)}function lr(t){var n=t.domain;return t.ticks=function(t){var e=n();return Pn(e[0],e[e.length-1],null==t?10:t)},t.tickFormat=function(t,e){var r=n();return cr(r[0],r[r.length-1],null==t?10:t,e)},t.nice=function(e){null==e&&(e=10);var r,o,i=n(),a=0,u=i.length-1,c=i[a],l=i[u],s=10;for(l<c&&(o=c,c=l,l=o,o=a,a=u,u=o);s-- >0;){if((o=Tn(c,l,e))===r)return i[a]=c,i[u]=l,n(i);if(o>0)c=Math.floor(c/o)*o,l=Math.ceil(l/o)*o;else{if(!(o<0))break;c=Math.ceil(c*o)/o,l=Math.floor(l*o)/o}r=o}return t},t}function sr(){var t=Ge();return t.copy=function(){return He(t,sr())},bn.apply(t,arguments),lr(t)}function fr(t){var n;function e(t){return null==t||isNaN(t=+t)?n:t}return e.invert=e,e.domain=e.range=function(n){return arguments.length?(t=Array.from(n,Re),e):t.slice()},e.unknown=function(t){return arguments.length?(n=t,e):n},e.copy=function(){return fr(t).unknown(n)},t=arguments.length?Array.from(t,Re):[0,1],lr(e)}function pr(t,n){var e,r=0,o=(t=t.slice()).length-1,i=t[r],a=t[o];return a<i&&(e=r,r=o,o=e,e=i,i=a,a=e),t[r]=n.floor(i),t[o]=n.ceil(a),t}function hr(t){return Math.log(t)}function dr(t){return Math.exp(t)}function gr(t){return-Math.log(-t)}function vr(t){return-Math.exp(-t)}function yr(t){return isFinite(t)?+("1e"+t):t<0?0:t}function mr(t){return(n,e)=>-t(-n,e)}function br(t){const n=t(hr,dr),e=n.domain;let r,o,i=10;function a(){return r=function(t){return t===Math.E?Math.log:10===t&&Math.log10||2===t&&Math.log2||(t=Math.log(t),n=>Math.log(n)/t)}(i),o=function(t){return 10===t?yr:t===Math.E?Math.exp:n=>Math.pow(t,n)}(i),e()[0]<0?(r=mr(r),o=mr(o),t(gr,vr)):t(hr,dr),n}return n.base=function(t){return arguments.length?(i=+t,a()):i},n.domain=function(t){return arguments.length?(e(t),a()):e()},n.ticks=t=>{const n=e();let a=n[0],u=n[n.length-1];const c=u<a;c&&([a,u]=[u,a]);let l,s,f=r(a),p=r(u);const h=null==t?10:+t;let d=[];if(!(i%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),a>0){for(;f<=p;++f)for(l=1;l<i;++l)if(s=f<0?l/o(-f):l*o(f),!(s<a)){if(s>u)break;d.push(s)}}else for(;f<=p;++f)for(l=i-1;l>=1;--l)if(s=f>0?l/o(-f):l*o(f),!(s<a)){if(s>u)break;d.push(s)}2*d.length<h&&(d=Pn(a,u,h))}else d=Pn(f,p,Math.min(p-f,h)).map(o);return c?d.reverse():d},n.tickFormat=(t,e)=>{if(null==t&&(t=10),null==e&&(e=10===i?"s":","),"function"!=typeof e&&(i%1||null!=(e=Ze(e)).precision||(e.trim=!0),e=or(e)),t===1/0)return e;const a=Math.max(1,i*t/n.ticks().length);return t=>{let n=t/o(Math.round(r(t)));return n*i<i-.5&&(n*=i),n<=a?e(t):""}},n.nice=()=>e(pr(e(),{floor:t=>o(Math.floor(r(t))),ceil:t=>o(Math.ceil(r(t)))})),n}function xr(){const t=br(Ye()).domain([1,10]);return t.copy=()=>He(t,xr()).base(t.base()),bn.apply(t,arguments),t}function Or(t){return function(n){return Math.sign(n)*Math.log1p(Math.abs(n/t))}}function wr(t){return function(n){return Math.sign(n)*Math.expm1(Math.abs(n))*t}}function jr(t){var n=1,e=t(Or(n),wr(n));return e.constant=function(e){return arguments.length?t(Or(n=+e),wr(n)):n},lr(e)}function Ar(){var t=jr(Ye());return t.copy=function(){return He(t,Ar()).constant(t.constant())},bn.apply(t,arguments)}function Mr(t){return function(n){return n<0?-Math.pow(-n,t):Math.pow(n,t)}}function kr(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function Er(t){return t<0?-t*t:t*t}function Sr(t){var n=t($e,$e),e=1;function r(){return 1===e?t($e,$e):.5===e?t(kr,Er):t(Mr(e),Mr(1/e))}return n.exponent=function(t){return arguments.length?(e=+t,r()):e},lr(n)}function Dr(){var t=Sr(Ye());return t.copy=function(){return He(t,Dr()).exponent(t.exponent())},bn.apply(t,arguments),t}function Cr(){return Dr.apply(null,arguments).exponent(.5)}function _r(t){return Math.sign(t)*t*t}function Pr(t){return Math.sign(t)*Math.sqrt(Math.abs(t))}function Tr(){var t,n=Ge(),e=[0,1],r=!1;function o(e){var o=Pr(n(e));return isNaN(o)?t:r?Math.round(o):o}return o.invert=function(t){return n.invert(_r(t))},o.domain=function(t){return arguments.length?(n.domain(t),o):n.domain()},o.range=function(t){return arguments.length?(n.range((e=Array.from(t,Re)).map(_r)),o):e.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(r=!!t,o):r},o.clamp=function(t){return arguments.length?(n.clamp(t),o):n.clamp()},o.unknown=function(n){return arguments.length?(t=n,o):t},o.copy=function(){return Tr(n.domain(),e).round(r).clamp(n.clamp()).unknown(t)},bn.apply(o,arguments),lr(o)}function Nr(t,n){let e;if(void 0===n)for(const n of t)null!=n&&(e<n||void 0===e&&n>=n)&&(e=n);else{let r=-1;for(let o of t)null!=(o=n(o,++r,t))&&(e<o||void 0===e&&o>=o)&&(e=o)}return e}function Ur(t,n){let e;if(void 0===n)for(const n of t)null!=n&&(e>n||void 0===e&&n>=n)&&(e=n);else{let r=-1;for(let o of t)null!=(o=n(o,++r,t))&&(e>o||void 0===e&&o>=o)&&(e=o)}return e}function Fr(t,n){return(null==t||!(t>=t))-(null==n||!(n>=n))||(t<n?-1:t>n?1:0)}function Lr(t,n,e=0,r=t.length-1,o){for(o=void 0===o?Fr:function(t=Un){if(t===Un)return Fr;if("function"!=typeof t)throw new TypeError("compare is not a function");return(n,e)=>{const r=t(n,e);return r||0===r?r:(0===t(e,e))-(0===t(n,n))}}(o);r>e;){if(r-e>600){const i=r-e+1,a=n-e+1,u=Math.log(i),c=.5*Math.exp(2*u/3),l=.5*Math.sqrt(u*c*(i-c)/i)*(a-i/2<0?-1:1);Lr(t,n,Math.max(e,Math.floor(n-a*c/i+l)),Math.min(r,Math.floor(n+(i-a)*c/i+l)),o)}const i=t[n];let a=e,u=r;for(Ir(t,e,n),o(t[r],i)>0&&Ir(t,e,r);a<u;){for(Ir(t,a,u),++a,--u;o(t[a],i)<0;)++a;for(;o(t[u],i)>0;)--u}0===o(t[e],i)?Ir(t,e,u):(++u,Ir(t,u,r)),u<=n&&(e=u+1),n<=u&&(r=u-1)}return t}function Ir(t,n,e){const r=t[n];t[n]=t[e],t[e]=r}function Rr(t,n,e){if(t=Float64Array.from(function*(t,n){if(void 0===n)for(let n of t)null!=n&&(n=+n)>=n&&(yield n);else{let e=-1;for(let r of t)null!=(r=n(r,++e,t))&&(r=+r)>=r&&(yield r)}}(t,e)),r=t.length){if((n=+n)<=0||r<2)return Ur(t);if(n>=1)return Nr(t);var r,o=(r-1)*n,i=Math.floor(o),a=Nr(Lr(t,i).subarray(0,i+1));return a+(Ur(t.subarray(i+1))-a)*(o-i)}}function Wr(t,n,e=Rn){if(r=t.length){if((n=+n)<=0||r<2)return+e(t[0],0,t);if(n>=1)return+e(t[r-1],r-1,t);var r,o=(r-1)*n,i=Math.floor(o),a=+e(t[i],i,t);return a+(+e(t[i+1],i+1,t)-a)*(o-i)}}function $r(){var t,n=[],e=[],r=[];function o(){var t=0,o=Math.max(1,e.length);for(r=new Array(o-1);++t<o;)r[t-1]=Wr(n,t/o);return i}function i(n){return null==n||isNaN(n=+n)?t:e[Bn(r,n)]}return i.invertExtent=function(t){var o=e.indexOf(t);return o<0?[NaN,NaN]:[o>0?r[o-1]:n[0],o<r.length?r[o]:n[n.length-1]]},i.domain=function(t){if(!arguments.length)return n.slice();n=[];for(let e of t)null==e||isNaN(e=+e)||n.push(e);return n.sort(Un),o()},i.range=function(t){return arguments.length?(e=Array.from(t),o()):e.slice()},i.unknown=function(n){return arguments.length?(t=n,i):t},i.quantiles=function(){return r.slice()},i.copy=function(){return $r().domain(n).range(e).unknown(t)},bn.apply(i,arguments)}function Br(){var t,n=0,e=1,r=1,o=[.5],i=[0,1];function a(n){return null!=n&&n<=n?i[Bn(o,n,0,r)]:t}function u(){var t=-1;for(o=new Array(r);++t<r;)o[t]=((t+1)*e-(t-r)*n)/(r+1);return a}return a.domain=function(t){return arguments.length?([n,e]=t,n=+n,e=+e,u()):[n,e]},a.range=function(t){return arguments.length?(r=(i=Array.from(t)).length-1,u()):i.slice()},a.invertExtent=function(t){var a=i.indexOf(t);return a<0?[NaN,NaN]:a<1?[n,o[0]]:a>=r?[o[r-1],e]:[o[a-1],o[a]]},a.unknown=function(n){return arguments.length?(t=n,a):a},a.thresholds=function(){return o.slice()},a.copy=function(){return Br().domain([n,e]).range(i).unknown(t)},bn.apply(lr(a),arguments)}function qr(){var t,n=[.5],e=[0,1],r=1;function o(o){return null!=o&&o<=o?e[Bn(n,o,0,r)]:t}return o.domain=function(t){return arguments.length?(n=Array.from(t),r=Math.min(n.length,e.length-1),o):n.slice()},o.range=function(t){return arguments.length?(e=Array.from(t),r=Math.min(n.length,e.length-1),o):e.slice()},o.invertExtent=function(t){var r=e.indexOf(t);return[n[r-1],n[r]]},o.unknown=function(n){return arguments.length?(t=n,o):t},o.copy=function(){return qr().domain(n).range(e).unknown(t)},bn.apply(o,arguments)}rr=function(t){var n,e,r=void 0===t.grouping||void 0===t.thousands?er:(n=ar.call(t.grouping,Number),e=t.thousands+"",function(t,r){for(var o=t.length,i=[],a=0,u=n[0],c=0;o>0&&u>0&&(c+u+1>r&&(u=Math.max(1,r-c)),i.push(t.substring(o-=u,o+u)),!((c+=u+1)>r));)u=n[a=(a+1)%n.length];return i.reverse().join(e)}),o=void 0===t.currency?"":t.currency[0]+"",i=void 0===t.currency?"":t.currency[1]+"",a=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?er:function(t){return function(n){return n.replace(/[0-9]/g,(function(n){return t[+n]}))}}(ar.call(t.numerals,String)),c=void 0===t.percent?"%":t.percent+"",l=void 0===t.minus?"−":t.minus+"",s=void 0===t.nan?"NaN":t.nan+"";function f(t){var n=(t=Ze(t)).fill,e=t.align,f=t.sign,p=t.symbol,h=t.zero,d=t.width,g=t.comma,v=t.precision,y=t.trim,m=t.type;"n"===m?(g=!0,m="g"):nr[m]||(void 0===v&&(v=12),y=!0,m="g"),(h||"0"===n&&"="===e)&&(h=!0,n="0",e="=");var b="$"===p?o:"#"===p&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",x="$"===p?i:/[%p]/.test(m)?c:"",O=nr[m],w=/[defgprs%]/.test(m);function j(t){var o,i,c,p=b,j=x;if("c"===m)j=O(t)+j,t="";else{var A=(t=+t)<0||1/t<0;if(t=isNaN(t)?s:O(Math.abs(t),v),y&&(t=function(t){t:for(var n,e=t.length,r=1,o=-1;r<e;++r)switch(t[r]){case".":o=n=r;break;case"0":0===o&&(o=r),n=r;break;default:if(!+t[r])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(n+1):t}(t)),A&&0==+t&&"+"!==f&&(A=!1),p=(A?"("===f?f:l:"-"===f||"("===f?"":f)+p,j=("s"===m?ur[8+Ke/3]:"")+j+(A&&"("===f?")":""),w)for(o=-1,i=t.length;++o<i;)if(48>(c=t.charCodeAt(o))||c>57){j=(46===c?a+t.slice(o+1):t.slice(o))+j,t=t.slice(0,o);break}}g&&!h&&(t=r(t,1/0));var M=p.length+t.length+j.length,k=M<d?new Array(d-M+1).join(n):"";switch(g&&h&&(t=r(k+t,k.length?d-j.length:1/0),k=""),e){case"<":t=p+t+j+k;break;case"=":t=p+k+t+j;break;case"^":t=k.slice(0,M=k.length>>1)+p+t+j+k.slice(M);break;default:t=k+p+t+j}return u(t)}return v=void 0===v?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:f,formatPrefix:function(t,n){var e=f(((t=Ze(t)).type="f",t)),r=3*Math.max(-8,Math.min(8,Math.floor(Je(n)/3))),o=Math.pow(10,-r),i=ur[8+r/3];return function(t){return e(o*t)+i}}}}({thousands:",",grouping:[3],currency:["$",""]}),or=rr.format,ir=rr.formatPrefix;const zr=1e3,Hr=6e4,Yr=36e5,Gr=864e5,Kr=6048e5,Vr=31536e6;var Zr=new Date,Qr=new Date;function Xr(t,n,e,r){function o(n){return t(n=0===arguments.length?new Date:new Date(+n)),n}return o.floor=function(n){return t(n=new Date(+n)),n},o.ceil=function(e){return t(e=new Date(e-1)),n(e,1),t(e),e},o.round=function(t){var n=o(t),e=o.ceil(t);return t-n<e-t?n:e},o.offset=function(t,e){return n(t=new Date(+t),null==e?1:Math.floor(e)),t},o.range=function(e,r,i){var a,u=[];if(e=o.ceil(e),i=null==i?1:Math.floor(i),!(e<r&&i>0))return u;do{u.push(a=new Date(+e)),n(e,i),t(e)}while(a<e&&e<r);return u},o.filter=function(e){return Xr((function(n){if(n>=n)for(;t(n),!e(n);)n.setTime(n-1)}),(function(t,r){if(t>=t)if(r<0)for(;++r<=0;)for(;n(t,-1),!e(t););else for(;--r>=0;)for(;n(t,1),!e(t););}))},e&&(o.count=function(n,r){return Zr.setTime(+n),Qr.setTime(+r),t(Zr),t(Qr),Math.floor(e(Zr,Qr))},o.every=function(t){return t=Math.floor(t),isFinite(t)&&t>0?t>1?o.filter(r?function(n){return r(n)%t==0}:function(n){return o.count(0,n)%t==0}):o:null}),o}var Jr=Xr((function(){}),(function(t,n){t.setTime(+t+n)}),(function(t,n){return n-t}));Jr.every=function(t){return t=Math.floor(t),isFinite(t)&&t>0?t>1?Xr((function(n){n.setTime(Math.floor(n/t)*t)}),(function(n,e){n.setTime(+n+e*t)}),(function(n,e){return(e-n)/t})):Jr:null};const to=Jr;Jr.range;var no=Xr((function(t){t.setTime(t-t.getMilliseconds())}),(function(t,n){t.setTime(+t+n*zr)}),(function(t,n){return(n-t)/zr}),(function(t){return t.getUTCSeconds()}));const eo=no;no.range;var ro=Xr((function(t){t.setTime(t-t.getMilliseconds()-t.getSeconds()*zr)}),(function(t,n){t.setTime(+t+n*Hr)}),(function(t,n){return(n-t)/Hr}),(function(t){return t.getMinutes()}));const oo=ro;ro.range;var io=Xr((function(t){t.setTime(t-t.getMilliseconds()-t.getSeconds()*zr-t.getMinutes()*Hr)}),(function(t,n){t.setTime(+t+n*Yr)}),(function(t,n){return(n-t)/Yr}),(function(t){return t.getHours()}));const ao=io;io.range;var uo=Xr((t=>t.setHours(0,0,0,0)),((t,n)=>t.setDate(t.getDate()+n)),((t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*Hr)/Gr),(t=>t.getDate()-1));const co=uo;function lo(t){return Xr((function(n){n.setDate(n.getDate()-(n.getDay()+7-t)%7),n.setHours(0,0,0,0)}),(function(t,n){t.setDate(t.getDate()+7*n)}),(function(t,n){return(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*Hr)/Kr}))}uo.range;var so=lo(0),fo=lo(1),po=lo(2),ho=lo(3),go=lo(4),vo=lo(5),yo=lo(6),mo=(so.range,fo.range,po.range,ho.range,go.range,vo.range,yo.range,Xr((function(t){t.setDate(1),t.setHours(0,0,0,0)}),(function(t,n){t.setMonth(t.getMonth()+n)}),(function(t,n){return n.getMonth()-t.getMonth()+12*(n.getFullYear()-t.getFullYear())}),(function(t){return t.getMonth()})));const bo=mo;mo.range;var xo=Xr((function(t){t.setMonth(0,1),t.setHours(0,0,0,0)}),(function(t,n){t.setFullYear(t.getFullYear()+n)}),(function(t,n){return n.getFullYear()-t.getFullYear()}),(function(t){return t.getFullYear()}));xo.every=function(t){return isFinite(t=Math.floor(t))&&t>0?Xr((function(n){n.setFullYear(Math.floor(n.getFullYear()/t)*t),n.setMonth(0,1),n.setHours(0,0,0,0)}),(function(n,e){n.setFullYear(n.getFullYear()+e*t)})):null};const Oo=xo;xo.range;var wo=Xr((function(t){t.setUTCSeconds(0,0)}),(function(t,n){t.setTime(+t+n*Hr)}),(function(t,n){return(n-t)/Hr}),(function(t){return t.getUTCMinutes()}));const jo=wo;wo.range;var Ao=Xr((function(t){t.setUTCMinutes(0,0,0)}),(function(t,n){t.setTime(+t+n*Yr)}),(function(t,n){return(n-t)/Yr}),(function(t){return t.getUTCHours()}));const Mo=Ao;Ao.range;var ko=Xr((function(t){t.setUTCHours(0,0,0,0)}),(function(t,n){t.setUTCDate(t.getUTCDate()+n)}),(function(t,n){return(n-t)/Gr}),(function(t){return t.getUTCDate()-1}));const Eo=ko;function So(t){return Xr((function(n){n.setUTCDate(n.getUTCDate()-(n.getUTCDay()+7-t)%7),n.setUTCHours(0,0,0,0)}),(function(t,n){t.setUTCDate(t.getUTCDate()+7*n)}),(function(t,n){return(n-t)/Kr}))}ko.range;var Do=So(0),Co=So(1),_o=So(2),Po=So(3),To=So(4),No=So(5),Uo=So(6),Fo=(Do.range,Co.range,_o.range,Po.range,To.range,No.range,Uo.range,Xr((function(t){t.setUTCDate(1),t.setUTCHours(0,0,0,0)}),(function(t,n){t.setUTCMonth(t.getUTCMonth()+n)}),(function(t,n){return n.getUTCMonth()-t.getUTCMonth()+12*(n.getUTCFullYear()-t.getUTCFullYear())}),(function(t){return t.getUTCMonth()})));const Lo=Fo;Fo.range;var Io=Xr((function(t){t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)}),(function(t,n){t.setUTCFullYear(t.getUTCFullYear()+n)}),(function(t,n){return n.getUTCFullYear()-t.getUTCFullYear()}),(function(t){return t.getUTCFullYear()}));Io.every=function(t){return isFinite(t=Math.floor(t))&&t>0?Xr((function(n){n.setUTCFullYear(Math.floor(n.getUTCFullYear()/t)*t),n.setUTCMonth(0,1),n.setUTCHours(0,0,0,0)}),(function(n,e){n.setUTCFullYear(n.getUTCFullYear()+e*t)})):null};const Ro=Io;function Wo(t,n,e,r,o,i){const a=[[eo,1,zr],[eo,5,5e3],[eo,15,15e3],[eo,30,3e4],[i,1,Hr],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,Yr],[o,3,108e5],[o,6,216e5],[o,12,432e5],[r,1,Gr],[r,2,1728e5],[e,1,Kr],[n,1,2592e6],[n,3,7776e6],[t,1,Vr]];function u(n,e,r){const o=Math.abs(e-n)/r,i=Ln((([,,t])=>t)).right(a,o);if(i===a.length)return t.every(Nn(n/Vr,e/Vr,r));if(0===i)return to.every(Math.max(Nn(n,e,r),1));const[u,c]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return u.every(c)}return[function(t,n,e){const r=n<t;r&&([t,n]=[n,t]);const o=e&&"function"==typeof e.range?e:u(t,n,e),i=o?o.range(t,+n+1):[];return r?i.reverse():i},u]}Io.range;const[$o,Bo]=Wo(Ro,Lo,Do,Eo,Mo,jo),[qo,zo]=Wo(Oo,bo,so,co,ao,oo);function Ho(t){if(0<=t.y&&t.y<100){var n=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return n.setFullYear(t.y),n}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function Yo(t){if(0<=t.y&&t.y<100){var n=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return n.setUTCFullYear(t.y),n}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function Go(t,n,e){return{y:t,m:n,d:e,H:0,M:0,S:0,L:0}}var Ko,Vo,Zo,Qo={"-":"",_:" ",0:"0"},Xo=/^\s*\d+/,Jo=/^%/,ti=/[\\^$*+?|[\]().{}]/g;function ni(t,n,e){var r=t<0?"-":"",o=(r?-t:t)+"",i=o.length;return r+(i<e?new Array(e-i+1).join(n)+o:o)}function ei(t){return t.replace(ti,"\\$&")}function ri(t){return new RegExp("^(?:"+t.map(ei).join("|")+")","i")}function oi(t){return new Map(t.map(((t,n)=>[t.toLowerCase(),n])))}function ii(t,n,e){var r=Xo.exec(n.slice(e,e+1));return r?(t.w=+r[0],e+r[0].length):-1}function ai(t,n,e){var r=Xo.exec(n.slice(e,e+1));return r?(t.u=+r[0],e+r[0].length):-1}function ui(t,n,e){var r=Xo.exec(n.slice(e,e+2));return r?(t.U=+r[0],e+r[0].length):-1}function ci(t,n,e){var r=Xo.exec(n.slice(e,e+2));return r?(t.V=+r[0],e+r[0].length):-1}function li(t,n,e){var r=Xo.exec(n.slice(e,e+2));return r?(t.W=+r[0],e+r[0].length):-1}function si(t,n,e){var r=Xo.exec(n.slice(e,e+4));return r?(t.y=+r[0],e+r[0].length):-1}function fi(t,n,e){var r=Xo.exec(n.slice(e,e+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),e+r[0].length):-1}function pi(t,n,e){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(n.slice(e,e+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),e+r[0].length):-1}function hi(t,n,e){var r=Xo.exec(n.slice(e,e+1));return r?(t.q=3*r[0]-3,e+r[0].length):-1}function di(t,n,e){var r=Xo.exec(n.slice(e,e+2));return r?(t.m=r[0]-1,e+r[0].length):-1}function gi(t,n,e){var r=Xo.exec(n.slice(e,e+2));return r?(t.d=+r[0],e+r[0].length):-1}function vi(t,n,e){var r=Xo.exec(n.slice(e,e+3));return r?(t.m=0,t.d=+r[0],e+r[0].length):-1}function yi(t,n,e){var r=Xo.exec(n.slice(e,e+2));return r?(t.H=+r[0],e+r[0].length):-1}function mi(t,n,e){var r=Xo.exec(n.slice(e,e+2));return r?(t.M=+r[0],e+r[0].length):-1}function bi(t,n,e){var r=Xo.exec(n.slice(e,e+2));return r?(t.S=+r[0],e+r[0].length):-1}function xi(t,n,e){var r=Xo.exec(n.slice(e,e+3));return r?(t.L=+r[0],e+r[0].length):-1}function Oi(t,n,e){var r=Xo.exec(n.slice(e,e+6));return r?(t.L=Math.floor(r[0]/1e3),e+r[0].length):-1}function wi(t,n,e){var r=Jo.exec(n.slice(e,e+1));return r?e+r[0].length:-1}function ji(t,n,e){var r=Xo.exec(n.slice(e));return r?(t.Q=+r[0],e+r[0].length):-1}function Ai(t,n,e){var r=Xo.exec(n.slice(e));return r?(t.s=+r[0],e+r[0].length):-1}function Mi(t,n){return ni(t.getDate(),n,2)}function ki(t,n){return ni(t.getHours(),n,2)}function Ei(t,n){return ni(t.getHours()%12||12,n,2)}function Si(t,n){return ni(1+co.count(Oo(t),t),n,3)}function Di(t,n){return ni(t.getMilliseconds(),n,3)}function Ci(t,n){return Di(t,n)+"000"}function _i(t,n){return ni(t.getMonth()+1,n,2)}function Pi(t,n){return ni(t.getMinutes(),n,2)}function Ti(t,n){return ni(t.getSeconds(),n,2)}function Ni(t){var n=t.getDay();return 0===n?7:n}function Ui(t,n){return ni(so.count(Oo(t)-1,t),n,2)}function Fi(t){var n=t.getDay();return n>=4||0===n?go(t):go.ceil(t)}function Li(t,n){return t=Fi(t),ni(go.count(Oo(t),t)+(4===Oo(t).getDay()),n,2)}function Ii(t){return t.getDay()}function Ri(t,n){return ni(fo.count(Oo(t)-1,t),n,2)}function Wi(t,n){return ni(t.getFullYear()%100,n,2)}function $i(t,n){return ni((t=Fi(t)).getFullYear()%100,n,2)}function Bi(t,n){return ni(t.getFullYear()%1e4,n,4)}function qi(t,n){var e=t.getDay();return ni((t=e>=4||0===e?go(t):go.ceil(t)).getFullYear()%1e4,n,4)}function zi(t){var n=t.getTimezoneOffset();return(n>0?"-":(n*=-1,"+"))+ni(n/60|0,"0",2)+ni(n%60,"0",2)}function Hi(t,n){return ni(t.getUTCDate(),n,2)}function Yi(t,n){return ni(t.getUTCHours(),n,2)}function Gi(t,n){return ni(t.getUTCHours()%12||12,n,2)}function Ki(t,n){return ni(1+Eo.count(Ro(t),t),n,3)}function Vi(t,n){return ni(t.getUTCMilliseconds(),n,3)}function Zi(t,n){return Vi(t,n)+"000"}function Qi(t,n){return ni(t.getUTCMonth()+1,n,2)}function Xi(t,n){return ni(t.getUTCMinutes(),n,2)}function Ji(t,n){return ni(t.getUTCSeconds(),n,2)}function ta(t){var n=t.getUTCDay();return 0===n?7:n}function na(t,n){return ni(Do.count(Ro(t)-1,t),n,2)}function ea(t){var n=t.getUTCDay();return n>=4||0===n?To(t):To.ceil(t)}function ra(t,n){return t=ea(t),ni(To.count(Ro(t),t)+(4===Ro(t).getUTCDay()),n,2)}function oa(t){return t.getUTCDay()}function ia(t,n){return ni(Co.count(Ro(t)-1,t),n,2)}function aa(t,n){return ni(t.getUTCFullYear()%100,n,2)}function ua(t,n){return ni((t=ea(t)).getUTCFullYear()%100,n,2)}function ca(t,n){return ni(t.getUTCFullYear()%1e4,n,4)}function la(t,n){var e=t.getUTCDay();return ni((t=e>=4||0===e?To(t):To.ceil(t)).getUTCFullYear()%1e4,n,4)}function sa(){return"+0000"}function fa(){return"%"}function pa(t){return+t}function ha(t){return Math.floor(+t/1e3)}function da(t){return new Date(t)}function ga(t){return t instanceof Date?+t:+new Date(+t)}function va(t,n,e,r,o,i,a,u,c,l){var s=Ge(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),g=l("%I:%M"),v=l("%I %p"),y=l("%a %d"),m=l("%b %d"),b=l("%B"),x=l("%Y");function O(t){return(c(t)<t?h:u(t)<t?d:a(t)<t?g:i(t)<t?v:r(t)<t?o(t)<t?y:m:e(t)<t?b:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,ga)):p().map(da)},s.ticks=function(n){var e=p();return t(e[0],e[e.length-1],null==n?10:n)},s.tickFormat=function(t,n){return null==n?O:l(n)},s.nice=function(t){var e=p();return t&&"function"==typeof t.range||(t=n(e[0],e[e.length-1],null==t?10:t)),t?p(pr(e,t)):s},s.copy=function(){return He(s,va(t,n,e,r,o,i,a,u,c,l))},s}function ya(){return bn.apply(va(qo,zo,Oo,bo,so,co,ao,oo,eo,Vo).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function ma(){return bn.apply(va($o,Bo,Ro,Lo,Do,Eo,Mo,jo,eo,Zo).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function ba(){var t,n,e,r,o,i=0,a=1,u=$e,c=!1;function l(n){return null==n||isNaN(n=+n)?o:u(0===e?.5:(n=(r(n)-t)*e,c?Math.max(0,Math.min(1,n)):n))}function s(t){return function(n){var e,r;return arguments.length?([e,r]=n,u=t(e,r),l):[u(0),u(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=r(i=+i),n=r(a=+a),e=t===n?0:1/(n-t),l):[i,a]},l.clamp=function(t){return arguments.length?(c=!!t,l):c},l.interpolator=function(t){return arguments.length?(u=t,l):u},l.range=s(Le),l.rangeRound=s(Ie),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return r=o,t=o(i),n=o(a),e=t===n?0:1/(n-t),l}}function xa(t,n){return n.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function Oa(){var t=lr(ba()($e));return t.copy=function(){return xa(t,Oa())},xn.apply(t,arguments)}function wa(){var t=br(ba()).domain([1,10]);return t.copy=function(){return xa(t,wa()).base(t.base())},xn.apply(t,arguments)}function ja(){var t=jr(ba());return t.copy=function(){return xa(t,ja()).constant(t.constant())},xn.apply(t,arguments)}function Aa(){var t=Sr(ba());return t.copy=function(){return xa(t,Aa()).exponent(t.exponent())},xn.apply(t,arguments)}function Ma(){return Aa.apply(null,arguments).exponent(.5)}function ka(){var t=[],n=$e;function e(e){if(null!=e&&!isNaN(e=+e))return n((Bn(t,e,1)-1)/(t.length-1))}return e.domain=function(n){if(!arguments.length)return t.slice();t=[];for(let e of n)null==e||isNaN(e=+e)||t.push(e);return t.sort(Un),e},e.interpolator=function(t){return arguments.length?(n=t,e):n},e.range=function(){return t.map(((e,r)=>n(r/(t.length-1))))},e.quantiles=function(n){return Array.from({length:n+1},((e,r)=>Rr(t,r/n)))},e.copy=function(){return ka(n).domain(t)},xn.apply(e,arguments)}function Ea(t,n){void 0===n&&(n=t,t=Le);for(var e=0,r=n.length-1,o=n[0],i=new Array(r<0?0:r);e<r;)i[e]=t(o,o=n[++e]);return function(t){var n=Math.max(0,Math.min(r-1,Math.floor(t*=r)));return i[n](t-n)}}function Sa(){var t,n,e,r,o,i,a,u=0,c=.5,l=1,s=1,f=$e,p=!1;function h(t){return isNaN(t=+t)?a:(t=.5+((t=+i(t))-n)*(s*t<s*n?r:o),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(n){var e,r,o;return arguments.length?([e,r,o]=n,f=Ea(t,[e,r,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([u,c,l]=a,t=i(u=+u),n=i(c=+c),e=i(l=+l),r=t===n?0:.5/(n-t),o=n===e?0:.5/(e-n),s=n<t?-1:1,h):[u,c,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(Le),h.rangeRound=d(Ie),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(u),n=a(c),e=a(l),r=t===n?0:.5/(n-t),o=n===e?0:.5/(e-n),s=n<t?-1:1,h}}function Da(){var t=lr(Sa()($e));return t.copy=function(){return xa(t,Da())},xn.apply(t,arguments)}function Ca(){var t=br(Sa()).domain([.1,1,10]);return t.copy=function(){return xa(t,Ca()).base(t.base())},xn.apply(t,arguments)}function _a(){var t=jr(Sa());return t.copy=function(){return xa(t,_a()).constant(t.constant())},xn.apply(t,arguments)}function Pa(){var t=Sr(Sa());return t.copy=function(){return xa(t,Pa()).exponent(t.exponent())},xn.apply(t,arguments)}function Ta(){return Pa.apply(null,arguments).exponent(.5)}Ko=function(t){var n=t.dateTime,e=t.date,r=t.time,o=t.periods,i=t.days,a=t.shortDays,u=t.months,c=t.shortMonths,l=ri(o),s=oi(o),f=ri(i),p=oi(i),h=ri(a),d=oi(a),g=ri(u),v=oi(u),y=ri(c),m=oi(c),b={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return c[t.getMonth()]},B:function(t){return u[t.getMonth()]},c:null,d:Mi,e:Mi,f:Ci,g:$i,G:qi,H:ki,I:Ei,j:Si,L:Di,m:_i,M:Pi,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:pa,s:ha,S:Ti,u:Ni,U:Ui,V:Li,w:Ii,W:Ri,x:null,X:null,y:Wi,Y:Bi,Z:zi,"%":fa},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return c[t.getUTCMonth()]},B:function(t){return u[t.getUTCMonth()]},c:null,d:Hi,e:Hi,f:Zi,g:ua,G:la,H:Yi,I:Gi,j:Ki,L:Vi,m:Qi,M:Xi,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:pa,s:ha,S:Ji,u:ta,U:na,V:ra,w:oa,W:ia,x:null,X:null,y:aa,Y:ca,Z:sa,"%":fa},O={a:function(t,n,e){var r=h.exec(n.slice(e));return r?(t.w=d.get(r[0].toLowerCase()),e+r[0].length):-1},A:function(t,n,e){var r=f.exec(n.slice(e));return r?(t.w=p.get(r[0].toLowerCase()),e+r[0].length):-1},b:function(t,n,e){var r=y.exec(n.slice(e));return r?(t.m=m.get(r[0].toLowerCase()),e+r[0].length):-1},B:function(t,n,e){var r=g.exec(n.slice(e));return r?(t.m=v.get(r[0].toLowerCase()),e+r[0].length):-1},c:function(t,e,r){return A(t,n,e,r)},d:gi,e:gi,f:Oi,g:fi,G:si,H:yi,I:yi,j:vi,L:xi,m:di,M:mi,p:function(t,n,e){var r=l.exec(n.slice(e));return r?(t.p=s.get(r[0].toLowerCase()),e+r[0].length):-1},q:hi,Q:ji,s:Ai,S:bi,u:ai,U:ui,V:ci,w:ii,W:li,x:function(t,n,r){return A(t,e,n,r)},X:function(t,n,e){return A(t,r,n,e)},y:fi,Y:si,Z:pi,"%":wi};function w(t,n){return function(e){var r,o,i,a=[],u=-1,c=0,l=t.length;for(e instanceof Date||(e=new Date(+e));++u<l;)37===t.charCodeAt(u)&&(a.push(t.slice(c,u)),null!=(o=Qo[r=t.charAt(++u)])?r=t.charAt(++u):o="e"===r?" ":"0",(i=n[r])&&(r=i(e,o)),a.push(r),c=u+1);return a.push(t.slice(c,u)),a.join("")}}function j(t,n){return function(e){var r,o,i=Go(1900,void 0,1);if(A(i,t,e+="",0)!=e.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(n&&!("Z"in i)&&(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(o=(r=Yo(Go(i.y,0,1))).getUTCDay(),r=o>4||0===o?Co.ceil(r):Co(r),r=Eo.offset(r,7*(i.V-1)),i.y=r.getUTCFullYear(),i.m=r.getUTCMonth(),i.d=r.getUTCDate()+(i.w+6)%7):(o=(r=Ho(Go(i.y,0,1))).getDay(),r=o>4||0===o?fo.ceil(r):fo(r),r=co.offset(r,7*(i.V-1)),i.y=r.getFullYear(),i.m=r.getMonth(),i.d=r.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),o="Z"in i?Yo(Go(i.y,0,1)).getUTCDay():Ho(Go(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,Yo(i)):Ho(i)}}function A(t,n,e,r){for(var o,i,a=0,u=n.length,c=e.length;a<u;){if(r>=c)return-1;if(37===(o=n.charCodeAt(a++))){if(o=n.charAt(a++),!(i=O[o in Qo?n.charAt(a++):o])||(r=i(t,e,r))<0)return-1}else if(o!=e.charCodeAt(r++))return-1}return r}return b.x=w(e,b),b.X=w(r,b),b.c=w(n,b),x.x=w(e,x),x.X=w(r,x),x.c=w(n,x),{format:function(t){var n=w(t+="",b);return n.toString=function(){return t},n},parse:function(t){var n=j(t+="",!1);return n.toString=function(){return t},n},utcFormat:function(t){var n=w(t+="",x);return n.toString=function(){return t},n},utcParse:function(t){var n=j(t+="",!0);return n.toString=function(){return t},n}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]}),Vo=Ko.format,Ko.parse,Zo=Ko.utcFormat,Ko.utcParse;var Na=["linear","time","log","sqrt"];function Ua(t){return"function"==typeof t?Z(t.copy)&&Z(t.domain)&&Z(t.range):"string"==typeof t&&Na.includes(t)}function Fa(t,n){return!(!t.scale||(t.scale.x||t.scale.y)&&!t.scale[n])}function La(t,n){if(!t.data)return"linear";var e=Q(t[n]);return Ut(t.data.map((function(t){var r=en()(e(t))?e(t)[n]:e(t);return void 0!==r?r:t[n]})))?"time":"linear"}function Ia(n){if(Ua(n)){var e="scale".concat((r=n)&&r[0].toUpperCase()+r.slice(1));return t[e]()}var r;return sr()}function Ra(t,n){var e=Wa(t,n);return e?"string"==typeof e?Ia(e):e:Ia(function(t,n){var e;if(t.domain&&t.domain[n]?e=t.domain[n]:t.domain&&Array.isArray(t.domain)&&(e=t.domain),e)return Ut(e)?"time":"linear"}(t,n)||La(t,n))}function Wa(t,n){if(Fa(t,n)){var e=t.scale[n]||t.scale;return Ua(e)?Z(e)?e:Ia(e):void 0}}function $a(t,n){return function(t,n){if(Fa(t,n)){var e=t.scale[n]||t.scale;return"string"==typeof e?e:function(t){if("string"==typeof t)return t;var n=Ba.filter((function(n){return void 0!==t[n.method]}))[0];return n?n.name:void 0}(e)}}(t,n)||La(t,n)}var Ba=[{name:"quantile",method:"quantiles"},{name:"log",method:"base"}];function qa(t){return!(!t||!t["@@__IMMUTABLE_ITERABLE__@@"])}function za(t,n){return qa(t)?t.reduce((function(t,e,r){var o=e;return n&&n[r]&&(o=za(e)),t[r]=o,t}),function(t){return!(!t||!t["@@__IMMUTABLE_LIST__@@"])}(t)?[]:{}):t}function Ha(t){return function(t){if(Array.isArray(t))return Ya(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(t){if("string"==typeof t)return Ya(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Ya(t,n):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ya(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function Ga(t){var n;return qa(n=t)||function(t){return!(!t||!t["@@__IMMUTABLE_RECORD__@@"])}(n)?za(t,{errorX:!0,errorY:!0}):t}function Ka(t,n){var e=(en()(t.domain)?t.domain[n]:t.domain)||Ra(t,n).domain(),r=t.samples||1,o=Math.max.apply(Math,Ha(e)),i=Math.min.apply(Math,Ha(e)),a=function(t,n,e){var r=n?t:0,o=n||t;o||(o=0);var i=o-r,a=Math.abs(i),u=i/a||1,c=e||1,l=Math.max(Math.ceil(a/c),0);return Array.from(Array(l),(function(t,n){return r+n*u*c}))}(i,o,(o-i)/r);return a[a.length-1]===o?a:a.concat(o)}function Va(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ascending";if(!n)return t;var r=n;"x"!==n&&"y"!==n||(r="_".concat(n));var o="ascending"===e?"asc":"desc";return fn()(t,r,o)}function Za(t,n){var e=tu(t,n),r=nu(t,n),o=function(t,n){if(!Array.isArray(t.data)&&!qa(t.data))return[];var e=Q(void 0===t[n]?n:t[n]);return Va(t.data.reduce((function(t,n){return t.push(Ga(n)),t}),[]),t.sortKey,t.sortOrder).reduce((function(t,n){var r=Ga(n);return t.push(e(r)),t}),[]).filter((function(t){return"string"==typeof t})).reduce((function(t,n){return null!=n&&-1===t.indexOf(n)&&t.push(n),t}),[])}(t,n),i=ln()([].concat(Ha(e),Ha(r),Ha(o)));return 0===i.length?null:i.reduce((function(t,n,e){return t[n]=e+1,t}),{})}function Qa(t,n,r){if(!Array.isArray(t)&&!qa(t)||function(t){return qa(t)?t.size:t.length}(t)<1)return[];var o,i=["x","y","y0"],a=(r=Array.isArray(r)?r:i).reduce((function(t,e){var r;return t[e]=Q(void 0!==n[r=e]?n[r]:r),t}),{}),u=yn()(r,i)&&"_x"===n.x&&"_y"===n.y&&"_y0"===n.y0;!1===u&&(o={x:-1!==r.indexOf("x")?Za(n,"x"):void 0,y:-1!==r.indexOf("y")?Za(n,"y"):void 0,y0:-1!==r.indexOf("y0")?Za(n,"y"):void 0});var c=u?t:t.reduce((function(t,n,i){var u=Ga(n),c={x:i,y:u},l=r.reduce((function(t,n){var e=a[n](u),r=void 0!==e?e:c[n];return void 0!==r&&("string"==typeof r&&o[n]?(t["".concat(n,"Name")]=r,t["_".concat(n)]=o[n][r]):t["_".concat(n)]=r),t}),{}),s=Object.assign({},l,u);return e()(s)||t.push(s),t}),[]),l=function(t,n){var e=1/Number.MAX_SAFE_INTEGER,r={x:$a(n,"x"),y:$a(n,"y")};if("log"!==r.x&&"log"!==r.y)return t;var o=function(t,n){return"log"!==r[n]||0!==t["_".concat(n)]};return t.map((function(t){return o(t,"x")&&o(t,"y")&&o(t,"y0")?t:function(t){var n=o(t,"x")?t._x:e,r=o(t,"y")?t._y:e,i=o(t,"y0")?t._y0:e;return Object.assign({},t,{_x:n,_y:r,_y0:i})}(t)}))}(Va(c,n.sortKey,n.sortOrder),n);return function(t,n){var e,r=!!t.eventKey,o=Z(e=t.eventKey)?e:null==e?function(){}:q()(e);return n.map((function(t,n){if(void 0!==t.eventKey)return t;if(r){var e=o(t,n);return void 0!==e?Object.assign({eventKey:e},t):t}return t}))}(n,l)}function Xa(t,n){return t.categories&&!Array.isArray(t.categories)?t.categories[n]:t.categories}function Ja(t){return t.data?Qa(t.data,t):Qa(function(t){var n=Ka(t,"x"),e=Ka(t,"y");return n.map((function(t,n){return{x:t,y:e[n]}}))}(t),t)}function tu(t,n){var e=t.tickValues,r=t.tickFormat;return(e&&(Array.isArray(e)||e[n])?e[n]||e:r&&Array.isArray(r)?r:[]).filter((function(t){return"string"==typeof t}))}function nu(t,n){if(!t.categories)return[];var e=Xa(t,n),r=e&&e.filter((function(t){return"string"==typeof t}));return r?It(r):[]}function eu(t){var n=function(t){return t&&t.type?t.type.role:""},e=n(t);if("portal"===e){var r=c().Children.toArray(t.props.children);e=r.length?n(r[0]):""}return["area","bar","boxplot","candlestick","errorbar","group","histogram","line","pie","scatter","stack","voronoi"].includes(e)}var ru=r(4295),ou=r.n(ru),iu=r(2829),au=r.n(iu);function uu(t){return function(t){if(Array.isArray(t))return cu(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(t){if("string"==typeof t)return cu(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?cu(t,n):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cu(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function lu(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"min",r=function(t){return"max"===e?Math.max.apply(Math,uu(t)):Math.min.apply(Math,uu(t))},o="max"===e?-1/0:1/0,i=!1,a=t.flat().reduce((function(t,e){var o=void 0!==e["_".concat(n,"0")]?e["_".concat(n,"0")]:e["_".concat(n)],a=void 0!==e["_".concat(n,"1")]?e["_".concat(n,"1")]:e["_".concat(n)],u=r([o,a]);return i=i||o instanceof Date||a instanceof Date,r([t,u])}),o);return i?new Date(a):a}function su(t,n,e){return function(t,n,e){return"log"!==$a(n,e)?t:(o=(r=t)[0]<0||r[1]<0?-1/Number.MAX_SAFE_INTEGER:1/Number.MAX_SAFE_INTEGER,[0===r[0]?o:r[0],0===r[1]?o:r[1]]);var r,o}(function(t,n,e){if(!n.domainPadding)return t;var r=gu(n,e),o=du(n,e),i=function(t,n){var e=function(t){return Array.isArray(t)?{left:t[0],right:t[1]}:{left:t,right:t}};return en()(t.domainPadding)?e(t.domainPadding[n]):e(t.domainPadding)}(n,e);if(!i.left&&!i.right)return t;var a=Wt(t),u=Rt(t),c=function(t,n){return n?"x"===t?"y":"x":t}(e,n.horizontal),l=V(n,c),s=Math.abs(l[0]-l[1]),f=Math.max(s-i.left-i.right,1),p=Math.abs(u.valueOf()-a.valueOf())/f*s,h=p*i.left/s,d=p*i.right/s,g={min:a.valueOf()-h,max:u.valueOf()+d},v=en()(n.singleQuadrantDomainPadding)?n.singleQuadrantDomainPadding[e]:n.singleQuadrantDomainPadding,y=function(t,n){return"min"===n&&a>=0&&t<=0||"max"===n&&u<=0&&t>=0?0:t};if((a>=0&&g.min<=0||u<=0&&g.max>=0)&&!1!==v){var m={left:Math.abs(u-a)*i.left/s,right:Math.abs(u-a)*i.right/s},b={min:y(a.valueOf()-m.left,"min"),max:y(u.valueOf()+m.right,"max")},x={left:Math.abs(b.max-b.min)*i.left/s,right:Math.abs(b.max-b.min)*i.right/s};g={min:y(a.valueOf()-x.left,"min"),max:y(u.valueOf()+x.right,"max")}}var O={min:void 0!==r?r:g.min,max:void 0!==o?o:g.max};return a instanceof Date||u instanceof Date?pu(new Date(O.min),new Date(O.max)):pu(O.min,O.max)}(t,n,e),n,e)}function fu(t,n,e){var r=e||Ja(t),o=t.polar,i=t.startAngle,a=void 0===i?0:i,u=t.endAngle,c=void 0===u?360:u,l=gu(t,n),s=du(t,n);if(r.length<1)return void 0!==l&&void 0!==s?pu(l,s):void 0;var f=pu(void 0!==l?l:lu(r,n,"min"),void 0!==s?s:lu(r,n,"max"));return o&&"x"===n&&360===Math.abs(a-c)?vu(f,function(t,n){var e="_".concat(n);return t.flat().map((function(t){return t[e]&&void 0!==t[e][1]?t[e][1]:t[e]}))}(r,n)):f}function pu(t,n){var e,r,o,i;return Number(t)===Number(n)?(r=0===(e=n)?2*Math.pow(10,-10):Math.pow(10,-10),o=e instanceof Date?new Date(Number(e)-1):Number(e)-r,i=e instanceof Date?new Date(Number(e)+1):Number(e)+r,0===e?[0,i]:[o,i]):[t,n]}function hu(t,n){var e=gu(t,n),r=du(t,n);return en()(t.domain)&&t.domain[n]?t.domain[n]:Array.isArray(t.domain)?t.domain:void 0!==e&&void 0!==r?pu(e,r):void 0}function du(t,n){return en()(t.maxDomain)&&void 0!==t.maxDomain[n]?t.maxDomain[n]:"number"==typeof t.maxDomain||ou()(t.maxDomain)?t.maxDomain:void 0}function gu(t,n){return en()(t.minDomain)&&void 0!==t.minDomain[n]?t.minDomain[n]:"number"==typeof t.minDomain||ou()(t.minDomain)?t.minDomain:void 0}function vu(t,n){var e=au()(n.sort((function(t,n){return t-n}))),r=e[1]-e[0];return[t[0],t[1]+r]}function yu(t){var n=function(t){return t&&t.type?t.type.role:""},e=n(t);if("portal"===e){var r=c().Children.toArray(t.props.children);e=r.length?n(r[0]):""}return["area","axis","bar","boxplot","candlestick","errorbar","group","histogram","line","pie","scatter","stack","voronoi"].includes(e)}var mu=r(4912),bu=r.n(mu);function xu(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function Ou(t){return function(t){if(Array.isArray(t))return wu(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(t){if("string"==typeof t)return wu(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?wu(t,n):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function wu(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}var ju=/^onGlobal(.*)$/;function Au(t,n,e,r){var o,i=this,a=function(t){var r,o=(r=t.reduce((function(t,e){return void 0!==e.target?(Array.isArray(e.target)?e.target.includes(n):"".concat(e.target)==="".concat(n))?t.concat(e):t:t.concat(e)}),[]),void 0!==e&&"parent"!==n?r.filter((function(t){var n=t.eventKey,r=function(t){return!t||"".concat(t)==="".concat(e)};return Array.isArray(n)?n.some((function(t){return r(t)})):r(n)})):r);return Array.isArray(o)&&o.reduce((function(t,n){return n?Object.assign(t,n.eventHandlers):t}),{})},u=Array.isArray(i.componentEvents)?Array.isArray(t.events)?(o=i.componentEvents).concat.apply(o,Ou(t.events)):i.componentEvents:t.events,c=u&&Z(r)?r(a(u),n):void 0;if(!t.sharedEvents)return c;var l=t.sharedEvents.getEvents,s=t.sharedEvents.events&&l(a(t.sharedEvents.events),n);return Object.assign({},s,c)}function Mu(t,n,r,o){var i=this;if(e()(t))return{};var a=o||this.baseProps,u=function(t,n){var e=t.childName,r=t.target,o=t.key,u="props"===n?a:i.state||{},c=null!=e&&u[e]?u[e]:u;return"parent"===o?c.parent:c[o]&&c[o][r]},c=function(t,e){var o="parent"===n?t.childName:t.childName||r,c=t.target||n,l=function(n,e){var r=i.state||{};if(!Z(t.mutation))return r;var o=u({childName:e,key:n,target:c},"props"),l=u({childName:e,key:n,target:c},"state"),s=t.mutation(Object.assign({},o,l),a),f=r[e]||{},p=function(t){return s?function(t){return"parent"===c?Object.assign(t,xu({},n,Object.assign(t[n]||{},s))):Object.assign(t,xu({},n,Object.assign(t[n]||{},xu({},c,s))))}(t):function(t){return t[n]&&t[n][c]&&delete t[n][c],t[n]&&!Object.keys(t[n]).length&&delete t[n],t}(t)};return null!=e?Object.assign(r,xu({},e,p(f))):p(r)},s=function(n){var r=function(n){return"parent"===c?"parent":"all"===t.eventKey?a[n]?Object.keys(a[n]).filter((function(t){return"parent"!==t})):Object.keys(a).filter((function(t){return"parent"!==t})):void 0===t.eventKey&&"parent"===e?a[n]?Object.keys(a[n]):Object.keys(a):void 0!==t.eventKey?t.eventKey:e}(n);return Array.isArray(r)?r.reduce((function(t,e){return Object.assign(t,l(e,n))}),{}):l(r,n)},f="all"===o?Object.keys(a).filter((function(t){return"parent"!==t})):o;return Array.isArray(f)?f.reduce((function(t,n){return Object.assign(t,s(n))}),{}):s(f)},l=function(n,r,o,a){var u=t[a](n,r,o,i);if(!e()(u)){var l=function(t){var n=function(t){return Z(t.callback)&&t.callback},e=(Array.isArray(t)?t.map((function(t){return n(t)})):[n(t)]).filter((function(t){return!1!==t}));return e.length?function(){return e.forEach((function(t){return t()}))}:void 0}(u);i.setState(function(t,n){return Array.isArray(t)?t.reduce((function(t,e){return Object.assign({},t,c(e,n))}),{}):c(t,n)}(u,o),l)}};return Object.keys(t).reduce((function(t,n){return t[n]=l,t}),{})}function ku(t,n,e){var r=this.state||{};return e?r[e]&&r[e][t]&&r[e][t][n]:"parent"===t?r[t]&&r[t][n]||r[t]:r[t]&&r[t][n]}function Eu(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=arguments.length>3?arguments[3]:void 0,i=Object.keys(n);return i.reduce((function(i,a){var u=r[a]||{},c=n[a]||{};if("parent"===a){var l=Su(t,c,u,{eventKey:a,target:"parent"});i[a]=void 0!==l?Object.assign({},u,l):u}else{var s=ln()(Object.keys(c).concat(Object.keys(u)));i[a]=s.reduce((function(n,r){var i={eventKey:a,target:r,childName:o},l=Su(t,c[r],u[r],i);return n[r]=void 0!==l?Object.assign({},u[r],l):u[r],bu()(n,(function(t){return!e()(t)}))}),{})}return bu()(i,(function(t){return!e()(t)}))}),{})}function Su(t,n,r,o){var i=function(t,n){return"string"==typeof t[n]?"all"===t[n]||t[n]===o[n]:!!Array.isArray(t[n])&&t[n].map((function(t){return"".concat(t)})).includes(o[n])},a=Array.isArray(t)?t:[t];o.childName&&(a=t.filter((function(t){return i(t,"childName")})));var u=a.filter((function(t){return i(t,"target")}));if(!e()(u)){var c=u.filter((function(t){return i(t,"eventKey")}));if(!e()(c))return c.reduce((function(t,e){var o=(e&&Z(e.mutation)?e.mutation:function(){})(Object.assign({},n,r));return Object.assign({},t,o)}),{})}}function Du(t,n){var e=Array.isArray(n)&&n.reduce((function(n,e){var r=t[e],o=r&&r.type&&r.type.defaultEvents,i=Z(o)?o(r.props):o;return Array.isArray(i)?n.concat.apply(n,Ou(i)):n}),[]);return e&&e.length?e:void 0}function Cu(t){var n=t.match(ju);return n&&n[1]&&n[1].toLowerCase()}function _u(t){return function(t){if(Array.isArray(t))return Pu(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(t){if("string"==typeof t)return Pu(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Pu(t,n):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Pu(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function Tu(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),e.push.apply(e,r)}return e}function Nu(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?Tu(Object(e),!0).forEach((function(n){Uu(t,n,e[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):Tu(Object(e)).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}))}return t}function Uu(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function Fu(t,n,e){var r=t.datasets,o=V(t,t.horizontal?"y":"x"),i=Math.abs(o[1]-o[0]),a=void 0!==e?e:Array.isArray(r[0])&&r[0].length||1,u=(n||r.length)*a;return Math.round(.5*i/u)}function Lu(t,n,e){var r=e?e.slice(0):c().Children.toArray(t.children),o=t.data?Ja(t):void 0,i=t.polar,a=t.startAngle,u=t.endAngle,l=t.categories,s=t.minDomain,f=t.maxDomain,p={horizontal:t.horizontal,polar:i,startAngle:a,endAngle:u,minDomain:s,maxDomain:f,categories:l},h=o?Object.assign(p,{data:o}):p,d=J(r,(function(t){var e=Object.assign({},t.props,h);return yu(t)?t.type&&Z(t.type.getDomain)?t.props&&t.type.getDomain(e,n):function(t,n){return(o=Z(e)?e:fu,i=Z(r)?r:su,function(t,n){var e=hu(t,n);if(e)return i(e,t,n);var r=Xa(t,n),a=r?function(t,n,e){var r=e||Xa(t,n),o=t.polar,i=t.startAngle,a=void 0===i?0:i,u=t.endAngle,c=void 0===u?360:u;if(r){var l,s=gu(t,n),f=du(t,n),p=(l=r,Array.isArray(l)&&l.some((function(t){return"string"==typeof t}))?nu(t,n):[]),h=0===p.length?null:p.reduce((function(t,n,e){return t[n]=e+1,t}),{}),d=h?r.map((function(t){return h[t]})):r,g=pu(void 0!==s?s:Wt(d),void 0!==f?f:Rt(d));return o&&"x"===n&&360===Math.abs(a-c)?vu(g,d):g}}(t,n,r):o(t,n);return a?i(a,t,n):void 0})(t,n);var e,r,o,i}(e,n):null}),t);return[0===d.length?0:Wt(d),0===d.length?1:Rt(d)]}function Iu(t,n,e){var r,o=e||c().Children.toArray(t.children),i=hu(t,n),a=function(t,n,e){if(!t.polar&&"x"===n){var r=e.filter((function(t){return t.type&&t.type.role&&"group"===t.type.role}));if(!(r.length<1)){var o=r[0].props,i=o.offset,a=o.children;if(i){var u=Array.isArray(a)&&a[0];if(u){var c=u.props.barWidth,l=u.props.data&&u.props.data.length||1;if(u&&"stack"===u.type.role){var s=u.props.children&&u.props.children[0];if(!s)return;c=s.props.barWidth,l=u.props.children.length}var f=c||Fu(t,a.length,l);return{x:f*a.length/2+(i-f*((a.length-1)/2))}}}}}}(t,n,o);if(i)r=i;else{var u=gu(t,n),l=du(t,n),s=(t.data||t.y)&&Ja(t),f=s?fu(t,n,s):[],p=Lu(t,n,o);r=pu(u||Wt([].concat(_u(f),_u(p))),l||Rt([].concat(_u(f),_u(p))))}return su(r,Object.assign({domainPadding:a},t),n)}function Ru(t,n,e){if(t.data)return Ra(t,n);var r=e?e.slice(0):c().Children.toArray(t.children),o=ln()(J(r,(function(e){return $a(Object.assign({},e.props,{horizontal:t.horizontal}),n)}),t));return o.length>1?Ia("linear"):Ia(o[0])}function Wu(t,n,e){var r=en()(t.categories)?t.categories[n]:t.categories,o=hn(e,n),i=o?tu(o.props,n):[],a=r||function(t,n){return J(t.slice(0),(function(t){var e=t.props||{};if(!yu(t)||!e.categories)return null;var r=e.categories&&!Array.isArray(e.categories)?e.categories[n]:e.props.categories,o=r&&r.filter((function(t){return"string"==typeof t}));return o?It(o):[]}))}(e,n);return ln()([].concat(_u(a),_u(i)).flat())}var $u=r(7314),Bu=r.n($u),qu=r(7491),zu=r.n(qu),Hu=r(2516),Yu=r.n(Hu);function Gu(t,n){if(t){if("string"==typeof t)return Ku(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Ku(t,n):void 0}}function Ku(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function Vu(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function Zu(t,n){return Zu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},Zu(t,n)}function Qu(t,n){if(n&&("object"==typeof n||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return Xu(t)}function Xu(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Ju(t){return Ju=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Ju(t)}var tc=function(t){!function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),n&&Zu(t,n)}(l,t);var n,r,o,i,u=(o=l,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,n=Ju(o);if(i){var e=Ju(this).constructor;t=Reflect.construct(n,arguments,e)}else t=n.apply(this,arguments);return Qu(this,t)});function l(t){var n;return function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,l),(n=u.call(this,t)).getScopedEvents=void 0,n.getEventState=void 0,n.baseProps=void 0,n.sharedEventsCache=void 0,n.globalEvents=void 0,n.prevGlobalEventKeys=void 0,n.boundGlobalEvents=void 0,n.getScopedEvents=Mu.bind(Xu(n)),n.getEventState=ku.bind(Xu(n)),n.state=n.state||{},n.sharedEventsCache={},n.globalEvents={},n.prevGlobalEventKeys=[],n.boundGlobalEvents={},n.baseProps=n.getBaseProps(t),n}return n=l,r=[{key:"shouldComponentUpdate",value:function(t){if(!zu()(this.props,t)){this.baseProps=this.getBaseProps(t);var n=this.getExternalMutations(t,this.baseProps);this.applyExternalMutations(t,n)}return!0}},{key:"componentDidMount",value:function(){var t=this,n=Object.keys(this.globalEvents);n.forEach((function(n){return t.addGlobalListener(n)})),this.prevGlobalEventKeys=n}},{key:"componentDidUpdate",value:function(){var t=this,n=Object.keys(this.globalEvents);Ft(this.prevGlobalEventKeys,n).forEach((function(n){return t.removeGlobalListener(n)})),Ft(n,this.prevGlobalEventKeys).forEach((function(n){return t.addGlobalListener(n)})),this.prevGlobalEventKeys=n}},{key:"componentWillUnmount",value:function(){var t=this;this.prevGlobalEventKeys.forEach((function(n){return t.removeGlobalListener(n)}))}},{key:"addGlobalListener",value:function(t){var n=this,e=function(e){var r=n.globalEvents[t];return r&&r(function(t){return Object.assign(t,{nativeEvent:t})}(e))};this.boundGlobalEvents[t]=e,window.addEventListener(Cu(t),e)}},{key:"removeGlobalListener",value:function(t){window.removeEventListener(Cu(t),this.boundGlobalEvents[t])}},{key:"getAllEvents",value:function(t){var n,e=Du(t,["container","groupComponent"]);return Array.isArray(e)?Array.isArray(t.events)?e.concat.apply(e,function(t){if(Array.isArray(t))return Ku(t)}(n=t.events)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||Gu(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):e:t.events}},{key:"applyExternalMutations",value:function(t,n){if(!e()(n)){var r=t.externalEventMutations.reduce((function(t,n){return Z(n.callback)?t.concat(n.callback):t}),[]),o=r.length?function(){r.forEach((function(t){return t()}))}:void 0;this.setState(n,o)}}},{key:"getExternalMutations",value:function(t,n){return e()(t.externalEventMutations)?void 0:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return(arguments.length>3?arguments[3]:void 0).reduce((function(o,i){var a=r[i],u=Eu(t,n[i],r[i],i);return o[i]=u||a,bu()(o,(function(t){return!e()(t)}))}),{})}(t.externalEventMutations,n,this.state,Object.keys(n))}},{key:"cacheSharedEvents",value:function(t,n,e){this.sharedEventsCache[t]=[n,e]}},{key:"getCachedSharedEvents",value:function(t,n){var e,r,o=(e=this.sharedEventsCache[t]||[],r=2,function(t){if(Array.isArray(t))return t}(e)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,o,i=[],a=!0,u=!1;try{for(e=e.call(t);!(a=(r=e.next()).done)&&(i.push(r.value),!n||i.length!==n);a=!0);}catch(t){u=!0,o=t}finally{try{a||null==e.return||e.return()}finally{if(u)throw o}}return i}}(e,r)||Gu(e,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),i=o[0],a=o[1];if(i&&zu()(n,a))return i}},{key:"getBaseProps",value:function(t){var n=t.container,e=c().Children.toArray(this.props.children),r=this.getBasePropsFromChildren(e),o=n?n.props:{};return Object.assign({},r,{parent:o})}},{key:"getBasePropsFromChildren",value:function(t){var n=J(t,(function(t,n){if(t.type&&Z(t.type.getBaseProps)){var e=t.props&&t.type.getBaseProps(t.props);return e?[[n,e]]:null}return null}));return Bu()(n)}},{key:"getNewChildren",value:function(t,n){var e=this,r=t.events,o=t.eventKey,i=function(t,a){return t.reduce((function(t,u,l){if(u.props.children){var s=c().Children.toArray(u.props.children),f=a.slice(l,l+s.length),p=c().cloneElement(u,u.props,i(s,f));return t.concat(p)}if("parent"!==a[l]&&u.type&&Z(u.type.getBaseProps)){var h=u.props.name||a[l],d=Array.isArray(r)&&r.filter((function(t){return"parent"!==t.target&&(Array.isArray(t.childName)?t.childName.indexOf(h)>-1:t.childName===h||"all"===t.childName)})),g=[h,n,d,Yu()(e.state[h])],v=e.getCachedSharedEvents(h,g)||{events:d,getEvents:function(t,r){return e.getScopedEvents(t,r,h,n)},getEventState:function(t,n){return e.getEventState(t,n,h)}};return e.cacheSharedEvents(h,v,g),t.concat(c().cloneElement(u,Object.assign({key:"events-".concat(h),sharedEvents:v,eventKey:o,name:h},u.props)))}return t.concat(u)}),[])},a=Object.keys(n),u=c().Children.toArray(t.children);return i(u,a)}},{key:"getContainer",value:function(t,n,e){var r=this,o=this.getNewChildren(t,n),i=Array.isArray(e)?e.filter((function(t){return"parent"===t.target})):[],u=i.length>0?{events:i,getEvents:function(t,e){return r.getScopedEvents(t,e,null,n)},getEventState:this.getEventState}:null,l=t.container||t.groupComponent,s=l.type&&l.type.role,f=l.props||{},p=Au.bind(this),h=u&&p({sharedEvents:u},"parent"),d=a()({},this.getEventState("parent","parent"),f,n.parent,{children:o}),g=a()({},function(t,n,e){return t?Object.keys(t).reduce((function(n,r){return n[r]=function(n){return t[r](n,e,"parent",r)},n}),{}):{}}(h,0,d),f.events);this.globalEvents=function(t){return bu()(t,(function(t,n){return ju.test(n)}))}(g);var v=function(t){return gn()(t,(function(t,n){return ju.test(n)}))}(g);return"container"===s?c().cloneElement(l,Object.assign({},d,{events:v})):c().cloneElement(l,v,o)}},{key:"render",value:function(){var t=this.getAllEvents(this.props);return t?this.getContainer(this.props,this.baseProps,t):c().cloneElement(this.props.container,{children:this.props.children})}}],r&&Vu(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),l}(c().Component);function nc(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function ec(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),e.push.apply(e,r)}return e}function rc(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?ec(Object(e),!0).forEach((function(n){oc(t,n,e[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):ec(Object(e)).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}))}return t}function oc(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}tc.displayName="VictorySharedEvents",tc.role="shared-event-wrapper",tc.contextType=R,tc.defaultProps={groupComponent:c().createElement("g",null)};var ic={width:450,height:300,padding:50};function ac(t,n){var e=function(t,n){var e={polar:t.polar,startAngle:t.startAngle,endAngle:t.endAngle,categories:t.categories,minDomain:t.minDomain,maxDomain:t.maxDomain},r=0,o=n?n.slice(0):c().Children.toArray(t.children);e=function(t){var n=t.children,e=t.props,r=t.childComponents,o=t.parentProps;if(!(n.some((function(t){return t.type&&"histogram"===t.type.role}))&&n.length&&n.every((function(t){return t.type&&"histogram"===t.type.role}))))return o;var i=e.bins||r[0].props.bins;if(!Array.isArray(i)){var a=n.reduce((function(t,n){var e=Q(n.props.x||"x");return t.concat(n.props.data.map((function(t){return{x:e(t)}})))}),[]);i=(0,n[0].type.getFormattedData)({data:a,bins:i}).reduce((function(t,n,e){var r=n.x0,o=n.x1;return 0===e?t.concat([r,o]):t.concat(o)}),[])}return Nu(Nu({},o),{},{bins:i})}({children:o,props:t,childComponents:n,parentProps:e});var i=o.filter((function(t){return t.type&&"stack"===t.type.role})).length,a=J(o,(function(t,n,o){var i,a=Object.assign({},t.props,e);return eu(t)?(i=t.type&&Z(t.type.getData)?(o?c().cloneElement(t,o.props):t).type.getData(a):Ja(a),r+=1,i.map((function(t,n){return Object.assign({_stack:r,_group:n},t)}))):null}),t,[],(function(t,n){return t.concat(on()(n,"_group"))})),u=i?"_group":"_stack";return Object.values(un()(a,u))}(t,n).map((function(t){return t.filter((function(t){return null!==t._x&&null!==t._y}))})),r=function(t,n){var e=t.fillInMissingData,r=n.reduce((function(t,n){return n.forEach((function(n){t[n._x instanceof Date?n._x.getTime():n._x]=!0})),t}),{}),o=Object.keys(r).map((function(t){return Number(t)})),i=fn()(o);return n.map((function(t){var n=0,r=t[0]&&t[0]._x instanceof Date;return i.map((function(o,i){var a=Number(o),u=t[i-n];if(u){if((r?u._x.getTime():u._x)===a)return u;n++;var c=e?0:null;return{x:a=r?new Date(a):a,y:c,_x:a,_y:c}}var l=e?0:null;return{x:a=r?new Date(a):a,y:l,_x:a,_y:l}}))}))}(t,e);return r.map((function(n,e){return function(t,n,e){var r=t.xOffset||0;return n[e].map((function(t){var o=function(t,n,e){if(t.y0)return t.y0;var r=t._y,o=t._group,i=e[0].map((function(t){return t.y0})),a=e.slice(0,n).reduce((function(n,e){return n.concat(e.filter((function(n){return t._x instanceof Date?n._x.getTime()===t._x.getTime():n._x===t._x})).map((function(t){return t._y||0})))}),[]),u=a.length&&a.reduce((function(t,n){return r<0&&n<0||r>=0&&n>=0?Number(n)+t:t}),i[o]||0);return a.some((function(t){return t instanceof Date}))?new Date(u):u}(t,e,n)||0;return Object.assign({},t,{_y0:t._y instanceof Date?o?new Date(o):t._y:o,_y1:null===t._y?null:t._y instanceof Date?new Date(Number(t._y)+Number(o)):t._y+o,_x1:null===t._x?null:t._x instanceof Date?new Date(Number(t._x)+Number(r)):t._x+r})}))}(t,r,e)}))}function uc(t,n){var e=n||c().Children.toArray(t.children),r="stack",o=X(t,ic,r),i=function(t,n,e){return function(t,n){var e="100%",r="100%";if(!t)return a()({parent:{height:r,width:e}},n);var o=t.data,i=t.labels,u=t.parent,c=n&&n.parent||{},l=n&&n.labels||{},s=n&&n.data||{};return{parent:a()({},u,c,{width:e,height:r}),labels:a()({},i,l),data:a()({},o,s)}}(n,t&&t[e]&&t[e].style?t[e].style:{})}(o.theme,o.style,r),u=o.categories||function(t,n,e){var r=t.categories&&!Array.isArray(t.categories)?t.categories.x:t.categories,o=t.categories&&!Array.isArray(t.categories)?t.categories.y:t.categories,i=r&&o?{}:function(t,n){var e=n||c().Children.toArray(t.children),r=Wu(t,"x",e),o=Wu(t,"y",e),i=function(t){return J(t.slice(0),(function(t){var n=t.props||{};return eu(t)?(t.type&&Z(t.type.getData)?t.type.getData(n):Ja(n)).map((function(t){return{x:t.xName,y:t.yName}})):null}),{},{x:[],y:[]},(function(t,n){var e=Array.isArray(n)?n.map((function(t){return t.x})).filter(Boolean):n.x,r=Array.isArray(n)?n.map((function(t){return t.y})).filter(Boolean):n.y;return{x:void 0!==e?t.x.concat(e):t.x,y:void 0!==r?t.y.concat(r):t.y}}))}(e);return{x:ln()([].concat(_u(r),_u(i.x)).flat()),y:ln()([].concat(_u(o),_u(i.y)).flat())}}(t,n),a=r||i.x,u=o||i.y;return{x:a.length>0?a:void 0,y:u.length>0?u:void 0}}(o,e),l=o.datasets||ac(o,e),s=e.map((function(t,n){return c().cloneElement(t,{data:l[n]})})),f={x:Iu(Object.assign({},o,{categories:u}),"x",s),y:Iu(Object.assign({},o,{categories:u}),"y",s)},p=o.range||{x:V(o,"x"),y:V(o,"y")},h={x:Wa(o,"x")||Ru(o,"x"),y:Wa(o,"y")||Ru(o,"y")},d={x:h.x.domain(f.x).range(o.horizontal?p.y:p.x),y:h.y.domain(f.y).range(o.horizontal?p.x:p.y)},g=o.colorScale,v=o.horizontal;return{datasets:l,categories:u,range:p,domain:f,horizontal:v,scale:d,style:i,colorScale:g,role:r}}function cc(t){var n,e,r=function(t){var n=t.children,e=c().Children.toArray(n).map((function(t){var n=t;return rc(rc({},n),{},{props:z(n.props,["sharedEvents"])})}));return t.children=e,t}(t),o=(n=c().useState(r),e=2,function(t){if(Array.isArray(t))return t}(n)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,o,i=[],a=!0,u=!1;try{for(e=e.call(t);!(a=(r=e.next()).done)&&(i.push(r.value),!n||i.length!==n);a=!0);}catch(t){u=!0,o=t}finally{try{a||null==e.return||e.return()}finally{if(u)throw o}}return i}}(n,e)||function(t,n){if(t){if("string"==typeof t)return nc(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?nc(t,n):void 0}}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),i=o[0],a=o[1];return c().useEffect((function(){zu()(r,i)||a(r)}),[i,a,r]),c().useMemo((function(){return uc(i,i.children)}),[i])}function lc(t,n){var e=n.type&&n.type.role,r=n.props.colorScale||t.colorScale;if("group"===e||"stack"===e)return t.theme?r||t.theme.props.colorScale:r}function sc(t,n,e){var r=X(t,ic,"stack"),o=n||c().Children.toArray(r.children),i=e||uc(r,o),u=i.datasets,l=function(t,n){var e=n.categories,r=n.domain,o=n.range,i=n.scale,a=n.horizontal;return{height:t.height,width:t.width,padding:H(t),standalone:!1,theme:t.theme,categories:e,domain:r,range:o,scale:i,horizontal:a}}(r,i),s=r.name||"stack";return o.map((function(t,n){var e=t.type&&t.type.role,o=u[n],f=function(t,n,e){var r=e.style,o=e.role,i=t.props.style||{};if(Array.isArray(i))return i;var u=t.type&&t.type.role,c="stack"===u?void 0:function(t,n,e){var r=t.style,o=t.colorScale,i=t.color;if(r&&r.data&&r.data.fill)return r.data.fill;if(o=n.props&&n.props.colorScale?n.props.colorScale:o,i=n.props&&n.props.color?n.props.color:i,o||i){var a,u,c=Array.isArray(o)?o:(u={grayscale:["#cccccc","#969696","#636363","#252525"],qualitative:["#334D5C","#45B29D","#EFC94C","#E27A3F","#DF5A49","#4F7DA1","#55DBC1","#EFDA97","#E2A37F","#DF948A"],heatmap:["#428517","#77D200","#D6D305","#EC8E19","#C92B05"],warm:["#940031","#C43343","#DC5429","#FF821D","#FFAF55"],cool:["#2746B9","#0B69D4","#2794DB","#31BB76","#60E83B"],red:["#FCAE91","#FB6A4A","#DE2D26","#A50F15","#750B0E"],blue:["#002C61","#004B8F","#006BC9","#3795E5","#65B4F4"],green:["#354722","#466631","#649146","#8AB25C","#A9C97E"]},(a=o)?u[a]:u.grayscale);return i||c[e%c.length]}}(e,t,n),l="line"===u?{fill:"none",stroke:c}:{fill:c},s="stack"===o?{}:{width:Fu(e)},f=a()({},i.data,Object.assign({},s,r.data,l)),p=a()({},i.labels,r.labels);return Nu(Nu({},i),{},{parent:r.parent,data:f,labels:p})}(t,n,i),p=r.labels?function(t,n,e){if(t.labels)return n.length===e+1?t.labels:void 0}(r,u,n):t.props.labels,h=t.props.name||"".concat(s,"-").concat(e,"-").concat(n);return c().cloneElement(t,Object.assign({key:"".concat(h,"-key-").concat(n),labels:p,name:h,domainPadding:t.props.domainPadding||r.domainPadding,theme:r.theme,labelComponent:r.labelComponent||t.props.labelComponent,style:f,colorScale:lc(r,t),data:o,polar:r.polar},l))}))}function fc(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),e.push.apply(e,r)}return e}function pc(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?fc(Object(e),!0).forEach((function(n){hc(t,n,e[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):fc(Object(e)).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}))}return t}function hc(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}var dc={width:450,height:300,padding:50},gc={containerComponent:c().createElement(dt,null),groupComponent:c().createElement("g",null),standalone:!0,theme:Pt.grayscale,fillInMissingData:!0},vc=function(t){var n=mc.role,r=c().useMemo((function(){return pc(pc({},gc),t)}),[t]),o=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:tn,n=Xt(c().useState(t),2),e=n[0],r=n[1],o=c().useCallback((function(t){r((function(n){return Zt(Zt({},n),t)}))}),[r]),i=c().useCallback((function(t,n,r){if(null==t||!t.animate)return n.props.animate;var i,u=t.animate&&t.animate.getTransitions,c=(i=Lt(i=e&&e.childrenTransitions)?i[r]:i,a()({childrenTransitions:i},e)),l=t.animate&&t.animate.parentState||c;if(!u){var s=Kt(t,c,(function(t){return o(t)}));u=function(t){return s(t,r)}}return a()({getTransitions:u,parentState:l},t.animate,n.props.animate)}),[e,o]),u=c().useCallback((function(t,n){if(null!=t&&t.animate)if(t.animate.parentState){var e=t.animate.parentState.nodesWillExit?t:null,r=a()({oldProps:e,nextProps:n},t.animate.parentState);o(r)}else{var i=c().Children.toArray(t.children),u=c().Children.toArray(n.children),l=function(t){var n=function(t){return t.type&&t.type.continuous};return Array.isArray(t)?t.some(n):n(t)},s=!t.polar&&i.some((function(t){var n;return l(t)||(null==t||null===(n=t.props)||void 0===n?void 0:n.children)&&l(t.props.children)})),f=Gt(i,u),p=f.nodesWillExit,h=f.nodesWillEnter,d=f.childrenTransitions,g=f.nodesShouldEnter;o({nodesWillExit:p,nodesWillEnter:h,nodesShouldEnter:g,childrenTransitions:Lt(d)?d[0]:d,oldProps:p?t:void 0,nextProps:n,continuous:s})}}),[o]),l=c().useCallback((function(t){return e&&e.nodesWillExit&&e.oldProps||t}),[e]);return{state:e,setState:o,getAnimationProps:i,setAnimationState:u,getProps:l}}(),i=o.setAnimationState,u=o.getAnimationProps,l=(0,o.getProps)(r),s=X(l,dc,n),f=s.eventKey,p=s.containerComponent,h=s.standalone,d=s.groupComponent,g=s.externalEventMutations,v=s.width,y=s.height,m=s.theme,b=s.polar,x=s.horizontal,O=s.name,w=c().Children.toArray(s.children),j=cc(s),A=j.domain,M=j.scale,k=j.style,E=c().useMemo((function(){return sc(l,w,j).map((function(t,n){var e=Object.assign({animate:u(l,t,n)},t.props);return c().cloneElement(t,e)})).reverse()}),[l,w,j,u]),S=c().useMemo((function(){return h?{domain:A,scale:M,width:v,height:y,standalone:h,theme:m,style:k.parent,horizontal:x,polar:b,name:O}:{}}),[h,A,M,v,y,m,k,x,b,O]),D=c().useMemo((function(){return it(r)}),[r]),C=c().useMemo((function(){if(h){var t=a()({},p.props,S,D);return c().cloneElement(p,t)}return c().cloneElement(d,D)}),[d,h,p,S,D]),_=c().useMemo((function(){return function(t){var n=Du(t,["groupComponent","containerComponent","labelComponent"]),e=t.events;return Array.isArray(n)&&(e=Array.isArray(t.events)?n.concat.apply(n,_u(t.events)):n),e||[]}(l)}),[l]),P=function(t){var n=c().useRef();return c().useEffect((function(){n.current=t})),n.current}(r);return c().useEffect((function(){return function(){r.animate&&i(P,r)}}),[i,P,r]),e()(_)?c().cloneElement(C,C.props,E):c().createElement(tc,{container:C,eventKey:f,events:_,externalEventMutations:g},E)},yc={role:"stack",expectedComponents:["groupComponent","containerComponent","labelComponent"],getChildren:sc},mc=Object.assign(c().memo(vc,zu()),yc);mc.displayName="VictoryStack"})(),o})()));
//# sourceMappingURL=victory-stack.min.js.map