{"global": {"borderRadius": {"name": "基础圆角", "nameEn": "Base Border Radius", "desc": "基础组件的圆角大小，例如按钮、输入框、卡片等", "descEn": "Border radius of base components", "type": "number", "source": "seed"}, "colorBgBase": {"name": "基础背景色", "nameEn": "Seed Background Color", "desc": "用于派生背景色梯度的基础变量，v5 中我们添加了一层背景色的派生算法可以产出梯度明确的背景色的梯度变量。但请不要在代码中直接使用该 Seed Token ！", "descEn": "Used to derive the base variable of the background color gradient. In v5, we added a layer of background color derivation algorithm to produce map token of background color. But PLEASE DO NOT USE this Seed Token directly in the code!", "type": "string", "source": "seed"}, "colorError": {"name": "错误色", "nameEn": "Error Color", "desc": "用于表示操作失败的 Token 序列，如失败按钮、错误状态提示（Result）组件等。", "descEn": "Used to represent the visual elements of the operation failure, such as the error Button, error Result component, etc.", "type": "string", "source": "seed"}, "colorInfo": {"name": "信息色", "nameEn": "Info Color", "desc": "用于表示操作信息的 Token 序列，如 Alert 、Tag、 Progress 等组件都有用到该组梯度变量。", "descEn": "Used to represent the operation information of the Token sequence, such as Alert, Tag, Progress, and other components use these map tokens.", "type": "string", "source": "seed"}, "colorLink": {"name": "超链接颜色", "nameEn": "Hyperlink color", "desc": "控制超链接的颜色。", "descEn": "Control the color of hyperlink.", "type": "string", "source": "seed"}, "colorPrimary": {"name": "品牌主色", "nameEn": "Brand Color", "desc": "品牌色是体现产品特性和传播理念最直观的视觉元素之一。在你完成品牌主色的选取之后，我们会自动帮你生成一套完整的色板，并赋予它们有效的设计语义", "descEn": "Brand color is one of the most direct visual elements to reflect the characteristics and communication of the product. After you have selected the brand color, we will automatically generate a complete color palette and assign it effective design semantics.", "type": "string", "source": "seed"}, "colorSuccess": {"name": "成功色", "nameEn": "Success Color", "desc": "用于表示操作成功的 Token 序列，如 Result、Progress 等组件会使用该组梯度变量。", "descEn": "Used to represent the token sequence of operation success, such as Result, Progress and other components will use these map tokens.", "type": "string", "source": "seed"}, "colorTextBase": {"name": "基础文本色", "nameEn": "Seed Text Color", "desc": "用于派生文本色梯度的基础变量，v5 中我们添加了一层文本色的派生算法可以产出梯度明确的文本色的梯度变量。但请不要在代码中直接使用该 Seed Token ！", "descEn": "Used to derive the base variable of the text color gradient. In v5, we added a layer of text color derivation algorithm to produce gradient variables of text color gradient. But please do not use this Seed Token directly in the code!", "type": "string", "source": "seed"}, "colorWarning": {"name": "警戒色", "nameEn": "Warning Color", "desc": "用于表示操作警告的 Token 序列，如 Notification、 Alert等警告类组件或 Input 输入类等组件会使用该组梯度变量。", "descEn": "Used to represent the warning map token, such as Notification, Alert, etc. Alert or Control component(like Input) will use these map tokens.", "type": "string", "source": "seed"}, "controlHeight": {"name": "基础高度", "nameEn": "Base Control Height", "desc": "Ant Design 中按钮和输入框等基础控件的高度", "descEn": "The height of the basic controls such as buttons and input boxes in Ant Design", "type": "number", "source": "seed"}, "fontFamily": {"name": "字体", "nameEn": "Font family for default text", "desc": "Ant Design 的字体家族中优先使用系统默认的界面字体，同时提供了一套利于屏显的备用字体库，来维护在不同平台以及浏览器的显示下，字体始终保持良好的易读性和可读性，体现了友好、稳定和专业的特性。", "descEn": "The font family of Ant Design prioritizes the default interface font of the system, and provides a set of alternative font libraries that are suitable for screen display to maintain the readability and readability of the font under different platforms and browsers, reflecting the friendly, stable and professional characteristics.", "type": "string", "source": "seed"}, "fontFamilyCode": {"name": "代码字体", "nameEn": "Font family for code text", "desc": "代码字体，用于 Typography 内的 code、pre 和 kbd 类型的元素", "descEn": "Code font, used for code, pre and kbd elements in Typography", "type": "string", "source": "seed"}, "fontSize": {"name": "默认字号", "nameEn": "<PERSON><PERSON><PERSON>ont Si<PERSON>", "desc": "设计系统中使用最广泛的字体大小，文本梯度也将基于该字号进行派生。", "descEn": "The most widely used font size in the design system, from which the text gradient will be derived.", "type": "number", "source": "seed"}, "lineType": {"name": "线条样式", "nameEn": "Line Style", "desc": "用于控制组件边框、分割线等的样式，默认是实线", "descEn": "Border style of base components", "type": "string", "source": "seed"}, "lineWidth": {"name": "基础线宽", "nameEn": "Base Line Width", "desc": "用于控制组件边框、分割线等的宽度", "descEn": "Border width of base components", "type": "number", "source": "seed"}, "motion": {"name": "动画风格", "nameEn": "Motion Style", "desc": "用于配置动画效果，为 `false` 时则关闭动画", "descEn": "Used to configure the motion effect, when it is `false`, the motion is turned off", "type": "boolean", "source": "seed"}, "motionBase": {"name": "动画基础时长。", "nameEn": "Animation Base Duration.", "desc": "", "descEn": "", "type": "number", "source": "seed"}, "motionEaseInBack": {"name": "", "nameEn": "", "desc": "预设动效曲率", "descEn": "Preset motion curve.", "type": "string", "source": "seed"}, "motionEaseInOut": {"name": "", "nameEn": "", "desc": "预设动效曲率", "descEn": "Preset motion curve.", "type": "string", "source": "seed"}, "motionEaseInOutCirc": {"name": "", "nameEn": "", "desc": "预设动效曲率", "descEn": "Preset motion curve.", "type": "string", "source": "seed"}, "motionEaseInQuint": {"name": "", "nameEn": "", "desc": "预设动效曲率", "descEn": "Preset motion curve.", "type": "string", "source": "seed"}, "motionEaseOut": {"name": "", "nameEn": "", "desc": "预设动效曲率", "descEn": "Preset motion curve.", "type": "string", "source": "seed"}, "motionEaseOutBack": {"name": "", "nameEn": "", "desc": "预设动效曲率", "descEn": "Preset motion curve.", "type": "string", "source": "seed"}, "motionEaseOutCirc": {"name": "", "nameEn": "", "desc": "预设动效曲率", "descEn": "Preset motion curve.", "type": "string", "source": "seed"}, "motionEaseOutQuint": {"name": "", "nameEn": "", "desc": "预设动效曲率", "descEn": "Preset motion curve.", "type": "string", "source": "seed"}, "motionUnit": {"name": "动画时长变化单位", "nameEn": "Animation Duration Unit", "desc": "用于控制动画时长的变化单位", "descEn": "The unit of animation duration change", "type": "number", "source": "seed"}, "opacityImage": {"name": "图片不透明度", "nameEn": "Define default Image opacity. Useful when in dark-like theme", "desc": "控制图片不透明度", "descEn": "Control image opacity", "type": "number", "source": "seed"}, "sizePopupArrow": {"name": "组件箭头尺寸", "nameEn": "", "desc": "组件箭头的尺寸", "descEn": "The size of the component arrow", "type": "number", "source": "seed"}, "sizeStep": {"name": "尺寸步长", "nameEn": "Size Base Step", "desc": "用于控制组件尺寸的基础步长，尺寸步长结合尺寸变化单位，就可以派生各种尺寸梯度。通过调整步长即可得到不同的布局模式，例如 V5 紧凑模式下的尺寸步长为 2", "descEn": "The base step of size change, the size step combined with the size change unit, can derive various size steps. By adjusting the step, you can get different layout modes, such as the size step of the compact mode of V5 is 2", "type": "number", "source": "seed"}, "sizeUnit": {"name": "尺寸变化单位", "nameEn": "Size Change Unit", "desc": "用于控制组件尺寸的变化单位，在 Ant Design 中我们的基础单位为 4 ，便于更加细致地控制尺寸梯度", "descEn": "The unit of size change, in Ant Design, our base unit is 4, which is more fine-grained control of the size step", "type": "number", "source": "seed"}, "wireframe": {"name": "线框风格", "nameEn": "Wireframe Style", "desc": "用于将组件的视觉效果变为线框化，如果需要使用 V4 的效果，需要开启配置项", "descEn": "Used to change the visual effect of the component to wireframe, if you need to use the V4 effect, you need to enable the configuration item", "type": "boolean", "source": "seed"}, "zIndexBase": {"name": "基础 zIndex", "nameEn": "Base zIndex", "desc": "所有组件的基础 Z 轴值，用于一些悬浮类的组件的可以基于该值 Z 轴控制层级，例如 BackTop、 Affix 等", "descEn": "The base Z axis value of all components, which can be used to control the level of some floating components based on the Z axis value, such as BackTop, Affix, etc.", "type": "number", "source": "seed"}, "zIndexPopupBase": {"name": "浮层基础 zIndex", "nameEn": "popup base zIndex", "desc": "浮层类组件的基础 Z 轴值，用于一些悬浮类的组件的可以基于该值 Z 轴控制层级，例如 FloatButton、 Affix、Modal 等", "descEn": "Base zIndex of component like FloatButton, Affix which can be cover by large popup", "type": "number", "source": "seed"}, "borderRadiusLG": {"name": "LG号圆角", "nameEn": "LG Border Radius", "desc": "LG号圆角，用于组件中的一些大圆角，如 Card、Modal 等一些组件样式。", "descEn": "LG size border radius, used in some large border radius components, such as Card, Modal and other components.", "type": "number", "source": "map"}, "borderRadiusOuter": {"name": "外部圆角", "nameEn": "Outer Border Radius", "desc": "外部圆角", "descEn": "Outer border radius", "type": "number", "source": "map"}, "borderRadiusSM": {"name": "SM号圆角", "nameEn": "SM Border Radius", "desc": "SM号圆角，用于组件小尺寸下的圆角，如 Button、Input、Select 等输入类控件在 small size 下的圆角", "descEn": "SM size border radius, used in small size components, such as Button, Input, Select and other input components in small size", "type": "number", "source": "map"}, "borderRadiusXS": {"name": "XS号圆角", "nameEn": "XS Border Radius", "desc": "XS号圆角，用于组件中的一些小圆角，如 Segmented 、Arrow 等一些内部圆角的组件样式中。", "descEn": "XS size border radius, used in some small border radius components, such as Segmented, Arrow and other components with small border radius.", "type": "number", "source": "map"}, "colorBgBlur": {"name": "毛玻璃容器背景色", "nameEn": "Frosted glass container background color", "desc": "控制毛玻璃容器的背景色，通常为透明色。", "descEn": "Control the background color of frosted glass container, usually transparent.", "type": "string", "source": "map"}, "colorBgContainer": {"name": "组件容器背景色", "nameEn": "", "desc": "组件的容器背景色，例如：默认按钮、输入框等。务必不要将其与 `colorBgElevated` 混淆。", "descEn": "Container background color, e.g: default button, input box, etc. Be sure not to confuse this with `colorBgElevated`.", "type": "string", "source": "map"}, "colorBgElevated": {"name": "浮层容器背景色", "nameEn": "", "desc": "浮层容器背景色，在暗色模式下该 token 的色值会比 `colorBgContainer` 要亮一些。例如：模态框、弹出框、菜单等。", "descEn": "Container background color of the popup layer, in dark mode the color value of this token will be a little brighter than `colorBgContainer`. E.g: modal, pop-up, menu, etc.", "type": "string", "source": "map"}, "colorBgLayout": {"name": "布局背景色", "nameEn": "Layout Background Color", "desc": "该色用于页面整体布局的背景色，只有需要在页面中处于 B1 的视觉层级时才会使用该 token，其他用法都是错误的", "descEn": "This color is used for the background color of the overall layout of the page. This token will only be used when it is necessary to be at the B1 visual level in the page. Other usages are wrong.", "type": "string", "source": "map"}, "colorBgMask": {"name": "浮层的背景蒙层颜色", "nameEn": "Background color of the mask", "desc": "浮层的背景蒙层颜色，用于遮罩浮层下面的内容，Modal、Drawer 等组件的蒙层使用的是该 token", "descEn": "The background color of the mask, used to cover the content below the mask, Modal, Drawer and other components use this token", "type": "string", "source": "map"}, "colorBgSolid": {"name": "", "nameEn": "", "desc": "实心的背景颜色，目前只用在默认实心按钮背景色上。", "descEn": "Solid background color, currently only used for the default solid button background color.", "type": "string", "source": "map"}, "colorBgSolidActive": {"name": "", "nameEn": "", "desc": "实心的背景颜色激活态，目前只用在默认实心按钮的 active 效果。", "descEn": "Solid background color active state, currently only used in the active effect of the default solid button.", "type": "string", "source": "map"}, "colorBgSolidHover": {"name": "", "nameEn": "", "desc": "实心的背景颜色悬浮态，目前只用在默认实心按钮的 hover 效果。", "descEn": "Solid background color hover state, currently only used in the hover effect of the default solid button.", "type": "string", "source": "map"}, "colorBgSpotlight": {"name": "引起注意的背景色", "nameEn": "", "desc": "该色用于引起用户强烈关注注意的背景色，目前只用在 Tooltip 的背景色上。", "descEn": "This color is used to draw the user's strong attention to the background color, and is currently only used in the background color of Tooltip.", "type": "string", "source": "map"}, "colorBorder": {"name": "一级边框色", "nameEn": "Default Border Color", "desc": "默认使用的边框颜色, 用于分割不同的元素，例如：表单的分割线、卡片的分割线等。", "descEn": "Default border color, used to separate different elements, such as: form separator, card separator, etc.", "type": "string", "source": "map"}, "colorBorderSecondary": {"name": "二级边框色", "nameEn": "Secondary Border Color", "desc": "比默认使用的边框色要浅一级，此颜色和 colorSplit 的颜色一致。使用的是实色。", "descEn": "Slightly lighter than the default border color, this color is the same as `colorSplit`. Solid color is used.", "type": "string", "source": "map"}, "colorErrorActive": {"name": "错误色的深色激活态", "nameEn": "Error active color", "desc": "错误色的深色激活态", "descEn": "The active state of the error color.", "type": "string", "source": "map"}, "colorErrorBg": {"name": "错误色的浅色背景颜色", "nameEn": "Error background color", "desc": "错误色的浅色背景颜色", "descEn": "The background color of the error state.", "type": "string", "source": "map"}, "colorErrorBgActive": {"name": "错误色的浅色背景色激活态", "nameEn": "Error background color active state", "desc": "错误色的浅色背景色激活态", "descEn": "The active state background color of the error state.", "type": "string", "source": "map"}, "colorErrorBgFilledHover": {"name": "错误色的浅色填充背景色悬浮态", "nameEn": "Wrong color fill background color suspension state", "desc": "错误色的浅色填充背景色悬浮态，目前只用在危险填充按钮的 hover 效果。", "descEn": "The wrong color fills the background color of the suspension state, which is currently only used in the hover effect of the dangerous filled button.", "type": "string", "source": "map"}, "colorErrorBgHover": {"name": "错误色的浅色背景色悬浮态", "nameEn": "Error background color hover state", "desc": "错误色的浅色背景色悬浮态", "descEn": "The hover state background color of the error state.", "type": "string", "source": "map"}, "colorErrorBorder": {"name": "错误色的描边色", "nameEn": "Error border color", "desc": "错误色的描边色", "descEn": "The border color of the error state.", "type": "string", "source": "map"}, "colorErrorBorderHover": {"name": "错误色的描边色悬浮态", "nameEn": "Error border color hover state", "desc": "错误色的描边色悬浮态", "descEn": "The hover state border color of the error state.", "type": "string", "source": "map"}, "colorErrorHover": {"name": "错误色的深色悬浮态", "nameEn": "Error hover color", "desc": "错误色的深色悬浮态", "descEn": "The hover state of the error color.", "type": "string", "source": "map"}, "colorErrorText": {"name": "错误色的文本默认态", "nameEn": "Error text default state", "desc": "错误色的文本默认态", "descEn": "The default state of the text in the error color.", "type": "string", "source": "map"}, "colorErrorTextActive": {"name": "错误色的文本激活态", "nameEn": "Error text active state", "desc": "错误色的文本激活态", "descEn": "The active state of the text in the error color.", "type": "string", "source": "map"}, "colorErrorTextHover": {"name": "错误色的文本悬浮态", "nameEn": "Error text hover state", "desc": "错误色的文本悬浮态", "descEn": "The hover state of the text in the error color.", "type": "string", "source": "map"}, "colorFill": {"name": "一级填充色", "nameEn": "", "desc": "最深的填充色，用于拉开与二、三级填充色的区分度，目前只用在 Slider 的 hover 效果。", "descEn": "The darkest fill color is used to distinguish between the second and third level of fill color, and is currently only used in the hover effect of Slider.", "type": "string", "source": "map"}, "colorFillQuaternary": {"name": "四级填充色", "nameEn": "", "desc": "最弱一级的填充色，适用于不易引起注意的色块，例如斑马纹、区分边界的色块等。", "descEn": "The weakest level of fill color is suitable for color blocks that are not easy to attract attention, such as zebra stripes, color blocks that distinguish boundaries, etc.", "type": "string", "source": "map"}, "colorFillSecondary": {"name": "二级填充色", "nameEn": "", "desc": "二级填充色可以较为明显地勾勒出元素形体，如 Rate、Skeleton 等。也可以作为三级填充色的 Hover 状态，如 Table 等。", "descEn": "The second level of fill color can outline the shape of the element more clearly, such as Rate, Skeleton, etc. It can also be used as the Hover state of the third level of fill color, such as Table, etc.", "type": "string", "source": "map"}, "colorFillTertiary": {"name": "三级填充色", "nameEn": "", "desc": "三级填充色用于勾勒出元素形体的场景，如 Slider、Segmented 等。如无强调需求的情况下，建议使用三级填色作为默认填色。", "descEn": "The third level of fill color is used to outline the shape of the element, such as Slider, Segmented, etc. If there is no emphasis requirement, it is recommended to use the third level of fill color as the default fill color.", "type": "string", "source": "map"}, "colorInfoActive": {"name": "信息色的深色激活态", "nameEn": "Active state of dark color of information color", "desc": "信息色的深色激活态。", "descEn": "Active state of dark color of information color.", "type": "string", "source": "map"}, "colorInfoBg": {"name": "信息色的浅色背景颜色", "nameEn": "Light background color of information color", "desc": "信息色的浅色背景颜色。", "descEn": "Light background color of information color.", "type": "string", "source": "map"}, "colorInfoBgHover": {"name": "信息色的浅色背景色悬浮态", "nameEn": "Hover state of light background color of information color", "desc": "信息色的浅色背景色悬浮态。", "descEn": "Hover state of light background color of information color.", "type": "string", "source": "map"}, "colorInfoBorder": {"name": "信息色的描边色", "nameEn": "Border color of information color", "desc": "信息色的描边色。", "descEn": "Border color of information color.", "type": "string", "source": "map"}, "colorInfoBorderHover": {"name": "信息色的描边色悬浮态", "nameEn": "Hover state of border color of information color", "desc": "信息色的描边色悬浮态。", "descEn": "Hover state of border color of information color.", "type": "string", "source": "map"}, "colorInfoHover": {"name": "信息色的深色悬浮态", "nameEn": "Hover state of dark color of information color", "desc": "信息色的深色悬浮态。", "descEn": "Hover state of dark color of information color.", "type": "string", "source": "map"}, "colorInfoText": {"name": "信息色的文本默认态", "nameEn": "Default state of text color of information color", "desc": "信息色的文本默认态。", "descEn": "Default state of text color of information color.", "type": "string", "source": "map"}, "colorInfoTextActive": {"name": "信息色的文本激活态", "nameEn": "Active state of text color of information color", "desc": "信息色的文本激活态。", "descEn": "Active state of text color of information color.", "type": "string", "source": "map"}, "colorInfoTextHover": {"name": "信息色的文本悬浮态", "nameEn": "Hover state of text color of information color", "desc": "信息色的文本悬浮态。", "descEn": "Hover state of text color of information color.", "type": "string", "source": "map"}, "colorLinkActive": {"name": "超链接激活颜色", "nameEn": "Hyperlink active color", "desc": "控制超链接被点击时的颜色。", "descEn": "Control the color of hyperlink when clicked.", "type": "string", "source": "map"}, "colorLinkHover": {"name": "超链接悬浮颜色", "nameEn": "Hyperlink hover color", "desc": "控制超链接悬浮时的颜色。", "descEn": "Control the color of hyperlink when hovering.", "type": "string", "source": "map"}, "colorPrimaryActive": {"name": "主色激活态", "nameEn": "Active state of primary color", "desc": "主色梯度下的深色激活态。", "descEn": "Dark active state under the main color gradient.", "type": "string", "source": "map"}, "colorPrimaryBg": {"name": "主色浅色背景色", "nameEn": "Light background color of primary color", "desc": "主色浅色背景颜色，一般用于视觉层级较弱的选中状态。", "descEn": "Light background color of primary color, usually used for weak visual level selection state.", "type": "string", "source": "map"}, "colorPrimaryBgHover": {"name": "主色浅色背景悬浮态", "nameEn": "Hover state of light background color of primary color", "desc": "与主色浅色背景颜色相对应的悬浮态颜色。", "descEn": "The hover state color corresponding to the light background color of the primary color.", "type": "string", "source": "map"}, "colorPrimaryBorder": {"name": "主色描边色", "nameEn": "Border color of primary color", "desc": "主色梯度下的描边用色，用在 Slider 等组件的描边上。", "descEn": "The stroke color under the main color gradient, used on the stroke of components such as Slider.", "type": "string", "source": "map"}, "colorPrimaryBorderHover": {"name": "主色描边色悬浮态", "nameEn": "Hover state of border color of primary color", "desc": "主色梯度下的描边用色的悬浮态，Slider 、Button 等组件的描边 Hover 时会使用。", "descEn": "The hover state of the stroke color under the main color gradient, which will be used when the stroke Hover of components such as Slider and Button.", "type": "string", "source": "map"}, "colorPrimaryHover": {"name": "主色悬浮态", "nameEn": "Hover state of primary color", "desc": "主色梯度下的悬浮态。", "descEn": "Hover state under the main color gradient.", "type": "string", "source": "map"}, "colorPrimaryText": {"name": "主色文本", "nameEn": "Text color of primary color", "desc": "主色梯度下的文本颜色。", "descEn": "Text color under the main color gradient.", "type": "string", "source": "map"}, "colorPrimaryTextActive": {"name": "主色文本激活态", "nameEn": "Active state of text color of primary color", "desc": "主色梯度下的文本激活态。", "descEn": "Active state of text color under the main color gradient.", "type": "string", "source": "map"}, "colorPrimaryTextHover": {"name": "主色文本悬浮态", "nameEn": "Hover state of text color of primary color", "desc": "主色梯度下的文本悬浮态。", "descEn": "Hover state of text color under the main color gradient.", "type": "string", "source": "map"}, "colorSuccessActive": {"name": "成功色的深色激活态", "nameEn": "Active State Color of Dark Success", "desc": "成功色的深色激活态", "descEn": "Active state color of dark success color", "type": "string", "source": "map"}, "colorSuccessBg": {"name": "成功色的浅色背景颜色", "nameEn": "Light Background Color of Success Color", "desc": "成功色的浅色背景颜色，用于 Tag 和 Alert 的成功态背景色", "descEn": "Light background color of success color, used for Tag and Alert success state background color", "type": "string", "source": "map"}, "colorSuccessBgHover": {"name": "成功色的浅色背景色悬浮态", "nameEn": "Hover State Color of Light Success Background", "desc": "成功色浅色背景颜色，一般用于视觉层级较弱的选中状态，不过 antd 目前没有使用到该 token", "descEn": "Light background color of success color, but antd does not use this token currently", "type": "string", "source": "map"}, "colorSuccessBorder": {"name": "成功色的描边色", "nameEn": "Border Color of Success Color", "desc": "成功色的描边色，用于 Tag 和 Alert 的成功态描边色", "descEn": "Border color of success color, used for Tag and Alert success state border color", "type": "string", "source": "map"}, "colorSuccessBorderHover": {"name": "成功色的描边色悬浮态", "nameEn": "Hover State Color of Success Border", "desc": "成功色的描边色悬浮态", "descEn": "Hover state color of success color border", "type": "string", "source": "map"}, "colorSuccessHover": {"name": "成功色的深色悬浮态", "nameEn": "Hover State Color of Dark Success", "desc": "成功色的深色悬浮态", "descEn": "Hover state color of dark success color", "type": "string", "source": "map"}, "colorSuccessText": {"name": "成功色的文本默认态", "nameEn": "Default State Color of Success Text", "desc": "成功色的文本默认态", "descEn": "Default state color of success color text", "type": "string", "source": "map"}, "colorSuccessTextActive": {"name": "成功色的文本激活态", "nameEn": "Active State Color of Success Text", "desc": "成功色的文本激活态", "descEn": "Active state color of success color text", "type": "string", "source": "map"}, "colorSuccessTextHover": {"name": "成功色的文本悬浮态", "nameEn": "Hover State Color of Success Text", "desc": "成功色的文本悬浮态", "descEn": "Hover state color of success color text", "type": "string", "source": "map"}, "colorText": {"name": "一级文本色", "nameEn": "Text Color", "desc": "最深的文本色。为了符合W3C标准，默认的文本颜色使用了该色，同时这个颜色也是最深的中性色。", "descEn": "Default text color which comply with W3C standards, and this color is also the darkest neutral color.", "type": "string", "source": "map"}, "colorTextQuaternary": {"name": "四级文本色", "nameEn": "", "desc": "第四级文本色是最浅的文本色，例如表单的输入提示文本、禁用色文本等。", "descEn": "The fourth level of text color is the lightest text color, such as form input prompt text, disabled color text, etc.", "type": "string", "source": "map"}, "colorTextSecondary": {"name": "二级文本色", "nameEn": "Secondary Text Color", "desc": "作为第二梯度的文本色，一般用在不那么需要强化文本颜色的场景，例如 Label 文本、Menu 的文本选中态等场景。", "descEn": "The second level of text color is generally used in scenarios where text color is not emphasized, such as label text, menu text selection state, etc.", "type": "string", "source": "map"}, "colorTextTertiary": {"name": "三级文本色", "nameEn": "", "desc": "第三级文本色一般用于描述性文本，例如表单的中的补充说明文本、列表的描述性文本等场景。", "descEn": "The third level of text color is generally used for descriptive text, such as form supplementary explanation text, list descriptive text, etc.", "type": "string", "source": "map"}, "colorWarningActive": {"name": "警戒色的深色激活态", "nameEn": "Warning active color", "desc": "警戒色的深色激活态", "descEn": "The active state of the warning color.", "type": "string", "source": "map"}, "colorWarningBg": {"name": "警戒色的浅色背景颜色", "nameEn": "Warning background color", "desc": "警戒色的浅色背景颜色", "descEn": "The background color of the warning state.", "type": "string", "source": "map"}, "colorWarningBgHover": {"name": "警戒色的浅色背景色悬浮态", "nameEn": "Warning background color hover state", "desc": "警戒色的浅色背景色悬浮态", "descEn": "The hover state background color of the warning state.", "type": "string", "source": "map"}, "colorWarningBorder": {"name": "警戒色的描边色", "nameEn": "Warning border color", "desc": "警戒色的描边色", "descEn": "The border color of the warning state.", "type": "string", "source": "map"}, "colorWarningBorderHover": {"name": "警戒色的描边色悬浮态", "nameEn": "Warning border color hover state", "desc": "警戒色的描边色悬浮态", "descEn": "The hover state border color of the warning state.", "type": "string", "source": "map"}, "colorWarningHover": {"name": "警戒色的深色悬浮态", "nameEn": "Warning hover color", "desc": "警戒色的深色悬浮态", "descEn": "The hover state of the warning color.", "type": "string", "source": "map"}, "colorWarningText": {"name": "警戒色的文本默认态", "nameEn": "Warning text default state", "desc": "警戒色的文本默认态", "descEn": "The default state of the text in the warning color.", "type": "string", "source": "map"}, "colorWarningTextActive": {"name": "警戒色的文本激活态", "nameEn": "Warning text active state", "desc": "警戒色的文本激活态", "descEn": "The active state of the text in the warning color.", "type": "string", "source": "map"}, "colorWarningTextHover": {"name": "警戒色的文本悬浮态", "nameEn": "Warning text hover state", "desc": "警戒色的文本悬浮态", "descEn": "The hover state of the text in the warning color.", "type": "string", "source": "map"}, "colorWhite": {"name": "纯白色", "nameEn": "", "desc": "不随主题变化的纯白色", "descEn": "Pure white color don't changed by theme", "type": "string", "source": "map"}, "controlHeightLG": {"name": "较高的组件高度", "nameEn": "LG component height", "desc": "较高的组件高度", "descEn": "LG component height", "type": "number", "source": "map"}, "controlHeightSM": {"name": "较小的组件高度", "nameEn": "SM component height", "desc": "较小的组件高度", "descEn": "SM component height", "type": "number", "source": "map"}, "controlHeightXS": {"name": "更小的组件高度", "nameEn": "XS component height", "desc": "更小的组件高度", "descEn": "XS component height", "type": "number", "source": "map"}, "fontSizeHeading1": {"name": "一级标题字号", "nameEn": "Font size of heading level 1", "desc": "H1 标签所使用的字号", "descEn": "Font size of h1 tag.", "type": "number", "source": "map"}, "fontSizeHeading2": {"name": "二级标题字号", "nameEn": "Font size of heading level 2", "desc": "h2 标签所使用的字号", "descEn": "Font size of h2 tag.", "type": "number", "source": "map"}, "fontSizeHeading3": {"name": "三级标题字号", "nameEn": "Font size of heading level 3", "desc": "h3 标签使用的字号", "descEn": "Font size of h3 tag.", "type": "number", "source": "map"}, "fontSizeHeading4": {"name": "四级标题字号", "nameEn": "Font size of heading level 4", "desc": "h4 标签使用的字号", "descEn": "Font size of h4 tag.", "type": "number", "source": "map"}, "fontSizeHeading5": {"name": "五级标题字号", "nameEn": "Font size of heading level 5", "desc": "h5 标签使用的字号", "descEn": "Font size of h5 tag.", "type": "number", "source": "map"}, "fontSizeLG": {"name": "", "nameEn": "", "desc": "大号字体大小", "descEn": "Large font size", "type": "number", "source": "map"}, "fontSizeSM": {"name": "", "nameEn": "", "desc": "小号字体大小", "descEn": "Small font size", "type": "number", "source": "map"}, "fontSizeXL": {"name": "", "nameEn": "", "desc": "超大号字体大小", "descEn": "Super large font size", "type": "number", "source": "map"}, "lineHeight": {"name": "", "nameEn": "", "desc": "文本行高", "descEn": "Line height of text.", "type": "number", "source": "map"}, "lineHeightHeading1": {"name": "一级标题行高", "nameEn": "Line height of heading level 1", "desc": "H1 标签所使用的行高", "descEn": "Line height of h1 tag.", "type": "number", "source": "map"}, "lineHeightHeading2": {"name": "二级标题行高", "nameEn": "Line height of heading level 2", "desc": "h2 标签所使用的行高", "descEn": "Line height of h2 tag.", "type": "number", "source": "map"}, "lineHeightHeading3": {"name": "三级标题行高", "nameEn": "Line height of heading level 3", "desc": "h3 标签所使用的行高", "descEn": "Line height of h3 tag.", "type": "number", "source": "map"}, "lineHeightHeading4": {"name": "四级标题行高", "nameEn": "Line height of heading level 4", "desc": "h4 标签所使用的行高", "descEn": "Line height of h4 tag.", "type": "number", "source": "map"}, "lineHeightHeading5": {"name": "五级标题行高", "nameEn": "Line height of heading level 5", "desc": "h5 标签所使用的行高", "descEn": "Line height of h5 tag.", "type": "number", "source": "map"}, "lineHeightLG": {"name": "", "nameEn": "", "desc": "大型文本行高", "descEn": "Line height of large text.", "type": "number", "source": "map"}, "lineHeightSM": {"name": "", "nameEn": "", "desc": "小型文本行高", "descEn": "Line height of small text.", "type": "number", "source": "map"}, "lineWidthBold": {"name": "线宽", "nameEn": "Line Width", "desc": "描边类组件的默认线宽，如 Button、Input、Select 等输入类控件。", "descEn": "The default line width of the outline class components, such as Button, Input, Select, etc.", "type": "number", "source": "map"}, "motionDurationFast": {"name": "", "nameEn": "", "desc": "动效播放速度，快速。用于小型元素动画交互", "descEn": "Motion speed, fast speed. Used for small element animation interaction.", "type": "string", "source": "map"}, "motionDurationMid": {"name": "", "nameEn": "", "desc": "动效播放速度，中速。用于中型元素动画交互", "descEn": "Motion speed, medium speed. Used for medium element animation interaction.", "type": "string", "source": "map"}, "motionDurationSlow": {"name": "", "nameEn": "", "desc": "动效播放速度，慢速。用于大型元素如面板动画交互", "descEn": "Motion speed, slow speed. Used for large element animation interaction.", "type": "string", "source": "map"}, "size": {"name": "默认", "nameEn": "", "desc": "默认尺寸", "descEn": "", "type": "number", "source": "map"}, "sizeLG": {"name": "LG", "nameEn": "", "desc": "", "descEn": "", "type": "number", "source": "map"}, "sizeMD": {"name": "MD", "nameEn": "", "desc": "", "descEn": "", "type": "number", "source": "map"}, "sizeMS": {"name": "", "nameEn": "", "desc": "", "descEn": "", "type": "number", "source": "map"}, "sizeSM": {"name": "SM", "nameEn": "", "desc": "", "descEn": "", "type": "number", "source": "map"}, "sizeXL": {"name": "XL", "nameEn": "", "desc": "", "descEn": "", "type": "number", "source": "map"}, "sizeXS": {"name": "XS", "nameEn": "", "desc": "", "descEn": "", "type": "number", "source": "map"}, "sizeXXL": {"name": "XXL", "nameEn": "", "desc": "", "descEn": "", "type": "number", "source": "map"}, "sizeXXS": {"name": "XXS", "nameEn": "", "desc": "", "descEn": "", "type": "number", "source": "map"}, "boxShadow": {"name": "一级阴影", "nameEn": "Box shadow", "desc": "控制元素阴影样式。", "descEn": "Control the box shadow style of an element.", "type": "string", "source": "alias"}, "boxShadowSecondary": {"name": "二级阴影", "nameEn": "Secondary box shadow", "desc": "控制元素二级阴影样式。", "descEn": "Control the secondary box shadow style of an element.", "type": "string", "source": "alias"}, "boxShadowTertiary": {"name": "三级阴影", "nameEn": "Tertiary box shadow", "desc": "控制元素三级盒子阴影样式。", "descEn": "Control the tertiary box shadow style of an element.", "type": "string", "source": "alias"}, "colorBgContainerDisabled": {"name": "容器禁用态下的背景色", "nameEn": "Disabled container background color", "desc": "控制容器在禁用状态下的背景色。", "descEn": "Control the background color of container in disabled state.", "type": "string", "source": "alias"}, "colorBgTextActive": {"name": "文本激活态背景色", "nameEn": "Text active background color", "desc": "控制文本在激活状态下的背景色。", "descEn": "Control the background color of text in active state.", "type": "string", "source": "alias"}, "colorBgTextHover": {"name": "文本悬停态背景色", "nameEn": "Text hover background color", "desc": "控制文本在悬停状态下的背景色。", "descEn": "Control the background color of text in hover state.", "type": "string", "source": "alias"}, "colorBorderBg": {"name": "背景边框颜色", "nameEn": "Background border color", "desc": "控制元素背景边框的颜色。", "descEn": "Control the color of background border of element.", "type": "string", "source": "alias"}, "colorErrorOutline": {"name": "错误状态下的 Outline 颜色", "nameEn": "Error outline color", "desc": "控制输入组件错误状态下的外轮廓线颜色。", "descEn": "Control the outline color of input component in error state.", "type": "string", "source": "alias"}, "colorFillAlter": {"name": "替代背景色", "nameEn": "Alternative background color", "desc": "控制元素替代背景色。", "descEn": "Control the alternative background color of element.", "type": "string", "source": "alias"}, "colorFillContent": {"name": "内容区域背景色", "nameEn": "Background color of content area", "desc": "控制内容区域的背景色。", "descEn": "Control the background color of content area.", "type": "string", "source": "alias"}, "colorFillContentHover": {"name": "内容区域背景色（悬停）", "nameEn": "Background color of content area (hover)", "desc": "控制内容区域背景色在鼠标悬停时的样式。", "descEn": "Control the style of background color of content area when mouse hovers over it.", "type": "string", "source": "alias"}, "colorHighlight": {"name": "高亮颜色", "nameEn": "Highlight color", "desc": "控制页面元素高亮时的颜色。", "descEn": "Control the color of page element when highlighted.", "type": "string", "source": "alias"}, "colorIcon": {"name": "弱操作图标颜色\n *", "nameEn": "Weak action icon color\n *", "desc": "控制弱操作图标的颜色，例如 allowClear 或 Alert 关闭按钮。\n *", "descEn": "Weak action. Such as `allowClear` or Alert close button", "type": "string", "source": "alias"}, "colorIconHover": {"name": "弱操作图标悬浮态颜色", "nameEn": "Weak action icon hover color", "desc": "控制弱操作图标在悬浮状态下的颜色，例如 allowClear 或 Alert 关闭按钮。", "descEn": "Weak action hover color. Such as `allowClear` or Alert close button", "type": "string", "source": "alias"}, "colorSplit": {"name": "分割线颜色", "nameEn": "Separator Color", "desc": "用于作为分割线的颜色，此颜色和 colorBorderSecondary 的颜色一致，但是用的是透明色。", "descEn": "Used as the color of separator, this color is the same as colorBorderSecondary but with transparency.", "type": "string", "source": "alias"}, "colorTextDescription": {"name": "文本描述字体颜色", "nameEn": "Text description font color", "desc": "控制文本描述字体颜色。", "descEn": "Control the font color of text description.", "type": "string", "source": "alias"}, "colorTextDisabled": {"name": "禁用字体颜色", "nameEn": "Disabled Text Color", "desc": "控制禁用状态下的字体颜色。", "descEn": "Control the color of text in disabled state.", "type": "string", "source": "alias"}, "colorTextHeading": {"name": "标题字体颜色", "nameEn": "Heading Text Color", "desc": "控制标题字体颜色。", "descEn": "Control the font color of heading.", "type": "string", "source": "alias"}, "colorTextLabel": {"name": "文本标签字体颜色", "nameEn": "Text label font color", "desc": "控制文本标签字体颜色。", "descEn": "Control the font color of text label.", "type": "string", "source": "alias"}, "colorTextLightSolid": {"name": "固定文本高亮颜色", "nameEn": "Fixed text highlight color", "desc": "控制带背景色的文本，例如 Primary Button 组件中的文本高亮颜色。", "descEn": "Control the highlight color of text with background color, such as the text in Primary Button components.", "type": "string", "source": "alias"}, "colorTextPlaceholder": {"name": "占位文本颜色", "nameEn": "Placeholder Text Color", "desc": "控制占位文本的颜色。", "descEn": "Control the color of placeholder text.", "type": "string", "source": "alias"}, "colorWarningOutline": {"name": "警告状态下的 Outline 颜色", "nameEn": "Warning outline color", "desc": "控制输入组件警告状态下的外轮廓线颜色。", "descEn": "Control the outline color of input component in warning state.", "type": "string", "source": "alias"}, "controlInteractiveSize": {"name": "控制组件的交互大小", "nameEn": "Interactive size of control component", "desc": "控制组件的交互大小。", "descEn": "Control the interactive size of control component.", "type": "number", "source": "alias"}, "controlItemBgActive": {"name": "控制组件项在激活状态下的背景颜色", "nameEn": "Background color of control component item when active", "desc": "控制组件项在激活状态下的背景颜色。", "descEn": "Control the background color of control component item when active.", "type": "string", "source": "alias"}, "controlItemBgActiveDisabled": {"name": "控制组件项在禁用状态下的激活背景颜色", "nameEn": "Background color of control component item when active and disabled", "desc": "控制组件项在禁用状态下的激活背景颜色。", "descEn": "Control the background color of control component item when active and disabled.", "type": "string", "source": "alias"}, "controlItemBgActiveHover": {"name": "控制组件项在鼠标悬浮且激活状态下的背景颜色", "nameEn": "Background color of control component item when hovering and active", "desc": "控制组件项在鼠标悬浮且激活状态下的背景颜色。", "descEn": "Control the background color of control component item when hovering and active.", "type": "string", "source": "alias"}, "controlItemBgHover": {"name": "控制组件项在鼠标悬浮时的背景颜色", "nameEn": "Background color of control component item when hovering", "desc": "控制组件项在鼠标悬浮时的背景颜色。", "descEn": "Control the background color of control component item when hovering.", "type": "string", "source": "alias"}, "controlOutline": {"name": "输入组件的 Outline 颜色", "nameEn": "Input component outline color", "desc": "控制输入组件的外轮廓线颜色。", "descEn": "Control the outline color of input component.", "type": "string", "source": "alias"}, "controlOutlineWidth": {"name": "输入组件的外轮廓线宽度", "nameEn": "Input component outline width", "desc": "控制输入组件的外轮廓线宽度。", "descEn": "Control the outline width of input component.", "type": "number", "source": "alias"}, "controlPaddingHorizontal": {"name": "控制水平内间距", "nameEn": "Control horizontal padding", "desc": "控制元素水平内间距。", "descEn": "Control the horizontal padding of an element.", "type": "number", "source": "alias"}, "controlPaddingHorizontalSM": {"name": "控制中小尺寸水平内间距", "nameEn": "Control horizontal padding with a small-medium size", "desc": "控制元素中小尺寸水平内间距。", "descEn": "Control the horizontal padding of an element with a small-medium size.", "type": "number", "source": "alias"}, "fontSizeIcon": {"name": "选择器、级联选择器等中的操作图标字体大小", "nameEn": "Operation icon font size in Select, Cascader, etc.", "desc": "控制选择器、级联选择器等中的操作图标字体大小。正常情况下与 fontSizeSM 相同。", "descEn": "Control the font size of operation icon in Select, Cascader, etc. Normally same as fontSizeSM.", "type": "number", "source": "alias"}, "fontWeightStrong": {"name": "标题类组件（如 h1、h2、h3）或选中项的字体粗细", "nameEn": "Font weight for heading components (such as h1, h2, h3) or selected item", "desc": "控制标题类组件（如 h1、h2、h3）或选中项的字体粗细。", "descEn": "Control the font weight of heading components (such as h1, h2, h3) or selected item.", "type": "number", "source": "alias"}, "lineWidthFocus": {"name": "线条宽度(聚焦态)", "nameEn": "Line width(focus state)", "desc": "控制线条的宽度，当组件处于聚焦态时。", "descEn": "Control the width of the line when the component is in focus state.", "type": "number", "source": "alias"}, "linkDecoration": {"name": "链接文本装饰", "nameEn": "Link text decoration", "desc": "控制链接文本的装饰样式。", "descEn": "Control the text decoration style of a link.", "type": "undefined | TextDecoration<string | number>", "source": "alias"}, "linkFocusDecoration": {"name": "链接聚焦时文本装饰", "nameEn": "Link text decoration on focus", "desc": "控制链接聚焦时文本的装饰样式。", "descEn": "Control the text decoration style of a link on focus.", "type": "undefined | TextDecoration<string | number>", "source": "alias"}, "linkHoverDecoration": {"name": "链接鼠标悬浮时文本装饰", "nameEn": "Link text decoration on mouse hover", "desc": "控制链接鼠标悬浮时文本的装饰样式。", "descEn": "Control the text decoration style of a link on mouse hover.", "type": "undefined | TextDecoration<string | number>", "source": "alias"}, "margin": {"name": "外边距", "nameEn": "<PERSON><PERSON>", "desc": "控制元素外边距，中等尺寸。", "descEn": "Control the margin of an element, with a medium size.", "type": "number", "source": "alias"}, "marginLG": {"name": "外边距 LG", "nameEn": "<PERSON>gin LG", "desc": "控制元素外边距，大尺寸。", "descEn": "Control the margin of an element, with a large size.", "type": "number", "source": "alias"}, "marginMD": {"name": "外边距 MD", "nameEn": "<PERSON><PERSON>", "desc": "控制元素外边距，中大尺寸。", "descEn": "Control the margin of an element, with a medium-large size.", "type": "number", "source": "alias"}, "marginSM": {"name": "外边距 SM", "nameEn": "<PERSON><PERSON>", "desc": "控制元素外边距，中小尺寸。", "descEn": "Control the margin of an element, with a medium-small size.", "type": "number", "source": "alias"}, "marginXL": {"name": "外边距 XL", "nameEn": "Margin XL", "desc": "控制元素外边距，超大尺寸。", "descEn": "Control the margin of an element, with an extra-large size.", "type": "number", "source": "alias"}, "marginXS": {"name": "外边距 XS", "nameEn": "Margin XS", "desc": "控制元素外边距，小尺寸。", "descEn": "Control the margin of an element, with a small size.", "type": "number", "source": "alias"}, "marginXXL": {"name": "外边距 XXL", "nameEn": "Margin XXL", "desc": "控制元素外边距，最大尺寸。", "descEn": "Control the margin of an element, with the largest size.", "type": "number", "source": "alias"}, "marginXXS": {"name": "外边距 XXS", "nameEn": "Margin XXS", "desc": "控制元素外边距，最小尺寸。", "descEn": "Control the margin of an element, with the smallest size.", "type": "number", "source": "alias"}, "opacityLoading": {"name": "加载状态透明度", "nameEn": "Loading opacity", "desc": "控制加载状态的透明度。", "descEn": "Control the opacity of the loading state.", "type": "number", "source": "alias"}, "padding": {"name": "内间距", "nameEn": "Padding", "desc": "控制元素的内间距。", "descEn": "Control the padding of the element.", "type": "number", "source": "alias"}, "paddingContentHorizontal": {"name": "内容水平内间距", "nameEn": "Content horizontal padding", "desc": "控制内容元素水平内间距。", "descEn": "Control the horizontal padding of content element.", "type": "number", "source": "alias"}, "paddingContentHorizontalLG": {"name": "内容水平内间距（LG）", "nameEn": "Content horizontal padding (LG)", "desc": "控制内容元素水平内间距，适用于大屏幕设备。", "descEn": "Control the horizontal padding of content element, suitable for large screen devices.", "type": "number", "source": "alias"}, "paddingContentHorizontalSM": {"name": "内容水平内间距（SM）", "nameEn": "Content horizontal padding (SM)", "desc": "控制内容元素水平内间距，适用于小屏幕设备。", "descEn": "Control the horizontal padding of content element, suitable for small screen devices.", "type": "number", "source": "alias"}, "paddingContentVertical": {"name": "内容垂直内间距", "nameEn": "Content vertical padding", "desc": "控制内容元素垂直内间距。", "descEn": "Control the vertical padding of content element.", "type": "number", "source": "alias"}, "paddingContentVerticalLG": {"name": "内容垂直内间距（LG）", "nameEn": "Content vertical padding (LG)", "desc": "控制内容元素垂直内间距，适用于大屏幕设备。", "descEn": "Control the vertical padding of content element, suitable for large screen devices.", "type": "number", "source": "alias"}, "paddingContentVerticalSM": {"name": "内容垂直内间距（SM）", "nameEn": "Content vertical padding (SM)", "desc": "控制内容元素垂直内间距，适用于小屏幕设备。", "descEn": "Control the vertical padding of content element, suitable for small screen devices.", "type": "number", "source": "alias"}, "paddingLG": {"name": "大内间距", "nameEn": "Large padding", "desc": "控制元素的大内间距。", "descEn": "Control the large padding of the element.", "type": "number", "source": "alias"}, "paddingMD": {"name": "中等内间距", "nameEn": "Medium padding", "desc": "控制元素的中等内间距。", "descEn": "Control the medium padding of the element.", "type": "number", "source": "alias"}, "paddingSM": {"name": "小内间距", "nameEn": "Small padding", "desc": "控制元素的小内间距。", "descEn": "Control the small padding of the element.", "type": "number", "source": "alias"}, "paddingXL": {"name": "特大内间距", "nameEn": "Extra large padding", "desc": "控制元素的特大内间距。", "descEn": "Control the extra large padding of the element.", "type": "number", "source": "alias"}, "paddingXS": {"name": "特小内间距", "nameEn": "Extra small padding", "desc": "控制元素的特小内间距。", "descEn": "Control the extra small padding of the element.", "type": "number", "source": "alias"}, "paddingXXS": {"name": "极小内间距", "nameEn": "Extra extra small padding", "desc": "控制元素的极小内间距。", "descEn": "Control the extra extra small padding of the element.", "type": "number", "source": "alias"}, "screenLG": {"name": "屏幕宽度（像素） - 大屏幕", "nameEn": "Screen width (pixels) - Large screens", "desc": "控制大屏幕的屏幕宽度。", "descEn": "Control the screen width of large screens.", "type": "number", "source": "alias"}, "screenLGMax": {"name": "屏幕宽度（像素） - 大屏幕最大值", "nameEn": "Screen width (pixels) - Large screens maximum value", "desc": "控制大屏幕的最大宽度。", "descEn": "Control the maximum width of large screens.", "type": "number", "source": "alias"}, "screenLGMin": {"name": "屏幕宽度（像素） - 大屏幕最小值", "nameEn": "Screen width (pixels) - Large screens minimum value", "desc": "控制大屏幕的最小宽度。", "descEn": "Control the minimum width of large screens.", "type": "number", "source": "alias"}, "screenMD": {"name": "屏幕宽度（像素） - 中等屏幕", "nameEn": "Screen width (pixels) - Medium screens", "desc": "控制中等屏幕的屏幕宽度。", "descEn": "Control the screen width of medium screens.", "type": "number", "source": "alias"}, "screenMDMax": {"name": "屏幕宽度（像素） - 中等屏幕最大值", "nameEn": "Screen width (pixels) - Medium screens maximum value", "desc": "控制中等屏幕的最大宽度。", "descEn": "Control the maximum width of medium screens.", "type": "number", "source": "alias"}, "screenMDMin": {"name": "屏幕宽度（像素） - 中等屏幕最小值", "nameEn": "Screen width (pixels) - Medium screens minimum value", "desc": "控制中等屏幕的最小宽度。", "descEn": "Control the minimum width of medium screens.", "type": "number", "source": "alias"}, "screenSM": {"name": "屏幕宽度（像素） - 小屏幕", "nameEn": "Screen width (pixels) - Small screens", "desc": "控制小屏幕的屏幕宽度。", "descEn": "Control the screen width of small screens.", "type": "number", "source": "alias"}, "screenSMMax": {"name": "屏幕宽度（像素） - 小屏幕最大值", "nameEn": "Screen width (pixels) - Small screens maximum value", "desc": "控制小屏幕的最大宽度。", "descEn": "Control the maximum width of small screens.", "type": "number", "source": "alias"}, "screenSMMin": {"name": "屏幕宽度（像素） - 小屏幕最小值", "nameEn": "Screen width (pixels) - Small screens minimum value", "desc": "控制小屏幕的最小宽度。", "descEn": "Control the minimum width of small screens.", "type": "number", "source": "alias"}, "screenXL": {"name": "屏幕宽度（像素） - 超大屏幕", "nameEn": "Screen width (pixels) - Extra large screens", "desc": "控制超大屏幕的屏幕宽度。", "descEn": "Control the screen width of extra large screens.", "type": "number", "source": "alias"}, "screenXLMax": {"name": "屏幕宽度（像素） - 超大屏幕最大值", "nameEn": "Screen width (pixels) - Extra large screens maximum value", "desc": "控制超大屏幕的最大宽度。", "descEn": "Control the maximum width of extra large screens.", "type": "number", "source": "alias"}, "screenXLMin": {"name": "屏幕宽度（像素） - 超大屏幕最小值", "nameEn": "Screen width (pixels) - Extra large screens minimum value", "desc": "控制超大屏幕的最小宽度。", "descEn": "Control the minimum width of extra large screens.", "type": "number", "source": "alias"}, "screenXS": {"name": "屏幕宽度（像素） - 超小屏幕", "nameEn": "Screen width (pixels) - Extra small screens", "desc": "控制超小屏幕的屏幕宽度。", "descEn": "Control the screen width of extra small screens.", "type": "number", "source": "alias"}, "screenXSMax": {"name": "屏幕宽度（像素） - 超小屏幕最大值", "nameEn": "Screen width (pixels) - Extra small screens maximum value", "desc": "控制超小屏幕的最大宽度。", "descEn": "Control the maximum width of extra small screens.", "type": "number", "source": "alias"}, "screenXSMin": {"name": "屏幕宽度（像素） - 超小屏幕最小值", "nameEn": "Screen width (pixels) - Extra small screens minimum value", "desc": "控制超小屏幕的最小宽度。", "descEn": "Control the minimum width of extra small screens.", "type": "number", "source": "alias"}, "screenXXL": {"name": "屏幕宽度（像素） - 超超大屏幕", "nameEn": "Screen width (pixels) - Extra extra large screens", "desc": "控制超超大屏幕的屏幕宽度。", "descEn": "Control the screen width of extra extra large screens.", "type": "number", "source": "alias"}, "screenXXLMin": {"name": "屏幕宽度（像素） - 超超大屏幕最小值", "nameEn": "Screen width (pixels) - Extra extra large screens minimum value", "desc": "控制超超大屏幕的最小宽度。", "descEn": "Control the minimum width of extra extra large screens.", "type": "number", "source": "alias"}}, "components": {"Affix": [{"source": "Affix", "token": "zIndexPopup", "type": "number", "desc": "弹出层的 z-index", "descEn": "z-index of popup", "name": "", "nameEn": ""}], "Alert": [{"source": "<PERSON><PERSON>", "token": "defaultPadding", "type": "undefined | Padding<string | number>", "desc": "默认内间距", "descEn": "Default padding", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "withDescriptionIconSize", "type": "number", "desc": "带有描述时的图标尺寸", "descEn": "Icon size with description", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "withDescriptionPadding", "type": "undefined | Padding<string | number>", "desc": "带有描述的内间距", "descEn": "Padding with description", "name": "", "nameEn": ""}], "Anchor": [{"source": "<PERSON><PERSON>", "token": "linkPaddingBlock", "type": "number", "desc": "链接纵向内间距", "descEn": "Vertical padding of link", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "linkPaddingInlineStart", "type": "number", "desc": "链接横向内间距", "descEn": "Horizontal padding of link", "name": "", "nameEn": ""}], "App": [], "Avatar": [{"source": "Avatar", "token": "containerSize", "type": "number", "desc": "头像尺寸", "descEn": "<PERSON><PERSON> of Avatar", "name": "", "nameEn": ""}, {"source": "Avatar", "token": "containerSizeLG", "type": "number", "desc": "大号头像尺寸", "descEn": "Size of large Avatar", "name": "", "nameEn": ""}, {"source": "Avatar", "token": "containerSizeSM", "type": "number", "desc": "小号头像尺寸", "descEn": "Size of small Avatar", "name": "", "nameEn": ""}, {"source": "Avatar", "token": "groupBorderColor", "type": "string", "desc": "头像组边框颜色", "descEn": "Border color of avatars in a group", "name": "", "nameEn": ""}, {"source": "Avatar", "token": "groupOverlapping", "type": "number", "desc": "头像组重叠宽度", "descEn": "Overlapping of avatars in a group", "name": "", "nameEn": ""}, {"source": "Avatar", "token": "groupSpace", "type": "number", "desc": "头像组间距", "descEn": "Spacing between avatars in a group", "name": "", "nameEn": ""}, {"source": "Avatar", "token": "textFontSize", "type": "number", "desc": "头像文字大小", "descEn": "Font size of Avatar", "name": "", "nameEn": ""}, {"source": "Avatar", "token": "textFontSizeLG", "type": "number", "desc": "大号头像文字大小", "descEn": "Font size of large Avatar", "name": "", "nameEn": ""}, {"source": "Avatar", "token": "textFontSizeSM", "type": "number", "desc": "小号头像文字大小", "descEn": "Font size of small Avatar", "name": "", "nameEn": ""}], "BackTop": [{"source": "BackTop", "token": "zIndexPopup", "type": "number", "desc": "弹出层的 z-index", "descEn": "z-index of popup", "name": "", "nameEn": ""}], "Badge": [{"source": "Badge", "token": "dotSize", "type": "number", "desc": "点状徽标尺寸", "descEn": "Size of dot badge", "name": "", "nameEn": ""}, {"source": "Badge", "token": "indicatorHeight", "type": "string | number", "desc": "徽标高度", "descEn": "Height of badge", "name": "", "nameEn": ""}, {"source": "Badge", "token": "indicatorHeightSM", "type": "string | number", "desc": "小号徽标高度", "descEn": "Height of small badge", "name": "", "nameEn": ""}, {"source": "Badge", "token": "indicatorZIndex", "type": "string | number", "desc": "徽标 z-index", "descEn": "z-index of badge", "name": "", "nameEn": ""}, {"source": "Badge", "token": "statusSize", "type": "number", "desc": "状态徽标尺寸", "descEn": "Size of status badge", "name": "", "nameEn": ""}, {"source": "Badge", "token": "textFontSize", "type": "number", "desc": "徽标文本尺寸", "descEn": "Font size of badge text", "name": "", "nameEn": ""}, {"source": "Badge", "token": "textFontSizeSM", "type": "number", "desc": "小号徽标文本尺寸", "descEn": "Font size of small badge text", "name": "", "nameEn": ""}, {"source": "Badge", "token": "textFontWeight", "type": "string | number", "desc": "徽标文本粗细", "descEn": "Font weight of badge text", "name": "", "nameEn": ""}], "Breadcrumb": [{"source": "Breadcrumb", "token": "iconFontSize", "type": "number", "desc": "图标大小", "descEn": "Icon size", "name": "", "nameEn": ""}, {"source": "Breadcrumb", "token": "itemColor", "type": "string", "desc": "面包屑项文字颜色", "descEn": "Text color of Breadcrumb item", "name": "", "nameEn": ""}, {"source": "Breadcrumb", "token": "lastItemColor", "type": "string", "desc": "最后一项文字颜色", "descEn": "Text color of the last item", "name": "", "nameEn": ""}, {"source": "Breadcrumb", "token": "linkColor", "type": "string", "desc": "链接文字颜色", "descEn": "Text color of link", "name": "", "nameEn": ""}, {"source": "Breadcrumb", "token": "linkHoverColor", "type": "string", "desc": "链接文字悬浮颜色", "descEn": "Color of hovered link", "name": "", "nameEn": ""}, {"source": "Breadcrumb", "token": "separatorColor", "type": "string", "desc": "分隔符颜色", "descEn": "Color of separator", "name": "", "nameEn": ""}, {"source": "Breadcrumb", "token": "separator<PERSON><PERSON><PERSON>", "type": "number", "desc": "分隔符外间距", "descEn": "Margin of separator", "name": "", "nameEn": ""}], "Button": [{"source": "<PERSON><PERSON>", "token": "borderColorDisabled", "type": "string", "desc": "禁用状态边框颜色", "descEn": "Border color of disabled button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "contentFontSize", "type": "number", "desc": "按钮内容字体大小", "descEn": "Font size of button content", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "contentFontSizeLG", "type": "number", "desc": "大号按钮内容字体大小", "descEn": "Font size of large button content", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "contentFontSizeSM", "type": "number", "desc": "小号按钮内容字体大小", "descEn": "Font size of small button content", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "contentLineHeight", "type": "number", "desc": "按钮内容字体行高", "descEn": "Line height of button content", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "contentLineHeightLG", "type": "number", "desc": "大号按钮内容字体行高", "descEn": "Line height of large button content", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "contentLineHeightSM", "type": "number", "desc": "小号按钮内容字体行高", "descEn": "Line height of small button content", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "dangerColor", "type": "string", "desc": "危险按钮文本颜色", "descEn": "Text color of danger button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "dangerShadow", "type": "string", "desc": "危险按钮阴影", "descEn": "Shadow of danger button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "defaultActiveBg", "type": "string", "desc": "默认按钮激活态背景色", "descEn": "Background color of default button when active", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "defaultActiveBorderColor", "type": "string", "desc": "默认按钮激活态边框颜色", "descEn": "Border color of default button when active", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "defaultActiveColor", "type": "string", "desc": "默认按钮激活态文字颜色", "descEn": "Text color of default button when active", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "defaultBg", "type": "string", "desc": "默认按钮背景色", "descEn": "Background color of default button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "defaultBorderColor", "type": "string", "desc": "默认按钮边框颜色", "descEn": "Border color of default button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "defaultColor", "type": "string", "desc": "默认按钮文本颜色", "descEn": "Text color of default button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "defaultGhostBorderColor", "type": "string", "desc": "默认幽灵按钮边框颜色", "descEn": "Border color of default ghost button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "defaultGhostColor", "type": "string", "desc": "默认幽灵按钮文本颜色", "descEn": "Text color of default ghost button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "defaultHoverBg", "type": "string", "desc": "默认按钮悬浮态背景色", "descEn": "Background color of default button when hover", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "defaultHoverBorderColor", "type": "string", "desc": "默认按钮悬浮态边框颜色", "descEn": "Border color of default button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "defaultHoverColor", "type": "string", "desc": "默认按钮悬浮态文本颜色", "descEn": "Text color of default button when hover", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "defaultShadow", "type": "string", "desc": "默认按钮阴影", "descEn": "Shadow of default button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "fontWeight", "type": "undefined | FontWeight", "desc": "文字字重", "descEn": "Font weight of text", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "ghostBg", "type": "string", "desc": "幽灵按钮背景色", "descEn": "Background color of ghost button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "groupBorderColor", "type": "string", "desc": "按钮组边框颜色", "descEn": "Border color of button group", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "linkHoverBg", "type": "string", "desc": "链接按钮悬浮态背景色", "descEn": "Background color of link button when hover", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "onlyIconSize", "type": "string | number", "desc": "只有图标的按钮图标尺寸", "descEn": "Icon size of button which only contains icon", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "onlyIconSizeLG", "type": "string | number", "desc": "大号只有图标的按钮图标尺寸", "descEn": "Icon size of large button which only contains icon", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "onlyIconSizeSM", "type": "string | number", "desc": "小号只有图标的按钮图标尺寸", "descEn": "Icon size of small button which only contains icon", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "paddingBlock", "type": "undefined | PaddingBlock<string | number>", "desc": "按钮纵向内间距", "descEn": "Vertical padding of button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "paddingBlockLG", "type": "undefined | PaddingBlock<string | number>", "desc": "大号按钮纵向内间距", "descEn": "Vertical padding of large button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "paddingBlockSM", "type": "undefined | PaddingBlock<string | number>", "desc": "小号按钮纵向内间距", "descEn": "Vertical padding of small button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "paddingInline", "type": "undefined | PaddingInline<string | number>", "desc": "按钮横向内间距", "descEn": "Horizontal padding of button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "paddingInlineLG", "type": "undefined | PaddingInline<string | number>", "desc": "大号按钮横向内间距", "descEn": "Horizontal padding of large button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "paddingInlineSM", "type": "undefined | PaddingInline<string | number>", "desc": "小号按钮横向内间距", "descEn": "Horizontal padding of small button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "primaryColor", "type": "string", "desc": "主要按钮文本颜色", "descEn": "Text color of primary button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "primaryShadow", "type": "string", "desc": "主要按钮阴影", "descEn": "Shadow of primary button", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "solidTextColor", "type": "string", "desc": "默认实心按钮的文本色", "descEn": "Default text color for solid buttons.", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "textHoverBg", "type": "string", "desc": "文本按钮悬浮态背景色", "descEn": "Background color of text button when hover", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "textTextActiveColor", "type": "string", "desc": "默认文本按钮激活态文字颜色", "descEn": "Default text color for text buttons on active", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "textTextColor", "type": "string", "desc": "默认文本按钮的文本色", "descEn": "Default text color for text buttons", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "textTextHoverColor", "type": "string", "desc": "默认文本按钮悬浮态文本颜色", "descEn": "Default text color for text buttons on hover", "name": "", "nameEn": ""}], "Calendar": [{"source": "Calendar", "token": "fullBg", "type": "string", "desc": "完整日历背景色", "descEn": "Background color of full calendar", "name": "", "nameEn": ""}, {"source": "Calendar", "token": "fullPanelBg", "type": "string", "desc": "完整日历面板背景色", "descEn": "Background color of full calendar panel", "name": "", "nameEn": ""}, {"source": "Calendar", "token": "itemActiveBg", "type": "string", "desc": "日期项选中背景色", "descEn": "Background color of selected date item", "name": "", "nameEn": ""}, {"source": "Calendar", "token": "miniContentHeight", "type": "string | number", "desc": "迷你日历内容高度", "descEn": "Height of mini calendar content", "name": "", "nameEn": ""}, {"source": "Calendar", "token": "monthControlWidth", "type": "string | number", "desc": "月选择器宽度", "descEn": "Width of month select", "name": "", "nameEn": ""}, {"source": "Calendar", "token": "yearControlWidth", "type": "string | number", "desc": "年选择器宽度", "descEn": "Width of year select", "name": "", "nameEn": ""}], "Card": [{"source": "Card", "token": "actionsBg", "type": "string", "desc": "操作区背景色", "descEn": "Background color of card actions", "name": "", "nameEn": ""}, {"source": "Card", "token": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "desc": "操作区每一项的外间距", "descEn": "Margin of each item in card actions", "name": "", "nameEn": ""}, {"source": "Card", "token": "bodyPadding", "type": "number", "desc": "卡片内边距", "descEn": "Padding of card body", "name": "", "nameEn": ""}, {"source": "Card", "token": "bodyPaddingSM", "type": "number", "desc": "小号卡片内边距", "descEn": "Padding of small card body", "name": "", "nameEn": ""}, {"source": "Card", "token": "extraColor", "type": "string", "desc": "额外区文字颜色", "descEn": "Text color of extra area", "name": "", "nameEn": ""}, {"source": "Card", "token": "headerBg", "type": "string", "desc": "卡片头部背景色", "descEn": "Background color of card header", "name": "", "nameEn": ""}, {"source": "Card", "token": "headerFontSize", "type": "string | number", "desc": "卡片头部文字大小", "descEn": "Font size of card header", "name": "", "nameEn": ""}, {"source": "Card", "token": "headerFontSizeSM", "type": "string | number", "desc": "小号卡片头部文字大小", "descEn": "Font size of small card header", "name": "", "nameEn": ""}, {"source": "Card", "token": "headerHeight", "type": "string | number", "desc": "卡片头部高度", "descEn": "Height of card header", "name": "", "nameEn": ""}, {"source": "Card", "token": "headerHeightSM", "type": "string | number", "desc": "小号卡片头部高度", "descEn": "Height of small card header", "name": "", "nameEn": ""}, {"source": "Card", "token": "headerPadding", "type": "number", "desc": "卡片头部内边距", "descEn": "Padding of card head", "name": "", "nameEn": ""}, {"source": "Card", "token": "headerPaddingSM", "type": "number", "desc": "小号卡片头部内边距", "descEn": "Padding of small card head", "name": "", "nameEn": ""}, {"source": "Card", "token": "tabsMarginBottom", "type": "number", "desc": "内置标签页组件下间距", "descEn": "Margin bottom of tabs component", "name": "", "nameEn": ""}], "Carousel": [{"source": "Carousel", "token": "arrowOffset", "type": "number", "desc": "切换箭头边距", "descEn": "arrows offset to Carousel edge", "name": "", "nameEn": ""}, {"source": "Carousel", "token": "arrowSize", "type": "number", "desc": "切换箭头大小", "descEn": "Size of arrows", "name": "", "nameEn": ""}, {"source": "Carousel", "token": "dotActiveWidth", "type": "string | number", "desc": "激活态指示点宽度", "descEn": "Width of active indicator", "name": "", "nameEn": ""}, {"source": "Carousel", "token": "dotGap", "type": "number", "desc": "指示点之间的间距", "descEn": "gap between indicator", "name": "", "nameEn": ""}, {"source": "Carousel", "token": "dotHeight", "type": "string | number", "desc": "指示点高度", "descEn": "Height of indicator", "name": "", "nameEn": ""}, {"source": "Carousel", "token": "dotOffset", "type": "number", "desc": "指示点距离边缘的距离", "descEn": "dot offset to Carousel edge", "name": "", "nameEn": ""}, {"source": "Carousel", "token": "dotWidth", "type": "string | number", "desc": "指示点宽度", "descEn": "Width of indicator", "name": "", "nameEn": ""}], "Cascader": [{"source": "<PERSON>r", "token": "controlItemWidth", "type": "string | number", "desc": "选项宽度", "descEn": "Width of item", "name": "", "nameEn": ""}, {"source": "<PERSON>r", "token": "controlWidth", "type": "string | number", "desc": "选择器宽度", "descEn": "Width of Cascader", "name": "", "nameEn": ""}, {"source": "<PERSON>r", "token": "dropdownHeight", "type": "string | number", "desc": "下拉菜单高度", "descEn": "Height of dropdown", "name": "", "nameEn": ""}, {"source": "<PERSON>r", "token": "menuPadding", "type": "undefined | Padding<string | number>", "desc": "选项菜单（单列）内间距", "descEn": "Padding of menu item (single column)", "name": "", "nameEn": ""}, {"source": "<PERSON>r", "token": "optionPadding", "type": "undefined | Padding<string | number>", "desc": "选项内间距", "descEn": "Padding of menu item", "name": "", "nameEn": ""}, {"source": "<PERSON>r", "token": "optionSelectedBg", "type": "string", "desc": "选项选中时背景色", "descEn": "Background color of selected item", "name": "", "nameEn": ""}, {"source": "<PERSON>r", "token": "optionSelectedColor", "type": "string", "desc": "选项选中时文本颜色", "descEn": "Text color when option is selected", "name": "", "nameEn": ""}, {"source": "<PERSON>r", "token": "optionSelectedFontWeight", "type": "undefined | FontWeight", "desc": "选项选中时字重", "descEn": "Font weight of selected item", "name": "", "nameEn": ""}], "Checkbox": [], "Collapse": [{"source": "Collapse", "token": "borderlessContentBg", "type": "string", "desc": "简约风格折叠面板的内容背景", "descEn": "Background of content in borderless style", "name": "", "nameEn": ""}, {"source": "Collapse", "token": "borderlessContentPadding", "type": "undefined | Padding<string | number>", "desc": "简约风格折叠面板的内容内边距", "descEn": "Padding of content in borderless style", "name": "", "nameEn": ""}, {"source": "Collapse", "token": "contentBg", "type": "string", "desc": "折叠面板内容背景", "descEn": "Background of content", "name": "", "nameEn": ""}, {"source": "Collapse", "token": "contentPadding", "type": "undefined | Padding<string | number>", "desc": "折叠面板内容内边距", "descEn": "Padding of content", "name": "", "nameEn": ""}, {"source": "Collapse", "token": "headerBg", "type": "string", "desc": "折叠面板头部背景", "descEn": "Background of header", "name": "", "nameEn": ""}, {"source": "Collapse", "token": "headerPadding", "type": "undefined | Padding<string | number>", "desc": "折叠面板头部内边距", "descEn": "Padding of header", "name": "", "nameEn": ""}], "ColorPicker": [], "DatePicker": [{"source": "DatePicker", "token": "activeBg", "type": "string", "desc": "输入框激活状态时背景颜色", "descEn": "Background color when the input box is activated", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "activeBorderColor", "type": "string", "desc": "激活态边框色", "descEn": "Active border color", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "activeShadow", "type": "string", "desc": "激活态阴影", "descEn": "Box-shadow when active", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "addonBg", "type": "string", "desc": "前/后置标签背景色", "descEn": "Background color of addon", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "cellActiveWithRangeBg", "type": "string", "desc": "选取范围内的单元格背景色", "descEn": "Background color of cell in range", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "cellBgDisabled", "type": "string", "desc": "单元格禁用态背景色", "descEn": "Background color of disabled cell", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "cellHeight", "type": "number", "desc": "单元格高度", "descEn": "Height of cell", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "cellHoverBg", "type": "string", "desc": "单元格悬浮态背景色", "descEn": "Background color of cell hover state", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "cellHoverWithRangeBg", "type": "string", "desc": "选取范围内的单元格悬浮态背景色", "descEn": "Background color of hovered cell in range", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "cellRangeBorderColor", "type": "string", "desc": "选取范围时单元格边框色", "descEn": "Border color of cell in range when picking", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "cellWidth", "type": "number", "desc": "单元格宽度", "descEn": "Width of cell", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "errorActiveShadow", "type": "string", "desc": "错误状态时激活态阴影", "descEn": "Box-shadow when active in error status", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "hoverBg", "type": "string", "desc": "输入框hover状态时背景颜色", "descEn": "Background color when the input box hovers", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "hoverBorderColor", "type": "string", "desc": "悬浮态边框色", "descEn": "Hover border color", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "inputFontSize", "type": "number", "desc": "字体大小", "descEn": "Font size", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "inputFontSizeLG", "type": "number", "desc": "大号字体大小", "descEn": "Font size of large", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "inputFontSizeSM", "type": "number", "desc": "小号字体大小", "descEn": "Font size of small", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "multipleItemBg", "type": "string", "desc": "多选标签背景色", "descEn": "Background color of multiple tag", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "multipleItemBorderColor", "type": "string", "desc": "多选标签边框色", "descEn": "Border color of multiple tag", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "multipleItemBorderColorDisabled", "type": "string", "desc": "多选标签禁用边框色", "descEn": "Border color of multiple tag when disabled", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "multipleItemColorDisabled", "type": "string", "desc": "多选标签禁用文本颜色", "descEn": "Text color of multiple tag when disabled", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "multipleItemHeight", "type": "number", "desc": "多选标签高度", "descEn": "Height of multiple tag", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "multipleItemHeightLG", "type": "number", "desc": "大号多选标签高度", "descEn": "Height of multiple tag with large size", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "multipleItemHeightSM", "type": "number", "desc": "小号多选标签高度", "descEn": "Height of multiple tag with small size", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "multipleSelectorBgDisabled", "type": "string", "desc": "多选框禁用背景", "descEn": "Background color of multiple selector when disabled", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "paddingBlock", "type": "number", "desc": "输入框纵向内边距", "descEn": "Vertical padding of input", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "paddingBlockLG", "type": "number", "desc": "大号输入框纵向内边距", "descEn": "Vertical padding of large input", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "paddingBlockSM", "type": "number", "desc": "小号输入框纵向内边距", "descEn": "Vertical padding of small input", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "paddingInline", "type": "number", "desc": "输入框横向内边距", "descEn": "Horizontal padding of input", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "paddingInlineLG", "type": "number", "desc": "大号输入框横向内边距", "descEn": "Horizontal padding of large input", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "paddingInlineSM", "type": "number", "desc": "小号输入框横向内边距", "descEn": "Horizontal padding of small input", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "presetsMaxWidth", "type": "number", "desc": "预设区域最大宽度", "descEn": "Max width of preset area", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "presetsWidth", "type": "number", "desc": "预设区域宽度", "descEn": "Width of preset area", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "textHeight", "type": "number", "desc": "单元格文本高度", "descEn": "Height of cell text", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "timeCellHeight", "type": "number", "desc": "时间单元格高度", "descEn": "Height of time cell", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "timeColumnHeight", "type": "number", "desc": "时间列高度", "descEn": "Height of time column", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "timeColumn<PERSON><PERSON><PERSON>", "type": "number", "desc": "时间列宽度", "descEn": "Width of time column", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "warningActiveShadow", "type": "string", "desc": "警告状态时激活态阴影", "descEn": "Box-shadow when active in warning status", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "withoutTimeCellHeight", "type": "number", "desc": "十年/年/季/月/周单元格高度", "descEn": "Height of decade/year/quarter/month/week cell", "name": "", "nameEn": ""}, {"source": "DatePicker", "token": "zIndexPopup", "type": "number", "desc": "弹窗 z-index", "descEn": "z-index of popup", "name": "", "nameEn": ""}], "Descriptions": [{"source": "Descriptions", "token": "colonMarginLeft", "type": "number", "desc": "冒号左间距", "descEn": "Left margin of colon", "name": "", "nameEn": ""}, {"source": "Descriptions", "token": "colonMarginRight", "type": "number", "desc": "冒号右间距", "descEn": "Right margin of colon", "name": "", "nameEn": ""}, {"source": "Descriptions", "token": "contentColor", "type": "string", "desc": "内容区域文字颜色", "descEn": "Text color of content", "name": "", "nameEn": ""}, {"source": "Descriptions", "token": "extraColor", "type": "string", "desc": "额外区域文字颜色", "descEn": "Text color of extra area", "name": "", "nameEn": ""}, {"source": "Descriptions", "token": "itemPaddingBottom", "type": "number", "desc": "子项下间距", "descEn": "Bottom padding of item", "name": "", "nameEn": ""}, {"source": "Descriptions", "token": "itemPaddingEnd", "type": "number", "desc": "子项结束间距", "descEn": "End padding of item", "name": "", "nameEn": ""}, {"source": "Descriptions", "token": "labelBg", "type": "string", "desc": "标签背景色", "descEn": "Background color of label", "name": "", "nameEn": ""}, {"source": "Descriptions", "token": "labelColor", "type": "string", "desc": "标签文字颜色", "descEn": "Text color of label", "name": "", "nameEn": ""}, {"source": "Descriptions", "token": "titleColor", "type": "string", "desc": "标题文字颜色", "descEn": "Text color of title", "name": "", "nameEn": ""}, {"source": "Descriptions", "token": "titleMarginBottom", "type": "number", "desc": "标题下间距", "descEn": "Bottom margin of title", "name": "", "nameEn": ""}], "Divider": [{"source": "Divider", "token": "<PERSON><PERSON><PERSON><PERSON>", "type": "number", "desc": "文本与边缘距离，取值 0 ～ 1", "descEn": "Distance between text and edge, which should be a number between 0 and 1.", "name": "", "nameEn": ""}, {"source": "Divider", "token": "textPaddingInline", "type": "undefined | PaddingInline<string | number>", "desc": "文本横向内间距", "descEn": "Horizontal padding of text", "name": "", "nameEn": ""}, {"source": "Divider", "token": "verticalMarginInline", "type": "undefined | MarginInline<string | number>", "desc": "纵向分割线的横向外间距", "descEn": "Horizontal margin of vertical Divider", "name": "", "nameEn": ""}], "Drawer": [{"source": "Drawer", "token": "footerPaddingBlock", "type": "number", "desc": "底部区域纵向内间距", "descEn": "Vertical padding of footer", "name": "", "nameEn": ""}, {"source": "Drawer", "token": "footerPaddingInline", "type": "number", "desc": "底部区域横向内间距", "descEn": "Horizontal padding of footer", "name": "", "nameEn": ""}, {"source": "Drawer", "token": "zIndexPopup", "type": "number", "desc": "弹窗 z-index", "descEn": "z-index of drawer", "name": "", "nameEn": ""}], "Dropdown": [{"source": "Dropdown", "token": "paddingBlock", "type": "undefined | PaddingBlock<string | number>", "desc": "下拉菜单纵向内边距", "descEn": "Vertical padding of dropdown", "name": "", "nameEn": ""}, {"source": "Dropdown", "token": "zIndexPopup", "type": "number", "desc": "下拉菜单 z-index", "descEn": "z-index of dropdown", "name": "", "nameEn": ""}], "Empty": [], "Flex": [], "FloatButton": [], "Form": [{"source": "Form", "token": "inlineItemMarginBottom", "type": "number", "desc": "行内布局表单项间距", "descEn": "Inline layout form item margin bottom", "name": "", "nameEn": ""}, {"source": "Form", "token": "itemMarginBottom", "type": "number", "desc": "表单项间距", "descEn": "Form item margin bottom", "name": "", "nameEn": ""}, {"source": "Form", "token": "labelColonMarginInlineEnd", "type": "number", "desc": "标签冒号后间距", "descEn": "Label colon margin-inline-end", "name": "", "nameEn": ""}, {"source": "Form", "token": "labelColonMarginInlineStart", "type": "number", "desc": "标签冒号前间距", "descEn": "Label colon margin-inline-start", "name": "", "nameEn": ""}, {"source": "Form", "token": "labelColor", "type": "string", "desc": "标签颜色", "descEn": "Label color", "name": "", "nameEn": ""}, {"source": "Form", "token": "labelFontSize", "type": "number", "desc": "标签字体大小", "descEn": "Label font size", "name": "", "nameEn": ""}, {"source": "Form", "token": "labelHeight", "type": "string | number", "desc": "标签高度", "descEn": "Label height", "name": "", "nameEn": ""}, {"source": "Form", "token": "labelRequiredMarkColor", "type": "string", "desc": "必填项标记颜色", "descEn": "Required mark color", "name": "", "nameEn": ""}, {"source": "Form", "token": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "undefined | Margin<string | number>", "desc": "垂直布局标签外边距", "descEn": "Vertical layout label margin", "name": "", "nameEn": ""}, {"source": "Form", "token": "verticalLabelPadding", "type": "undefined | Padding<string | number>", "desc": "垂直布局标签内边距", "descEn": "Vertical layout label padding", "name": "", "nameEn": ""}], "Grid": [], "Image": [{"source": "Image", "token": "previewOperationColor", "type": "string", "desc": "预览操作图标颜色", "descEn": "Color of preview operation icon", "name": "", "nameEn": ""}, {"source": "Image", "token": "previewOperationColorDisabled", "type": "string", "desc": "预览操作图标禁用颜色", "descEn": "Disabled color of preview operation icon", "name": "", "nameEn": ""}, {"source": "Image", "token": "previewOperationHoverColor", "type": "string", "desc": "预览操作图标悬浮颜色", "descEn": "Color of hovered preview operation icon", "name": "", "nameEn": ""}, {"source": "Image", "token": "previewOperationSize", "type": "number", "desc": "预览操作图标大小", "descEn": "Size of preview operation icon", "name": "", "nameEn": ""}, {"source": "Image", "token": "zIndexPopup", "type": "number", "desc": "预览浮层 z-index", "descEn": "z-index of preview popup", "name": "", "nameEn": ""}], "InputNumber": [{"source": "InputNumber", "token": "activeBg", "type": "string", "desc": "输入框激活状态时背景颜色", "descEn": "Background color when the input box is activated", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "activeBorderColor", "type": "string", "desc": "激活态边框色", "descEn": "Active border color", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "activeShadow", "type": "string", "desc": "激活态阴影", "descEn": "Box-shadow when active", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "addonBg", "type": "string", "desc": "前/后置标签背景色", "descEn": "Background color of addon", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "controlWidth", "type": "number", "desc": "输入框宽度", "descEn": "Width of input", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "errorActiveShadow", "type": "string", "desc": "错误状态时激活态阴影", "descEn": "Box-shadow when active in error status", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "filledHandleBg", "type": "string", "desc": "面性变体操作按钮背景色", "descEn": "Background color of handle in filled variant", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "handleActiveBg", "type": "string", "desc": "操作按钮激活背景色", "descEn": "Active background color of handle", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "handleBg", "type": "string", "desc": "操作按钮背景色", "descEn": "Background color of handle", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "handleBorderColor", "type": "string", "desc": "操作按钮边框颜色", "descEn": "Border color of handle", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "handleFontSize", "type": "number", "desc": "操作按钮图标大小", "descEn": "Icon size of control button", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "handleHoverColor", "type": "string", "desc": "操作按钮悬浮颜色", "descEn": "Hover color of handle", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "handleVisible", "type": "true | \"auto\"", "desc": "操作按钮可见性", "descEn": "Handle visible", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "handleWidth", "type": "number", "desc": "操作按钮宽度", "descEn": "Width of control button", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "hoverBg", "type": "string", "desc": "输入框hover状态时背景颜色", "descEn": "Background color when the input box hovers", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "hoverBorderColor", "type": "string", "desc": "悬浮态边框色", "descEn": "Hover border color", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "inputFontSize", "type": "number", "desc": "字体大小", "descEn": "Font size", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "inputFontSizeLG", "type": "number", "desc": "大号字体大小", "descEn": "Font size of large", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "inputFontSizeSM", "type": "number", "desc": "小号字体大小", "descEn": "Font size of small", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "paddingBlock", "type": "number", "desc": "输入框纵向内边距", "descEn": "Vertical padding of input", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "paddingBlockLG", "type": "number", "desc": "大号输入框纵向内边距", "descEn": "Vertical padding of large input", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "paddingBlockSM", "type": "number", "desc": "小号输入框纵向内边距", "descEn": "Vertical padding of small input", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "paddingInline", "type": "number", "desc": "输入框横向内边距", "descEn": "Horizontal padding of input", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "paddingInlineLG", "type": "number", "desc": "大号输入框横向内边距", "descEn": "Horizontal padding of large input", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "paddingInlineSM", "type": "number", "desc": "小号输入框横向内边距", "descEn": "Horizontal padding of small input", "name": "", "nameEn": ""}, {"source": "InputNumber", "token": "warningActiveShadow", "type": "string", "desc": "警告状态时激活态阴影", "descEn": "Box-shadow when active in warning status", "name": "", "nameEn": ""}], "Input": [{"source": "Input", "token": "activeBg", "type": "string", "desc": "输入框激活状态时背景颜色", "descEn": "Background color when the input box is activated", "name": "", "nameEn": ""}, {"source": "Input", "token": "activeBorderColor", "type": "string", "desc": "激活态边框色", "descEn": "Active border color", "name": "", "nameEn": ""}, {"source": "Input", "token": "activeShadow", "type": "string", "desc": "激活态阴影", "descEn": "Box-shadow when active", "name": "", "nameEn": ""}, {"source": "Input", "token": "addonBg", "type": "string", "desc": "前/后置标签背景色", "descEn": "Background color of addon", "name": "", "nameEn": ""}, {"source": "Input", "token": "errorActiveShadow", "type": "string", "desc": "错误状态时激活态阴影", "descEn": "Box-shadow when active in error status", "name": "", "nameEn": ""}, {"source": "Input", "token": "hoverBg", "type": "string", "desc": "输入框hover状态时背景颜色", "descEn": "Background color when the input box hovers", "name": "", "nameEn": ""}, {"source": "Input", "token": "hoverBorderColor", "type": "string", "desc": "悬浮态边框色", "descEn": "Hover border color", "name": "", "nameEn": ""}, {"source": "Input", "token": "inputFontSize", "type": "number", "desc": "字体大小", "descEn": "Font size", "name": "", "nameEn": ""}, {"source": "Input", "token": "inputFontSizeLG", "type": "number", "desc": "大号字体大小", "descEn": "Font size of large", "name": "", "nameEn": ""}, {"source": "Input", "token": "inputFontSizeSM", "type": "number", "desc": "小号字体大小", "descEn": "Font size of small", "name": "", "nameEn": ""}, {"source": "Input", "token": "paddingBlock", "type": "number", "desc": "输入框纵向内边距", "descEn": "Vertical padding of input", "name": "", "nameEn": ""}, {"source": "Input", "token": "paddingBlockLG", "type": "number", "desc": "大号输入框纵向内边距", "descEn": "Vertical padding of large input", "name": "", "nameEn": ""}, {"source": "Input", "token": "paddingBlockSM", "type": "number", "desc": "小号输入框纵向内边距", "descEn": "Vertical padding of small input", "name": "", "nameEn": ""}, {"source": "Input", "token": "paddingInline", "type": "number", "desc": "输入框横向内边距", "descEn": "Horizontal padding of input", "name": "", "nameEn": ""}, {"source": "Input", "token": "paddingInlineLG", "type": "number", "desc": "大号输入框横向内边距", "descEn": "Horizontal padding of large input", "name": "", "nameEn": ""}, {"source": "Input", "token": "paddingInlineSM", "type": "number", "desc": "小号输入框横向内边距", "descEn": "Horizontal padding of small input", "name": "", "nameEn": ""}, {"source": "Input", "token": "warningActiveShadow", "type": "string", "desc": "警告状态时激活态阴影", "descEn": "Box-shadow when active in warning status", "name": "", "nameEn": ""}], "Layout": [{"source": "Layout", "token": "bodyBg", "type": "string", "desc": "主体部分背景色", "descEn": "Background Color of body", "name": "", "nameEn": ""}, {"source": "Layout", "token": "footerBg", "type": "string", "desc": "页脚背景色", "descEn": "Background Color of footer", "name": "", "nameEn": ""}, {"source": "Layout", "token": "footerPadding", "type": "undefined | Padding<string | number>", "desc": "页脚内边距", "descEn": "Padding of footer", "name": "", "nameEn": ""}, {"source": "Layout", "token": "headerBg", "type": "string", "desc": "顶部背景色", "descEn": "Background Color of header", "name": "", "nameEn": ""}, {"source": "Layout", "token": "headerColor", "type": "string", "desc": "顶部文字颜色", "descEn": "Text color of header", "name": "", "nameEn": ""}, {"source": "Layout", "token": "headerHeight", "type": "string | number", "desc": "顶部高度", "descEn": "Height of header", "name": "", "nameEn": ""}, {"source": "Layout", "token": "headerPadding", "type": "undefined | Padding<string | number>", "desc": "顶部内边距", "descEn": "Padding of header", "name": "", "nameEn": ""}, {"source": "Layout", "token": "lightSiderBg", "type": "string", "desc": "亮色主题侧边栏背景色", "descEn": "Background Color of light theme sider", "name": "", "nameEn": ""}, {"source": "Layout", "token": "lightTriggerBg", "type": "string", "desc": "亮色主题侧边栏开关背景色", "descEn": "Background Color of light theme sider trigger", "name": "", "nameEn": ""}, {"source": "Layout", "token": "lightTriggerColor", "type": "string", "desc": "亮色主题侧边栏开关颜色", "descEn": "Color of light theme sider trigger", "name": "", "nameEn": ""}, {"source": "Layout", "token": "siderBg", "type": "string", "desc": "侧边栏背景色", "descEn": "Background Color of sider", "name": "", "nameEn": ""}, {"source": "Layout", "token": "triggerBg", "type": "string", "desc": "侧边栏开关背景色", "descEn": "Background Color of sider trigger", "name": "", "nameEn": ""}, {"source": "Layout", "token": "triggerColor", "type": "string", "desc": "侧边栏开关颜色", "descEn": "Color of sider trigger", "name": "", "nameEn": ""}, {"source": "Layout", "token": "triggerHeight", "type": "string | number", "desc": "侧边栏开关高度", "descEn": "Height of sider trigger", "name": "", "nameEn": ""}, {"source": "Layout", "token": "zeroTriggerHeight", "type": "number", "desc": "collapse 为 0 时侧边栏开关高度", "descEn": "Height of sider trigger when collapse is 0", "name": "", "nameEn": ""}, {"source": "Layout", "token": "zeroTriggerWidth", "type": "number", "desc": "collapse 为 0 时侧边栏开关宽度", "descEn": "Width of sider trigger when collapse is 0", "name": "", "nameEn": ""}], "List": [{"source": "List", "token": "avatarMarginRight", "type": "undefined | MarginRight<string | number>", "desc": "头像右间距", "descEn": "Right margin of avatar", "name": "", "nameEn": ""}, {"source": "List", "token": "contentWidth", "type": "string | number", "desc": "内容宽度", "descEn": "Width of content", "name": "", "nameEn": ""}, {"source": "List", "token": "descriptionFontSize", "type": "number", "desc": "描述文字大小", "descEn": "Font size of description", "name": "", "nameEn": ""}, {"source": "List", "token": "emptyTextPadding", "type": "undefined | Padding<string | number>", "desc": "空文本内边距", "descEn": "Padding of empty text", "name": "", "nameEn": ""}, {"source": "List", "token": "footerBg", "type": "string", "desc": "底部区域背景色", "descEn": "Background color of footer", "name": "", "nameEn": ""}, {"source": "List", "token": "headerBg", "type": "string", "desc": "头部区域背景色", "descEn": "Background color of header", "name": "", "nameEn": ""}, {"source": "List", "token": "itemPadding", "type": "string", "desc": "列表项内间距", "descEn": "Padding of item", "name": "", "nameEn": ""}, {"source": "List", "token": "itemPaddingLG", "type": "string", "desc": "大号列表项内间距", "descEn": "Padding of large item", "name": "", "nameEn": ""}, {"source": "List", "token": "itemPaddingSM", "type": "string", "desc": "小号列表项内间距", "descEn": "Padding of small item", "name": "", "nameEn": ""}, {"source": "List", "token": "metaMarginBottom", "type": "undefined | MarginBottom<string | number>", "desc": "Meta 下间距", "descEn": "Margin bottom of meta", "name": "", "nameEn": ""}, {"source": "List", "token": "titleMarginBottom", "type": "undefined | MarginBottom<string | number>", "desc": "标题下间距", "descEn": "<PERSON><PERSON> bottom of title", "name": "", "nameEn": ""}], "Mentions": [{"source": "Mentions", "token": "activeBg", "type": "string", "desc": "输入框激活状态时背景颜色", "descEn": "Background color when the input box is activated", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "activeBorderColor", "type": "string", "desc": "激活态边框色", "descEn": "Active border color", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "activeShadow", "type": "string", "desc": "激活态阴影", "descEn": "Box-shadow when active", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "addonBg", "type": "string", "desc": "前/后置标签背景色", "descEn": "Background color of addon", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "controlItemWidth", "type": "string | number", "desc": "菜单项高度", "descEn": "Height of menu item", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "dropdownHeight", "type": "string | number", "desc": "弹层高度", "descEn": "Height of popup", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "errorActiveShadow", "type": "string", "desc": "错误状态时激活态阴影", "descEn": "Box-shadow when active in error status", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "hoverBg", "type": "string", "desc": "输入框hover状态时背景颜色", "descEn": "Background color when the input box hovers", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "hoverBorderColor", "type": "string", "desc": "悬浮态边框色", "descEn": "Hover border color", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "inputFontSize", "type": "number", "desc": "字体大小", "descEn": "Font size", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "inputFontSizeLG", "type": "number", "desc": "大号字体大小", "descEn": "Font size of large", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "inputFontSizeSM", "type": "number", "desc": "小号字体大小", "descEn": "Font size of small", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "paddingBlock", "type": "number", "desc": "输入框纵向内边距", "descEn": "Vertical padding of input", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "paddingBlockLG", "type": "number", "desc": "大号输入框纵向内边距", "descEn": "Vertical padding of large input", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "paddingBlockSM", "type": "number", "desc": "小号输入框纵向内边距", "descEn": "Vertical padding of small input", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "paddingInline", "type": "number", "desc": "输入框横向内边距", "descEn": "Horizontal padding of input", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "paddingInlineLG", "type": "number", "desc": "大号输入框横向内边距", "descEn": "Horizontal padding of large input", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "paddingInlineSM", "type": "number", "desc": "小号输入框横向内边距", "descEn": "Horizontal padding of small input", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "warningActiveShadow", "type": "string", "desc": "警告状态时激活态阴影", "descEn": "Box-shadow when active in warning status", "name": "", "nameEn": ""}, {"source": "Mentions", "token": "zIndexPopup", "type": "number", "desc": "弹层 z-index", "descEn": "z-index of popup", "name": "", "nameEn": ""}], "Menu": [{"source": "<PERSON><PERSON>", "token": "activeBarBorderWidth", "type": "string | number", "desc": "菜单项指示条边框宽度", "descEn": "Border width of menu item active bar", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "activeBarHeight", "type": "number", "desc": "菜单项指示条高度", "descEn": "Height of menu item active bar", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "activeBarWidth", "type": "string | number", "desc": "菜单项指示条宽度", "descEn": "Width of menu item active bar", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "collapsedIconSize", "type": "number", "desc": "收起时图标尺寸", "descEn": "Size of icon when collapsed", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "collapsedWidth", "type": "string | number", "desc": "收起后的宽度", "descEn": "Width when collapsed", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "dangerItemActiveBg", "type": "string", "desc": "危险菜单项激活态背景色", "descEn": "Background color of danger menu item when active", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "dangerItemColor", "type": "string", "desc": "危险菜单项文字颜色", "descEn": "Color of danger menu item text", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "dangerItemHoverColor", "type": "string", "desc": "危险菜单项文字悬浮颜色", "descEn": "Hover color of danger menu item text", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "dangerItemSelectedBg", "type": "string", "desc": "危险菜单项选中背景色", "descEn": "Background color of selected danger menu item", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "dangerItemSelectedColor", "type": "string", "desc": "危险菜单项文字选中颜色", "descEn": "Color of selected danger menu item text", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "darkDangerItemActiveBg", "type": "string", "desc": "暗色模式下的危险菜单项激活态背景", "descEn": "Background of active danger menu item in dark mode", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "darkDangerItemColor", "type": "string", "desc": "暗色模式下的危险菜单项文字颜色", "descEn": "Color of danger menu item text in dark mode", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "darkDangerItemHoverColor", "type": "string", "desc": "暗色模式下的危险菜单项悬浮文字背景", "descEn": "Background of hovered danger menu item in dark mode", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "darkDangerItemSelectedBg", "type": "string", "desc": "暗色模式下的危险菜单项选中背景", "descEn": "Background of active danger menu item in dark mode", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "darkDangerItemSelectedColor", "type": "string", "desc": "暗色模式下的危险菜单项选中文字颜色", "descEn": "Color of selected danger menu item in dark mode", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "darkGroupTitleColor", "type": "string", "desc": "暗色模式下的分组标题文字颜色", "descEn": "Color of group title text in dark mode", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "darkItemBg", "type": "string", "desc": "暗色模式下的菜单项背景", "descEn": "Background of menu item in dark mode", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "darkItemColor", "type": "string", "desc": "暗色模式下的菜单项文字颜色", "descEn": "Color of menu item text in dark mode", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "darkItemDisabledColor", "type": "string", "desc": "暗色模式下的菜单项禁用颜色", "descEn": "Color of disabled menu item in dark mode", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "darkItemHoverBg", "type": "string", "desc": "暗色模式下的菜单项悬浮背景", "descEn": "Background of hovered menu item in dark mode", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "darkItemHoverColor", "type": "string", "desc": "暗色模式下的菜单项悬浮颜色", "descEn": "Color of hovered menu item in dark mode", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "darkItemSelectedBg", "type": "string", "desc": "暗色模式下的菜单项选中背景", "descEn": "Background of active menu item in dark mode", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "darkItemSelectedColor", "type": "string", "desc": "暗色模式下的菜单项选中颜色", "descEn": "Color of selected menu item in dark mode", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "darkPopupBg", "type": "string", "desc": "暗色模式下的浮层菜单的背景颜色", "descEn": "The background color of the overlay menu in dark mode.", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "darkSubMenuItemBg", "type": "string", "desc": "暗色模式下的子菜单项背景", "descEn": "Background of submenu item in dark mode", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "dropdownWidth", "type": "string | number", "desc": "弹出菜单的宽度", "descEn": "Width of popup menu", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "groupTitleColor", "type": "string", "desc": "分组标题文字颜色", "descEn": "Color of group title text", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "groupTitleFontSize", "type": "number", "desc": "分组标题文字大小", "descEn": "font-size of group title", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "groupTitleLineHeight", "type": "string | number", "desc": "分组标题文字高度", "descEn": "line-height of group title", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "horizontalItemBorderRadius", "type": "number", "desc": "横向菜单项圆角", "descEn": "Border radius of horizontal menu item", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "horizontalItemHoverBg", "type": "string", "desc": "横向菜单项横悬浮态背景色", "descEn": "Background color of horizontal menu item when hover", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "horizontalItemHoverColor", "type": "string", "desc": "水平菜单项文字悬浮颜色", "descEn": "Hover color of horizontal menu item text", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "horizontalItemSelectedBg", "type": "string", "desc": "水平菜单项选中态背景色", "descEn": "Background color of horizontal menu item when selected", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "horizontalItemSelectedColor", "type": "string", "desc": "水平菜单项文字选中颜色", "descEn": "Color of selected horizontal menu item text", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "horizontalLineHeight", "type": "undefined | LineHeight<string | number>", "desc": "横向菜单行高", "descEn": "LineHeight of horizontal menu item", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "iconMarginInlineEnd", "type": "undefined | MarginInlineEnd<string | number>", "desc": "图标与文字间距", "descEn": "Spacing between icon and text", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "iconSize", "type": "number", "desc": "图标尺寸", "descEn": "Size of icon", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "itemActiveBg", "type": "string", "desc": "菜单项激活态背景色", "descEn": "Background color of menu item when active", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "itemBg", "type": "string", "desc": "菜单项背景色", "descEn": "", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "itemBorderRadius", "type": "number", "desc": "菜单项的圆角", "descEn": "Radius of menu item", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "itemColor", "type": "string", "desc": "菜单项文字颜色", "descEn": "Color of menu item text", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "itemDisabledColor", "type": "string", "desc": "菜单项文字禁用颜色", "descEn": "Color of disabled menu item text", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "itemHeight", "type": "string | number", "desc": "菜单项高度", "descEn": "Height of menu item", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "itemHoverBg", "type": "string", "desc": "菜单项悬浮态背景色", "descEn": "Background color of menu item when hover", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "itemHoverColor", "type": "string", "desc": "菜单项文字悬浮颜色", "descEn": "Hover color of menu item text", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "itemMarginBlock", "type": "undefined | MarginBlock<string | number>", "desc": "菜单项纵向外间距", "descEn": "margin-block of menu item", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "itemMarginInline", "type": "number", "desc": "菜单项横向外间距", "descEn": "Horizontal margin of menu item", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "itemPaddingInline", "type": "undefined | PaddingInline<string | number>", "desc": "菜单项横向内间距", "descEn": "padding-inline of menu item", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "itemSelectedBg", "type": "string", "desc": "菜单项选中态背景色", "descEn": "Background color of menu item when selected", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "itemSelectedColor", "type": "string", "desc": "菜单项文字选中颜色", "descEn": "Color of selected menu item text", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "popupBg", "type": "string", "desc": "弹出框背景色", "descEn": "Background color of popup", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "subMenuItemBg", "type": "string", "desc": "子菜单项背景色", "descEn": "Background color of sub-menu item", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "subMenuItemBorderRadius", "type": "number", "desc": "子菜单项的圆角", "descEn": "Radius of sub-menu item", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "subMenuItemSelectedColor", "type": "string", "desc": "子菜单内有选中项时，子菜单标题色", "descEn": "Color of submenu title when submenu has selected item", "name": "", "nameEn": ""}, {"source": "<PERSON><PERSON>", "token": "zIndexPopup", "type": "number", "desc": "弹出菜单的 z-index", "descEn": "z-index of popup menu", "name": "", "nameEn": ""}], "Message": [{"source": "Message", "token": "contentBg", "type": "string", "desc": "提示框背景色", "descEn": "Background color of Message", "name": "", "nameEn": ""}, {"source": "Message", "token": "contentPadding", "type": "undefined | Padding<string | number>", "desc": "提示框内边距", "descEn": "Padding of Message", "name": "", "nameEn": ""}, {"source": "Message", "token": "zIndexPopup", "type": "number", "desc": "提示框 z-index", "descEn": "z-index of Message", "name": "", "nameEn": ""}], "Modal": [{"source": "Modal", "token": "contentBg", "type": "string", "desc": "内容区域背景色", "descEn": "Background color of content", "name": "", "nameEn": ""}, {"source": "Modal", "token": "footerBg", "type": "string", "desc": "底部区域背景色", "descEn": "Background color of footer", "name": "", "nameEn": ""}, {"source": "Modal", "token": "headerBg", "type": "string", "desc": "顶部背景色", "descEn": "Background color of header", "name": "", "nameEn": ""}, {"source": "Modal", "token": "titleColor", "type": "string", "desc": "标题字体颜色", "descEn": "Font color of title", "name": "", "nameEn": ""}, {"source": "Modal", "token": "titleFontSize", "type": "number", "desc": "标题字体大小", "descEn": "Font size of title", "name": "", "nameEn": ""}, {"source": "Modal", "token": "titleLineHeight", "type": "string | number", "desc": "标题行高", "descEn": "Line height of title", "name": "", "nameEn": ""}], "Notification": [{"source": "Notification", "token": "width", "type": "string | number", "desc": "提醒框宽度", "descEn": "Width of Notification", "name": "", "nameEn": ""}, {"source": "Notification", "token": "zIndexPopup", "type": "number", "desc": "提醒框 z-index", "descEn": "z-index of Notification", "name": "", "nameEn": ""}], "Pagination": [{"source": "Pagination", "token": "itemActiveBg", "type": "string", "desc": "页码激活态背景色", "descEn": "Background color of active Pagination item", "name": "", "nameEn": ""}, {"source": "Pagination", "token": "itemActiveBgDisabled", "type": "string", "desc": "页码激活态禁用状态背景色", "descEn": "Background color of disabled active Pagination item", "name": "", "nameEn": ""}, {"source": "Pagination", "token": "itemActiveColorDisabled", "type": "string", "desc": "页码激活态禁用状态文字颜色", "descEn": "Text color of disabled active Pagination item", "name": "", "nameEn": ""}, {"source": "Pagination", "token": "itemBg", "type": "string", "desc": "页码选项背景色", "descEn": "Background color of Pagination item", "name": "", "nameEn": ""}, {"source": "Pagination", "token": "itemInputBg", "type": "string", "desc": "输入框背景色", "descEn": "Background color of input", "name": "", "nameEn": ""}, {"source": "Pagination", "token": "itemLinkBg", "type": "string", "desc": "页码链接背景色", "descEn": "Background color of Pagination item link", "name": "", "nameEn": ""}, {"source": "Pagination", "token": "itemSize", "type": "number", "desc": "页码尺寸", "descEn": "Size of Pagination item", "name": "", "nameEn": ""}, {"source": "Pagination", "token": "itemSizeSM", "type": "number", "desc": "小号页码尺寸", "descEn": "Size of small Pagination item", "name": "", "nameEn": ""}, {"source": "Pagination", "token": "miniOptionsSizeChangerTop", "type": "number", "desc": "每页展示数量选择器 top", "descEn": "Top of Pagination size changer", "name": "", "nameEn": ""}], "Popconfirm": [{"source": "Popconfirm", "token": "zIndexPopup", "type": "number", "desc": "确认框 z-index", "descEn": "z-index of Popconfirm", "name": "", "nameEn": ""}], "Popover": [{"source": "Popover", "token": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string | number", "desc": "气泡卡片标题最小宽度", "descEn": "Min width of Popover title", "name": "", "nameEn": ""}, {"source": "Popover", "token": "zIndexPopup", "type": "number", "desc": "气泡卡片 z-index", "descEn": "z-index of Popover", "name": "", "nameEn": ""}], "Progress": [{"source": "Progress", "token": "circleIconFontSize", "type": "string", "desc": "圆形进度条图标大小", "descEn": "Icon size of circular progress bar", "name": "", "nameEn": ""}, {"source": "Progress", "token": "circleTextColor", "type": "string", "desc": "圆形进度条文字颜色", "descEn": "Text color of circular progress bar", "name": "", "nameEn": ""}, {"source": "Progress", "token": "circleTextFontSize", "type": "string", "desc": "圆形进度条文本大小", "descEn": "Text size of circular progress bar", "name": "", "nameEn": ""}, {"source": "Progress", "token": "defaultColor", "type": "string", "desc": "进度条默认颜色", "descEn": "Default color of progress bar", "name": "", "nameEn": ""}, {"source": "Progress", "token": "lineBorderRadius", "type": "number", "desc": "条状进度条圆角", "descEn": "Border radius of line progress bar", "name": "", "nameEn": ""}, {"source": "Progress", "token": "remainingColor", "type": "string", "desc": "进度条剩余部分颜色", "descEn": "Color of remaining part of progress bar", "name": "", "nameEn": ""}], "QrCode": [], "Qrcode": [], "Radio": [{"source": "Radio", "token": "buttonBg", "type": "string", "desc": "单选框按钮背景色", "descEn": "Background color of Radio button", "name": "", "nameEn": ""}, {"source": "Radio", "token": "buttonCheckedBg", "type": "string", "desc": "单选框按钮选中背景色", "descEn": "Background color of checked Radio button", "name": "", "nameEn": ""}, {"source": "Radio", "token": "buttonCheckedBgDisabled", "type": "string", "desc": "单选框按钮选中并禁用时的背景色", "descEn": "Background color of checked and disabled Radio button", "name": "", "nameEn": ""}, {"source": "Radio", "token": "buttonCheckedColorDisabled", "type": "string", "desc": "单选框按钮选中并禁用时的文本颜色", "descEn": "Color of checked and disabled Radio button text", "name": "", "nameEn": ""}, {"source": "Radio", "token": "buttonColor", "type": "string", "desc": "单选框按钮文本颜色", "descEn": "Color of Radio button text", "name": "", "nameEn": ""}, {"source": "Radio", "token": "buttonPaddingInline", "type": "number", "desc": "单选框按钮横向内间距", "descEn": "Horizontal padding of Radio button", "name": "", "nameEn": ""}, {"source": "Radio", "token": "buttonSolidCheckedActiveBg", "type": "string", "desc": "单选框实色按钮选中时的激活态背景色", "descEn": "Background color of checked solid Radio button text when active", "name": "", "nameEn": ""}, {"source": "Radio", "token": "buttonSolidCheckedBg", "type": "string", "desc": "单选框实色按钮选中时的背景色", "descEn": "Background color of checked solid Radio button text", "name": "", "nameEn": ""}, {"source": "Radio", "token": "buttonSolidCheckedColor", "type": "string", "desc": "单选框实色按钮选中时的文本颜色", "descEn": "Color of checked solid Radio button text", "name": "", "nameEn": ""}, {"source": "Radio", "token": "buttonSolidCheckedHoverBg", "type": "string", "desc": "单选框实色按钮选中时的悬浮态背景色", "descEn": "Background color of checked solid Radio button text when hover", "name": "", "nameEn": ""}, {"source": "Radio", "token": "dotColorDisabled", "type": "string", "desc": "单选框圆点禁用颜色", "descEn": "Color of disabled Radio dot", "name": "", "nameEn": ""}, {"source": "Radio", "token": "dotSize", "type": "number", "desc": "单选框圆点大小", "descEn": "Size of Radio dot", "name": "", "nameEn": ""}, {"source": "Radio", "token": "radioSize", "type": "number", "desc": "单选框大小", "descEn": "Radio size", "name": "", "nameEn": ""}, {"source": "Radio", "token": "wrapperMarginInlineEnd", "type": "number", "desc": "单选框右间距", "descEn": "Margin right of Radio button", "name": "", "nameEn": ""}], "Rate": [{"source": "Rate", "token": "starBg", "type": "string", "desc": "星星背景色", "descEn": "Star background color", "name": "", "nameEn": ""}, {"source": "Rate", "token": "starColor", "type": "string", "desc": "星星颜色", "descEn": "Star color", "name": "", "nameEn": ""}, {"source": "Rate", "token": "starHoverScale", "type": "undefined | readonly string[] | Transform | readonly Transform[] | { _multi_value_?: boolean; _skip_check_?: boolean; value: readonly string[] | Transform | (readonly string[] | Transform | undefined)[] | undefined }", "desc": "星星悬浮时的缩放", "descEn": "Scale of star when hover", "name": "", "nameEn": ""}, {"source": "Rate", "token": "starSize", "type": "number", "desc": "星星大小", "descEn": "Star size", "name": "", "nameEn": ""}], "Result": [{"source": "Result", "token": "extraMargin", "type": "undefined | Margin<string | number>", "desc": "额外区域外间距", "descEn": "Margin of extra area", "name": "", "nameEn": ""}, {"source": "Result", "token": "iconFontSize", "type": "number", "desc": "图标大小", "descEn": "Icon size", "name": "", "nameEn": ""}, {"source": "Result", "token": "subtitleFontSize", "type": "number", "desc": "副标题字体大小", "descEn": "Subtitle font size", "name": "", "nameEn": ""}, {"source": "Result", "token": "titleFontSize", "type": "number", "desc": "标题字体大小", "descEn": "Title font size", "name": "", "nameEn": ""}], "Segmented": [{"source": "Segmented", "token": "itemActiveBg", "type": "string", "desc": "选项激活态背景颜色", "descEn": "Background color of item when active", "name": "", "nameEn": ""}, {"source": "Segmented", "token": "itemColor", "type": "string", "desc": "选项文本颜色", "descEn": "Text color of item", "name": "", "nameEn": ""}, {"source": "Segmented", "token": "itemHoverBg", "type": "string", "desc": "选项悬浮态背景颜色", "descEn": "Background color of item when hover", "name": "", "nameEn": ""}, {"source": "Segmented", "token": "itemHoverColor", "type": "string", "desc": "选项悬浮态文本颜色", "descEn": "Text color of item when hover", "name": "", "nameEn": ""}, {"source": "Segmented", "token": "itemSelectedBg", "type": "string", "desc": "选项选中时背景颜色", "descEn": "Background color of item when selected", "name": "", "nameEn": ""}, {"source": "Segmented", "token": "itemSelectedColor", "type": "string", "desc": "选项选中时文字颜色", "descEn": "Text color of item when selected", "name": "", "nameEn": ""}, {"source": "Segmented", "token": "trackBg", "type": "string", "desc": "Segmented 控件容器背景色", "descEn": "Background of Segmented container", "name": "", "nameEn": ""}, {"source": "Segmented", "token": "trackPadding", "type": "string | number", "desc": "Segmented 控件容器的 padding", "descEn": "Padding of Segmented container", "name": "", "nameEn": ""}], "Select": [{"source": "Select", "token": "activeBorderColor", "type": "string", "desc": "激活态边框色", "descEn": "Active border color", "name": "", "nameEn": ""}, {"source": "Select", "token": "activeOutlineColor", "type": "string", "desc": "激活态 outline 颜色", "descEn": "Active outline color", "name": "", "nameEn": ""}, {"source": "Select", "token": "clearBg", "type": "string", "desc": "清空按钮背景色", "descEn": "Background color of clear button", "name": "", "nameEn": ""}, {"source": "Select", "token": "hoverBorderColor", "type": "string", "desc": "悬浮态边框色", "descEn": "Hover border color", "name": "", "nameEn": ""}, {"source": "Select", "token": "multipleItemBg", "type": "string", "desc": "多选标签背景色", "descEn": "Background color of multiple tag", "name": "", "nameEn": ""}, {"source": "Select", "token": "multipleItemBorderColor", "type": "string", "desc": "多选标签边框色", "descEn": "Border color of multiple tag", "name": "", "nameEn": ""}, {"source": "Select", "token": "multipleItemBorderColorDisabled", "type": "string", "desc": "多选标签禁用边框色", "descEn": "Border color of multiple tag when disabled", "name": "", "nameEn": ""}, {"source": "Select", "token": "multipleItemColorDisabled", "type": "string", "desc": "多选标签禁用文本颜色", "descEn": "Text color of multiple tag when disabled", "name": "", "nameEn": ""}, {"source": "Select", "token": "multipleItemHeight", "type": "number", "desc": "多选标签高度", "descEn": "Height of multiple tag", "name": "", "nameEn": ""}, {"source": "Select", "token": "multipleItemHeightLG", "type": "number", "desc": "大号多选标签高度", "descEn": "Height of multiple tag with large size", "name": "", "nameEn": ""}, {"source": "Select", "token": "multipleItemHeightSM", "type": "number", "desc": "小号多选标签高度", "descEn": "Height of multiple tag with small size", "name": "", "nameEn": ""}, {"source": "Select", "token": "multipleSelectorBgDisabled", "type": "string", "desc": "多选框禁用背景", "descEn": "Background color of multiple selector when disabled", "name": "", "nameEn": ""}, {"source": "Select", "token": "optionActiveBg", "type": "string", "desc": "选项激活态时背景色", "descEn": "Background color when option is active", "name": "", "nameEn": ""}, {"source": "Select", "token": "optionFontSize", "type": "number", "desc": "选项字体大小", "descEn": "Font size of option", "name": "", "nameEn": ""}, {"source": "Select", "token": "optionHeight", "type": "number", "desc": "选项高度", "descEn": "Height of option", "name": "", "nameEn": ""}, {"source": "Select", "token": "optionLineHeight", "type": "undefined | LineHeight<string | number>", "desc": "选项行高", "descEn": "Line height of option", "name": "", "nameEn": ""}, {"source": "Select", "token": "optionPadding", "type": "undefined | Padding<string | number>", "desc": "选项内间距", "descEn": "Padding of option", "name": "", "nameEn": ""}, {"source": "Select", "token": "optionSelectedBg", "type": "string", "desc": "选项选中时背景色", "descEn": "Background color when option is selected", "name": "", "nameEn": ""}, {"source": "Select", "token": "optionSelectedColor", "type": "string", "desc": "选项选中时文本颜色", "descEn": "Text color when option is selected", "name": "", "nameEn": ""}, {"source": "Select", "token": "optionSelectedFontWeight", "type": "undefined | FontWeight", "desc": "选项选中时文本字重", "descEn": "Font weight when option is selected", "name": "", "nameEn": ""}, {"source": "Select", "token": "selectorBg", "type": "string", "desc": "选框背景色", "descEn": "Background color of selector", "name": "", "nameEn": ""}, {"source": "Select", "token": "showArrowPaddingInlineEnd", "type": "number", "desc": "箭头的行末内边距", "descEn": "Inline end padding of arrow", "name": "", "nameEn": ""}, {"source": "Select", "token": "singleItemHeightLG", "type": "number", "desc": "单选大号回填项高度", "descEn": "Height of single selected item with large size", "name": "", "nameEn": ""}, {"source": "Select", "token": "zIndexPopup", "type": "number", "desc": "下拉菜单 z-index", "descEn": "z-index of dropdown", "name": "", "nameEn": ""}], "Skeleton": [{"source": "Skeleton", "token": "blockRadius", "type": "number", "desc": "骨架屏圆角", "descEn": "Border radius of skeleton", "name": "", "nameEn": ""}, {"source": "Skeleton", "token": "gradientFromColor", "type": "string", "desc": "渐变色起点颜色", "descEn": "Start color of gradient", "name": "", "nameEn": ""}, {"source": "Skeleton", "token": "gradientToColor", "type": "string", "desc": "渐变色终点颜色", "descEn": "End color of gradient", "name": "", "nameEn": ""}, {"source": "Skeleton", "token": "paragraphLiHeight", "type": "number", "desc": "段落骨架屏单行高度", "descEn": "Line height of paragraph skeleton", "name": "", "nameEn": ""}, {"source": "Skeleton", "token": "paragraphMarginTop", "type": "number", "desc": "段落骨架屏上间距", "descEn": "Margin top of paragraph skeleton", "name": "", "nameEn": ""}, {"source": "Skeleton", "token": "titleHeight", "type": "string | number", "desc": "标题骨架屏高度", "descEn": "Height of title skeleton", "name": "", "nameEn": ""}], "Slider": [{"source": "Slide<PERSON>", "token": "controlSize", "type": "number", "desc": "滑动输入高度", "descEn": "Height of slider", "name": "", "nameEn": ""}, {"source": "Slide<PERSON>", "token": "dotActiveBorderColor", "type": "string", "desc": "圆点激活态边框颜色", "descEn": "Border color of dot when active", "name": "", "nameEn": ""}, {"source": "Slide<PERSON>", "token": "dotBorderColor", "type": "string", "desc": "圆点边框颜色", "descEn": "Border color of dot", "name": "", "nameEn": ""}, {"source": "Slide<PERSON>", "token": "dotSize", "type": "number", "desc": "滑块圆点尺寸", "descEn": "Size of dot", "name": "", "nameEn": ""}, {"source": "Slide<PERSON>", "token": "handleActiveColor", "type": "string", "desc": "滑块激活态边框色", "descEn": "Border color of handle when active", "name": "", "nameEn": ""}, {"source": "Slide<PERSON>", "token": "handleActiveOutlineColor", "type": "string", "desc": "滑块激活态外框色", "descEn": "Outline color of handle when active", "name": "", "nameEn": ""}, {"source": "Slide<PERSON>", "token": "handleColor", "type": "string", "desc": "滑块颜色", "descEn": "Color of handle", "name": "", "nameEn": ""}, {"source": "Slide<PERSON>", "token": "handleColorDisabled", "type": "string", "desc": "滑块禁用颜色", "descEn": "Color of handle when disabled", "name": "", "nameEn": ""}, {"source": "Slide<PERSON>", "token": "handleLineWidth", "type": "string | number", "desc": "滑块边框宽度", "descEn": "Border width of handle", "name": "", "nameEn": ""}, {"source": "Slide<PERSON>", "token": "handleLineWidthHover", "type": "string | number", "desc": "滑块边框宽度（悬浮态）", "descEn": "Border width of handle when hover", "name": "", "nameEn": ""}, {"source": "Slide<PERSON>", "token": "handleSize", "type": "number", "desc": "滑块尺寸", "descEn": "Size of handle", "name": "", "nameEn": ""}, {"source": "Slide<PERSON>", "token": "handleSizeHover", "type": "number", "desc": "滑块尺寸（悬浮态）", "descEn": "Size of handle when hover", "name": "", "nameEn": ""}, {"source": "Slide<PERSON>", "token": "railBg", "type": "string", "desc": "轨道背景色", "descEn": "Background color of rail", "name": "", "nameEn": ""}, {"source": "Slide<PERSON>", "token": "railHoverBg", "type": "string", "desc": "轨道背景色（悬浮态）", "descEn": "Background color of rail when hover", "name": "", "nameEn": ""}, {"source": "Slide<PERSON>", "token": "railSize", "type": "number", "desc": "轨道高度", "descEn": "Height of rail", "name": "", "nameEn": ""}, {"source": "Slide<PERSON>", "token": "trackBg", "type": "string", "desc": "轨道已覆盖部分背景色", "descEn": "Background color of track", "name": "", "nameEn": ""}, {"source": "Slide<PERSON>", "token": "trackBgDisabled", "type": "string", "desc": "轨道禁用态背景色", "descEn": "Background color of track when disabled", "name": "", "nameEn": ""}, {"source": "Slide<PERSON>", "token": "trackHoverBg", "type": "string", "desc": "轨道已覆盖部分背景色（悬浮态）", "descEn": "Background color of track when hover", "name": "", "nameEn": ""}], "Space": [], "Spin": [{"source": "Spin", "token": "contentHeight", "type": "string | number", "desc": "内容区域高度", "descEn": "Height of content area", "name": "", "nameEn": ""}, {"source": "Spin", "token": "dotSize", "type": "number", "desc": "加载图标尺寸", "descEn": "Loading icon size", "name": "", "nameEn": ""}, {"source": "Spin", "token": "dotSizeLG", "type": "number", "desc": "大号加载图标尺寸", "descEn": "Large loading icon size", "name": "", "nameEn": ""}, {"source": "Spin", "token": "dotSizeSM", "type": "number", "desc": "小号加载图标尺寸", "descEn": "Small loading icon size", "name": "", "nameEn": ""}], "Splitter": [{"source": "Splitter", "token": "splitBarDraggableSize", "type": "number", "desc": "拖拽标识元素大小", "descEn": "Drag and drop the identity element size", "name": "", "nameEn": ""}, {"source": "Splitter", "token": "splitBarSize", "type": "number", "desc": "拖拽元素大小", "descEn": "Drag the element size", "name": "", "nameEn": ""}, {"source": "Splitter", "token": "splitTriggerSize", "type": "number", "desc": "拖拽触发区域大小", "descEn": "Drag and drop trigger area size", "name": "", "nameEn": ""}], "Statistic": [{"source": "Statistic", "token": "contentFontSize", "type": "number", "desc": "内容字体大小", "descEn": "Content font size", "name": "", "nameEn": ""}, {"source": "Statistic", "token": "titleFontSize", "type": "number", "desc": "标题字体大小", "descEn": "Title font size", "name": "", "nameEn": ""}], "Steps": [{"source": "Steps", "token": "customIconFontSize", "type": "number", "desc": "自定义图标大小", "descEn": "Font size of custom icon", "name": "", "nameEn": ""}, {"source": "Steps", "token": "customIconSize", "type": "number", "desc": "自定义图标容器尺寸", "descEn": "Size of custom icon container", "name": "", "nameEn": ""}, {"source": "Steps", "token": "customIconTop", "type": "number", "desc": "自定义图标 top", "descEn": "Top of custom icon", "name": "", "nameEn": ""}, {"source": "Steps", "token": "descriptionMaxWidth", "type": "number", "desc": "描述区域最大宽度", "descEn": "Max width of description area", "name": "", "nameEn": ""}, {"source": "Steps", "token": "dotCurrentSize", "type": "number", "desc": "点状步骤点当前大小", "descEn": "Current size of dot", "name": "", "nameEn": ""}, {"source": "Steps", "token": "dotSize", "type": "number", "desc": "点状步骤点大小", "descEn": "Size of dot", "name": "", "nameEn": ""}, {"source": "Steps", "token": "iconFontSize", "type": "number", "desc": "图标大小", "descEn": "Size of icon", "name": "", "nameEn": ""}, {"source": "Steps", "token": "iconSize", "type": "number", "desc": "图标容器尺寸", "descEn": "Size of icon container", "name": "", "nameEn": ""}, {"source": "Steps", "token": "iconSizeSM", "type": "number", "desc": "小号步骤条图标大小", "descEn": "Size of small steps icon", "name": "", "nameEn": ""}, {"source": "Steps", "token": "iconTop", "type": "number", "desc": "图标 top", "descEn": "Top of icon", "name": "", "nameEn": ""}, {"source": "Steps", "token": "navArrowColor", "type": "string", "desc": "可跳转步骤条箭头颜色", "descEn": "Color of arrow in nav", "name": "", "nameEn": ""}, {"source": "Steps", "token": "navContentMaxWidth", "type": "undefined | MaxWidth<string | number>", "desc": "可跳转步骤条内容最大宽度", "descEn": "Max width of nav content", "name": "", "nameEn": ""}, {"source": "Steps", "token": "titleLineHeight", "type": "string | number", "desc": "标题行高", "descEn": "Line height of title", "name": "", "nameEn": ""}], "Switch": [{"source": "Switch", "token": "handleBg", "type": "string", "desc": "开关把手背景色", "descEn": "Background color of Switch handle", "name": "", "nameEn": ""}, {"source": "Switch", "token": "handleShadow", "type": "string", "desc": "开关把手阴影", "descEn": "Shadow of Switch handle", "name": "", "nameEn": ""}, {"source": "Switch", "token": "handleSize", "type": "number", "desc": "开关把手大小", "descEn": "Size of Switch handle", "name": "", "nameEn": ""}, {"source": "Switch", "token": "handleSizeSM", "type": "number", "desc": "小号开关把手大小", "descEn": "Size of small Switch handle", "name": "", "nameEn": ""}, {"source": "Switch", "token": "innerMaxMargin", "type": "number", "desc": "内容区域最大边距", "descEn": "Maximum margin of content area", "name": "", "nameEn": ""}, {"source": "Switch", "token": "innerMaxMarginSM", "type": "number", "desc": "小号开关内容区域最大边距", "descEn": "Maximum margin of content area of small Switch", "name": "", "nameEn": ""}, {"source": "Switch", "token": "inner<PERSON>in<PERSON>argin", "type": "number", "desc": "内容区域最小边距", "descEn": "Minimum margin of content area", "name": "", "nameEn": ""}, {"source": "Switch", "token": "innerMinMarginSM", "type": "number", "desc": "小号开关内容区域最小边距", "descEn": "Minimum margin of content area of small Switch", "name": "", "nameEn": ""}, {"source": "Switch", "token": "trackHeight", "type": "string | number", "desc": "开关高度", "descEn": "Height of Switch", "name": "", "nameEn": ""}, {"source": "Switch", "token": "trackHeightSM", "type": "string | number", "desc": "小号开关高度", "descEn": "Height of small Switch", "name": "", "nameEn": ""}, {"source": "Switch", "token": "trackMinWidth", "type": "string | number", "desc": "开关最小宽度", "descEn": "Minimum width of Switch", "name": "", "nameEn": ""}, {"source": "Switch", "token": "trackMinWidthSM", "type": "string | number", "desc": "小号开关最小宽度", "descEn": "Minimum width of small Switch", "name": "", "nameEn": ""}, {"source": "Switch", "token": "trackPadding", "type": "number", "desc": "开关内边距", "descEn": "Padding of Switch", "name": "", "nameEn": ""}], "Table": [{"source": "Table", "token": "bodySortBg", "type": "string", "desc": "表格排序列背景色", "descEn": "Background color of table sorted column", "name": "", "nameEn": ""}, {"source": "Table", "token": "borderColor", "type": "string", "desc": "表格边框/分割线颜色", "descEn": "Border color of table", "name": "", "nameEn": ""}, {"source": "Table", "token": "cellFontSize", "type": "number", "desc": "单元格文字大小（默认大尺寸）", "descEn": "Font size of table cell (large size by default)", "name": "", "nameEn": ""}, {"source": "Table", "token": "cellFontSizeMD", "type": "number", "desc": "单元格文字大小（中等尺寸）", "descEn": "Font size of table cell (middle size)", "name": "", "nameEn": ""}, {"source": "Table", "token": "cellFontSizeSM", "type": "number", "desc": "单元格文字大小（小尺寸）", "descEn": "Font size of table cell (small size)", "name": "", "nameEn": ""}, {"source": "Table", "token": "cellPaddingBlock", "type": "number", "desc": "单元格纵向内间距", "descEn": "Vertical padding of table cell", "name": "", "nameEn": ""}, {"source": "Table", "token": "cellPaddingBlockMD", "type": "number", "desc": "单元格纵向内间距（中等尺寸）", "descEn": "Vertical padding of table cell (middle size)", "name": "", "nameEn": ""}, {"source": "Table", "token": "cellPaddingBlockSM", "type": "number", "desc": "单元格纵向内间距（小尺寸）", "descEn": "Vertical padding of table cell (small size)", "name": "", "nameEn": ""}, {"source": "Table", "token": "cellPaddingInline", "type": "number", "desc": "单元格横向内间距（默认大尺寸）", "descEn": "Horizontal padding of table cell (large size by default)", "name": "", "nameEn": ""}, {"source": "Table", "token": "cellPaddingInlineMD", "type": "number", "desc": "单元格横向内间距（中等尺寸）", "descEn": "Horizontal padding of table cell (middle size)", "name": "", "nameEn": ""}, {"source": "Table", "token": "cellPaddingInlineSM", "type": "number", "desc": "单元格横向内间距（小尺寸）", "descEn": "Horizontal padding of table cell (small size)", "name": "", "nameEn": ""}, {"source": "Table", "token": "expandIconBg", "type": "string", "desc": "展开按钮背景色", "descEn": "Background of expand button", "name": "", "nameEn": ""}, {"source": "Table", "token": "filterDropdownBg", "type": "string", "desc": "过滤下拉菜单颜色", "descEn": "Color of filter dropdown", "name": "", "nameEn": ""}, {"source": "Table", "token": "filterDropdownMenuBg", "type": "string", "desc": "过滤下拉菜单选项背景", "descEn": "Background of filter dropdown menu item", "name": "", "nameEn": ""}, {"source": "Table", "token": "fixedHeaderSortActiveBg", "type": "string", "desc": "固定表头排序激活态背景色", "descEn": "Background color of fixed table header when sorted", "name": "", "nameEn": ""}, {"source": "Table", "token": "footerBg", "type": "string", "desc": "表格底部背景色", "descEn": "Background of footer", "name": "", "nameEn": ""}, {"source": "Table", "token": "footerColor", "type": "string", "desc": "表格底部文字颜色", "descEn": "Color of footer text", "name": "", "nameEn": ""}, {"source": "Table", "token": "headerBg", "type": "string", "desc": "表头背景", "descEn": "Background of table header", "name": "", "nameEn": ""}, {"source": "Table", "token": "headerBorderRadius", "type": "number", "desc": "表头圆角", "descEn": "Border radius of table header", "name": "", "nameEn": ""}, {"source": "Table", "token": "headerColor", "type": "string", "desc": "表头文字颜色", "descEn": "Color of table header text", "name": "", "nameEn": ""}, {"source": "Table", "token": "headerFilterHoverBg", "type": "string", "desc": "表头过滤按钮悬浮背景色", "descEn": "Background color of table header filter button when hovered", "name": "", "nameEn": ""}, {"source": "Table", "token": "headerSortActiveBg", "type": "string", "desc": "表头排序激活态背景色", "descEn": "Background color of table header when sorted", "name": "", "nameEn": ""}, {"source": "Table", "token": "headerSortHoverBg", "type": "string", "desc": "表头排序激活态悬浮背景色", "descEn": "Background color of table header when sorted and hovered", "name": "", "nameEn": ""}, {"source": "Table", "token": "headerSplitColor", "type": "string", "desc": "表头分割线颜色", "descEn": "Split border color of table header", "name": "", "nameEn": ""}, {"source": "Table", "token": "rowExpandedBg", "type": "string", "desc": "表格行展开背景色", "descEn": "Background color of table expanded row", "name": "", "nameEn": ""}, {"source": "Table", "token": "rowHoverBg", "type": "string", "desc": "表格行悬浮背景色", "descEn": "Background color of table hovered row", "name": "", "nameEn": ""}, {"source": "Table", "token": "rowSelectedBg", "type": "string", "desc": "表格行选中背景色", "descEn": "Background color of table selected row", "name": "", "nameEn": ""}, {"source": "Table", "token": "rowSelectedHoverBg", "type": "string", "desc": "表格行选中悬浮背景色", "descEn": "Background color of table selected row when hovered", "name": "", "nameEn": ""}, {"source": "Table", "token": "selectionColumn<PERSON><PERSON><PERSON>", "type": "string | number", "desc": "选择列宽度", "descEn": "Width of selection column", "name": "", "nameEn": ""}, {"source": "Table", "token": "stickyScrollBarBg", "type": "string", "desc": "Sticky 模式下滚动条背景色", "descEn": "Background of sticky scrollbar", "name": "", "nameEn": ""}, {"source": "Table", "token": "stickyScrollBarBorderRadius", "type": "number", "desc": "Sticky 模式下滚动条圆角", "descEn": "Border radius of sticky scrollbar", "name": "", "nameEn": ""}], "Tabs": [{"source": "Tabs", "token": "cardBg", "type": "string", "desc": "卡片标签页背景色", "descEn": "Background color of card tab", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "cardGutter", "type": "number", "desc": "卡片标签间距", "descEn": "Gutter of card tab", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "cardHeight", "type": "number", "desc": "卡片标签页高度", "descEn": "Height of card tab", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "cardHeightLG", "type": "number", "desc": "大尺寸卡片标签页高度", "descEn": "Height of large card tab", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "cardHeightSM", "type": "number", "desc": "小尺寸卡片标签页高度", "descEn": "Height of small card tab", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "cardPadding", "type": "string", "desc": "卡片标签页内间距", "descEn": "Padding of card tab", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "cardPaddingLG", "type": "string", "desc": "大号卡片标签页内间距", "descEn": "Padding of large card tab", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "cardPaddingSM", "type": "string", "desc": "小号卡片标签页内间距", "descEn": "Padding of small card tab", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "horizontalItemGutter", "type": "number", "desc": "横向标签页标签间距", "descEn": "Horizontal gutter of horizontal tab", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "horizontalItemMargin", "type": "string", "desc": "横向标签页标签外间距", "descEn": "Horizontal margin of horizontal tab item", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "horizontalItemMarginRTL", "type": "string", "desc": "横向标签页标签外间距（RTL）", "descEn": "Horizontal margin of horizontal tab item (RTL)", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "horizontalItemPadding", "type": "string", "desc": "横向标签页标签内间距", "descEn": "Horizontal padding of horizontal tab item", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "horizontalItemPaddingLG", "type": "string", "desc": "大号横向标签页标签内间距", "descEn": "Horizontal padding of large horizontal tab item", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "horizontalItemPaddingSM", "type": "string", "desc": "小号横向标签页标签内间距", "descEn": "Horizontal padding of small horizontal tab item", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "desc": "横向标签页外间距", "descEn": "Horizontal margin of horizontal tab", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "inkBarColor", "type": "string", "desc": "指示条颜色", "descEn": "Color of indicator", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "itemActiveColor", "type": "string", "desc": "标签激活态文本颜色", "descEn": "Text color of active tab", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "itemColor", "type": "string", "desc": "标签文本颜色", "descEn": "Text color of tab", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "itemHoverColor", "type": "string", "desc": "标签悬浮态文本颜色", "descEn": "Text color of hover tab", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "itemSelectedColor", "type": "string", "desc": "标签选中态文本颜色", "descEn": "Text color of selected tab", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "titleFontSize", "type": "number", "desc": "标签页标题文本大小", "descEn": "Font size of title", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "titleFontSizeLG", "type": "number", "desc": "大号标签页标题文本大小", "descEn": "Font size of large title", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "titleFontSizeSM", "type": "number", "desc": "小号标签页标题文本大小", "descEn": "Font size of small title", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "verticalItemMargin", "type": "string", "desc": "纵向标签页标签外间距", "descEn": "Vertical margin of vertical tab item", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "verticalItemPadding", "type": "string", "desc": "纵向标签页标签内间距", "descEn": "Vertical padding of vertical tab item", "name": "", "nameEn": ""}, {"source": "Tabs", "token": "zIndexPopup", "type": "number", "desc": "下拉菜单 z-index", "descEn": "z-index of dropdown menu", "name": "", "nameEn": ""}], "Tag": [{"source": "Tag", "token": "defaultBg", "type": "string", "desc": "默认背景色", "descEn": "Default background color", "name": "", "nameEn": ""}, {"source": "Tag", "token": "defaultColor", "type": "string", "desc": "默认文字颜色", "descEn": "Default text color", "name": "", "nameEn": ""}], "Timeline": [{"source": "Timeline", "token": "dotBg", "type": "string", "desc": "节点背景色", "descEn": "Background color of node", "name": "", "nameEn": ""}, {"source": "Timeline", "token": "dotBorderWidth", "type": "string | number", "desc": "节点边框宽度", "descEn": "Border width of node", "name": "", "nameEn": ""}, {"source": "Timeline", "token": "itemPaddingBottom", "type": "number", "desc": "时间项下间距", "descEn": "Bottom padding of item", "name": "", "nameEn": ""}, {"source": "Timeline", "token": "tailColor", "type": "string", "desc": "轨迹颜色", "descEn": "Line color", "name": "", "nameEn": ""}, {"source": "Timeline", "token": "tailWidth", "type": "string | number", "desc": "轨迹宽度", "descEn": "Line width", "name": "", "nameEn": ""}], "Tooltip": [{"source": "<PERSON><PERSON><PERSON>", "token": "zIndexPopup", "type": "number", "desc": "文字提示 z-index", "descEn": "z-index of tooltip", "name": "", "nameEn": ""}], "Tour": [{"source": "Tour", "token": "closeBtnSize", "type": "number", "desc": "关闭按钮尺寸", "descEn": "Close button size", "name": "", "nameEn": ""}, {"source": "Tour", "token": "primaryNextBtnHoverBg", "type": "string", "desc": "Primary 模式下一步按钮悬浮背景色", "descEn": "Hover background color of next button in primary type", "name": "", "nameEn": ""}, {"source": "Tour", "token": "primaryPrevBtnBg", "type": "string", "desc": "Primary 模式上一步按钮背景色", "descEn": "Background color of previous button in primary type", "name": "", "nameEn": ""}, {"source": "Tour", "token": "zIndexPopup", "type": "number", "desc": "弹层 z-index", "descEn": "Tour popup z-index", "name": "", "nameEn": ""}], "Transfer": [{"source": "Transfer", "token": "headerHeight", "type": "string | number", "desc": "顶部高度", "descEn": "Height of header", "name": "", "nameEn": ""}, {"source": "Transfer", "token": "itemHeight", "type": "string | number", "desc": "列表项高度", "descEn": "Height of list item", "name": "", "nameEn": ""}, {"source": "Transfer", "token": "itemPaddingBlock", "type": "string | number", "desc": "列表项纵向内边距", "descEn": "Vertical padding of list item", "name": "", "nameEn": ""}, {"source": "Transfer", "token": "listHeight", "type": "string | number", "desc": "列表高度", "descEn": "Height of list", "name": "", "nameEn": ""}, {"source": "Transfer", "token": "listWidth", "type": "string | number", "desc": "列表宽度", "descEn": "Width of list", "name": "", "nameEn": ""}, {"source": "Transfer", "token": "listWidthLG", "type": "string | number", "desc": "大号列表宽度", "descEn": "Width of large list", "name": "", "nameEn": ""}], "TreeSelect": [{"source": "TreeSelect", "token": "indentSize", "type": "number", "desc": "缩进宽度", "descEn": "Indent width of tree", "name": "", "nameEn": ""}, {"source": "TreeSelect", "token": "nodeHoverBg", "type": "string", "desc": "节点悬浮态背景色", "descEn": "Background color of hovered node", "name": "", "nameEn": ""}, {"source": "TreeSelect", "token": "nodeHoverColor", "type": "string", "desc": "节点悬浮态态文字颜色", "descEn": "Text color of hovered node", "name": "", "nameEn": ""}, {"source": "TreeSelect", "token": "nodeSelectedBg", "type": "string", "desc": "节点选中态背景色", "descEn": "Background color of selected node", "name": "", "nameEn": ""}, {"source": "TreeSelect", "token": "nodeSelectedColor", "type": "string", "desc": "节点选中态文字颜色", "descEn": "Text color of selected node", "name": "", "nameEn": ""}, {"source": "TreeSelect", "token": "titleHeight", "type": "number", "desc": "节点标题高度", "descEn": "Node title height", "name": "", "nameEn": ""}], "Tree": [{"source": "Tree", "token": "directoryNodeSelectedBg", "type": "string", "desc": "目录树节点选中背景色", "descEn": "Background color of selected directory node", "name": "", "nameEn": ""}, {"source": "Tree", "token": "directoryNodeSelectedColor", "type": "string", "desc": "目录树节点选中文字颜色", "descEn": "Text color of selected directory node", "name": "", "nameEn": ""}, {"source": "Tree", "token": "indentSize", "type": "number", "desc": "缩进宽度", "descEn": "Indent width of tree", "name": "", "nameEn": ""}, {"source": "Tree", "token": "nodeHoverBg", "type": "string", "desc": "节点悬浮态背景色", "descEn": "Background color of hovered node", "name": "", "nameEn": ""}, {"source": "Tree", "token": "nodeHoverColor", "type": "string", "desc": "节点悬浮态态文字颜色", "descEn": "Text color of hovered node", "name": "", "nameEn": ""}, {"source": "Tree", "token": "nodeSelectedBg", "type": "string", "desc": "节点选中态背景色", "descEn": "Background color of selected node", "name": "", "nameEn": ""}, {"source": "Tree", "token": "nodeSelectedColor", "type": "string", "desc": "节点选中态文字颜色", "descEn": "Text color of selected node", "name": "", "nameEn": ""}, {"source": "Tree", "token": "titleHeight", "type": "number", "desc": "节点标题高度", "descEn": "Node title height", "name": "", "nameEn": ""}], "Typography": [{"source": "Typography", "token": "titleMarginBottom", "type": "string | number", "desc": "标题下间距", "descEn": "<PERSON><PERSON> bottom of title", "name": "", "nameEn": ""}, {"source": "Typography", "token": "titleMarginTop", "type": "string | number", "desc": "标题上间距", "descEn": "<PERSON><PERSON> top of title", "name": "", "nameEn": ""}], "Upload": [{"source": "Upload", "token": "actionsColor", "type": "string", "desc": "操作按扭颜色", "descEn": "Action button color", "name": "", "nameEn": ""}]}}