# victory-selection-container

## 36.9.2

### Patch Changes

- Replace lodash keys with native code ([#2811](https://github.com/FormidableLabs/victory/pull/2811))

* Replace lodash array utils with native code ([#2810](https://github.com/FormidableLabs/victory/pull/2810))

- Replace lodash isFunction with native code ([#2802](https://github.com/FormidableLabs/victory/pull/2802))

## 36.9.1

### Patch Changes

- Fix victory-native component prop types ([#2785](https://github.com/FormidableLabs/victory/pull/2785))

## 36.9.0

### Minor Changes

- Remove prop-types definitions and dependency ([#2758](https://github.com/FormidableLabs/victory/pull/2758))

## 36.8.6

## 36.8.5

### Patch Changes

- Replace instances of lodash.assign with Object.assign ([#2757](https://github.com/FormidableLabs/victory/pull/2757))

## 36.8.4

## 36.8.3

### Patch Changes

- Refactor param reassignments ([#2724](https://github.com/FormidableLabs/victory/pull/2724))

## 36.8.2

### Patch Changes

- Migrated victory-selection-container to TypeScript ([#2706](https://github.com/FormidableLabs/victory/pull/2706))

## 36.8.1

## 36.8.0

## 36.7.0

## 36.6.12

## 36.6.11

## 36.6.10

### Patch Changes

- Setup NPM Provenance ([#2590](https://github.com/FormidableLabs/victory/pull/2590))

## 36.6.9

### Patch Changes

- Setup NPM Provenance ([#2587](https://github.com/FormidableLabs/victory/pull/2587))

## 36.6.8

### Patch Changes

- Updated dependencies []:
  - victory-core@36.6.8

## 36.6.7

### Patch Changes

- Updated dependencies []:
  - victory-core@36.6.7

## 36.6.6

### Patch Changes

- Improved the exported types (fixes [#2451](https://github.com/FormidableLabs/victory/issues/2451)) ([#2452](https://github.com/FormidableLabs/victory/pull/2452))

- Updated dependencies []:
  - victory-core@36.6.6

## 36.6.5

### Patch Changes

- Updated dependencies [[`6f4972123`](https://github.com/FormidableLabs/victory/commit/6f49721238332bb5ee879571a45b34a04e44d416)]:
  - victory-core@36.6.5

## 36.6.4

### Patch Changes

- Added explicit `any` type defs (fixes [#2358](https://github.com/FormidableLabs/victory/issues/2358)) ([`57ed0fe30`](https://github.com/FormidableLabs/victory/commit/57ed0fe304dbc8753da1126a02d44de8004e96aa))

* Allow data accessors to accept any data types (fixes [#2360](https://github.com/FormidableLabs/victory/issues/2360)) ([#2436](https://github.com/FormidableLabs/victory/pull/2436))

* Updated dependencies [[`9a6319cff`](https://github.com/FormidableLabs/victory/commit/9a6319cffbc480711b8c286dcae00575081170f0)]:
  - victory-core@36.6.4

## 36.6.3

### Patch Changes

- Do not generate \*.js.map sourcemaps (fixes [#2346](https://github.com/FormidableLabs/victory/issues/2346)) ([#2432](https://github.com/FormidableLabs/victory/pull/2432))

- Updated dependencies [[`4bfc65df5`](https://github.com/FormidableLabs/victory/commit/4bfc65df5a10aa6a10084882ed5c6d0d894dec6f)]:
  - victory-core@36.6.3

## 36.6.2

### Patch Changes

- Updated dependencies []:
  - victory-core@36.6.2

## 36.6.1

### Patch Changes

- Updated dependencies [[`d1f281104`](https://github.com/FormidableLabs/victory/commit/d1f281104c7598c43e220dafd57546ab03daeeb5)]:
  - victory-core@36.6.1

## 36.6.0

### Patch Changes

- Update source code with minor lint-based improvements (see [#2236](https://github.com/FormidableLabs/victory/issues/2236)). ([#2403](https://github.com/FormidableLabs/victory/pull/2403))

- Updated dependencies [[`fed5a5072`](https://github.com/FormidableLabs/victory/commit/fed5a507299b337846eed3d873ec7eb91bc69668), [`a2f48555a`](https://github.com/FormidableLabs/victory/commit/a2f48555adfed15bdb004dc0793f197d90c950a2)]:
  - victory-core@36.6.0

## 36.5.3 and earlier

Change history for version 36.5.3 and earlier can be found in our root [CHANGELOG.md](https://github.com/FormidableLabs/victory/blob/main/CHANGELOG.md).
