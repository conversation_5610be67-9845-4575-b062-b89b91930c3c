"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});

var _victorySharedEvents = require("./victory-shared-events");

Object.keys(_victorySharedEvents).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _victorySharedEvents[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _victorySharedEvents[key];
    }
  });
});