!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("react")):"function"==typeof define&&define.amd?define(["react"],e):"object"==typeof exports?exports.VictoryTooltip=e(require("react")):t.VictoryTooltip=e(t.React)}(self,(t=>(()=>{var e={503:(t,e,r)=>{var n=r(1171),o=r(7838),i=r(4859),a=r(4073),c=r(8541);function l(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}l.prototype.clear=n,l.prototype.delete=o,l.prototype.get=i,l.prototype.has=a,l.prototype.set=c,t.exports=l},3273:(t,e,r)=>{var n=r(163);t.exports=function(){if(!arguments.length)return[];var t=arguments[0];return n(t)?t:[t]}},8708:(t,e,r)=>{var n=r(1171),o=r(7838),i=r(4859),a=r(4073),c=r(8541);function l(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}l.prototype.clear=n,l.prototype.delete=o,l.prototype.get=i,l.prototype.has=a,l.prototype.set=c,t.exports=l},2596:(t,e,r)=>{var n=r(2373).Symbol;t.exports=n},6082:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},8644:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},9559:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},1634:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}},4132:(t,e,r)=>{var n=r(8347),o=r(788),i=Object.prototype.hasOwnProperty;t.exports=function(t,e,r){var a=t[e];i.call(t,e)&&o(a,r)&&(void 0!==r||e in t)||n(t,e,r)}},3162:(t,e,r)=>{var n=r(788);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return-1}},8347:(t,e,r)=>{var n=r(5525);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},6457:(t,e,r)=>{var n=r(9559),o=r(3608);t.exports=function t(e,r,i,a,c){var l=-1,u=e.length;for(i||(i=o),c||(c=[]);++l<u;){var s=e[l];r>0&&i(s)?r>1?t(s,r-1,i,a,c):n(c,s):a||(c[c.length]=s)}return c}},4432:(t,e,r)=>{var n=r(9026),o=r(3110);t.exports=function(t,e){for(var r=0,i=(e=n(e,t)).length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},563:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},6776:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},6123:t=>{t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return-1}},9070:(t,e,r)=>{var n=r(7028),o=r(3474);t.exports=function t(e,r,i,a,c){return e===r||(null==e||null==r||!o(e)&&!o(r)?e!=e&&r!=r:n(e,r,i,a,t,c))}},7028:(t,e,r)=>{var n=r(8708),o=r(8705),i=r(2813),a=r(6682),c=r(9667),l=r(163),u=r(4801),s=r(4289),f="[object Arguments]",p="[object Array]",d="[object Object]",y=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,g,h,v){var b=l(t),O=l(e),m=b?p:c(t),x=O?p:c(e),j=(m=m==f?d:m)==d,w=(x=x==f?d:x)==d,P=m==x;if(P&&u(t)){if(!u(e))return!1;b=!0,j=!1}if(P&&!j)return v||(v=new n),b||s(t)?o(t,e,r,g,h,v):i(t,e,m,r,g,h,v);if(!(1&r)){var S=j&&y.call(t,"__wrapped__"),k=w&&y.call(e,"__wrapped__");if(S||k){var A=S?t.value():t,E=k?e.value():e;return v||(v=new n),h(A,E,r,g,v)}}return!!P&&(v||(v=new n),a(t,e,r,g,h,v))}},9548:(t,e,r)=>{var n=r(8708),o=r(9070);t.exports=function(t,e,r,i){var a=r.length,c=a,l=!i;if(null==t)return!c;for(t=Object(t);a--;){var u=r[a];if(l&&u[2]?u[1]!==t[u[0]]:!(u[0]in t))return!1}for(;++a<c;){var s=(u=r[a])[0],f=t[s],p=u[1];if(l&&u[2]){if(void 0===f&&!(s in t))return!1}else{var d=new n;if(i)var y=i(f,p,s,t,e,d);if(!(void 0===y?o(p,f,3,i,d):y))return!1}}return!0}},3472:(t,e,r)=>{var n=r(758),o=r(5223),i=r(5346),a=r(163),c=r(8532);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):c(t)}},7190:(t,e,r)=>{var n=r(5125)(Object.keys,Object);t.exports=n},383:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},758:(t,e,r)=>{var n=r(9548),o=r(597),i=r(6878);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},5223:(t,e,r)=>{var n=r(9070),o=r(9201),i=r(5066),a=r(726),c=r(5385),l=r(6878),u=r(3110);t.exports=function(t,e){return a(t)&&c(e)?l(u(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},8245:(t,e,r)=>{var n=r(8644),o=r(4432),i=r(3472),a=r(383),c=r(9521),l=r(9110),u=r(9409),s=r(5346),f=r(163);t.exports=function(t,e,r){e=e.length?n(e,(function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t})):[s];var p=-1;e=n(e,l(i));var d=a(t,(function(t,r,o){return{criteria:n(e,(function(e){return e(t)})),index:++p,value:t}}));return c(d,(function(t,e){return u(t,e,r)}))}},5436:(t,e,r)=>{var n=r(6371),o=r(5066);t.exports=function(t,e){return n(t,e,(function(e,r){return o(t,r)}))}},6371:(t,e,r)=>{var n=r(4432),o=r(6539),i=r(9026);t.exports=function(t,e,r){for(var a=-1,c=e.length,l={};++a<c;){var u=e[a],s=n(t,u);r(s,u)&&o(l,i(u,t),s)}return l}},1600:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},3301:(t,e,r)=>{var n=r(4432);t.exports=function(t){return function(e){return n(e,t)}}},6317:(t,e,r)=>{var n=r(5346),o=r(4280),i=r(201);t.exports=function(t,e){return i(o(t,e,n),t+"")}},6539:(t,e,r)=>{var n=r(4132),o=r(9026),i=r(9099),a=r(7709),c=r(3110);t.exports=function(t,e,r,l){if(!a(t))return t;for(var u=-1,s=(e=o(e,t)).length,f=s-1,p=t;null!=p&&++u<s;){var d=c(e[u]),y=r;if("__proto__"===d||"constructor"===d||"prototype"===d)return t;if(u!=f){var g=p[d];void 0===(y=l?l(g,d,p):void 0)&&(y=a(g)?g:i(e[u+1])?[]:{})}n(p,d,y),p=p[d]}return t}},9521:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},6316:(t,e,r)=>{var n=r(2596),o=r(8644),i=r(163),a=r(1995),c=n?n.prototype:void 0,l=c?c.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return l?l.call(e):"";var r=e+"";return"0"==r&&1/e==-1/0?"-0":r}},9110:t=>{t.exports=function(t){return function(e){return t(e)}}},9914:(t,e,r)=>{var n=r(6123);t.exports=function(t,e){return!(null==t||!t.length)&&n(t,e,0)>-1}},9026:(t,e,r)=>{var n=r(163),o=r(726),i=r(7801),a=r(7010);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},522:(t,e,r)=>{var n=r(1995);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t==t,a=n(t),c=void 0!==e,l=null===e,u=e==e,s=n(e);if(!l&&!s&&!a&&t>e||a&&c&&u&&!l&&!s||o&&c&&u||!r&&u||!i)return 1;if(!o&&!a&&!s&&t<e||s&&r&&i&&!o&&!a||l&&r&&i||!c&&i||!u)return-1}return 0}},9409:(t,e,r)=>{var n=r(522);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,c=i.length,l=r.length;++o<c;){var u=n(i[o],a[o]);if(u)return o>=l?u:u*("desc"==r[o]?-1:1)}return t.index-e.index}},5525:(t,e,r)=>{var n=r(3743),o=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();t.exports=o},8705:(t,e,r)=>{var n=r(3273),o=r(1634),i=r(9914);t.exports=function(t,e,r,a,c,l){var u=1&r,s=t.length,f=e.length;if(s!=f&&!(u&&f>s))return!1;var p=l.get(t),d=l.get(e);if(p&&d)return p==e&&d==t;var y=-1,g=!0,h=2&r?new n:void 0;for(l.set(t,e),l.set(e,t);++y<s;){var v=t[y],b=e[y];if(a)var O=u?a(b,v,y,e,t,l):a(v,b,y,t,e,l);if(void 0!==O){if(O)continue;g=!1;break}if(h){if(!o(e,(function(t,e){if(!i(h,e)&&(v===t||c(v,t,r,a,l)))return h.push(e)}))){g=!1;break}}else if(v!==b&&!c(v,b,r,a,l)){g=!1;break}}return l.delete(t),l.delete(e),g}},2813:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},6682:(t,e,r)=>{var n=r(8143),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,c){var l=1&r,u=n(t),s=u.length;if(s!=n(e).length&&!l)return!1;for(var f=s;f--;){var p=u[f];if(!(l?p in e:o.call(e,p)))return!1}var d=c.get(t),y=c.get(e);if(d&&y)return d==e&&y==t;var g=!0;c.set(t,e),c.set(e,t);for(var h=l;++f<s;){var v=t[p=u[f]],b=e[p];if(i)var O=l?i(b,v,p,e,t,c):i(v,b,p,t,e,c);if(!(void 0===O?v===b||a(v,b,r,i,c):O)){g=!1;break}h||(h="constructor"==p)}if(g&&!h){var m=t.constructor,x=e.constructor;m==x||!("constructor"in t)||!("constructor"in e)||"function"==typeof m&&m instanceof m&&"function"==typeof x&&x instanceof x||(g=!1)}return c.delete(t),c.delete(e),g}},5557:(t,e,r)=>{var n=r(2645),o=r(4280),i=r(201);t.exports=function(t){return i(o(t,void 0,n),t+"")}},2117:(t,e,r)=>{var n="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;t.exports=n},8143:(t,e,r)=>{var n=r(5125)(Object.keys,Object);t.exports=n},597:(t,e,r)=>{var n=r(5385),o=r(7747);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},3743:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},9817:(t,e,r)=>{var n=r(5125)(Object.getPrototypeOf,Object);t.exports=n},9667:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},3096:(t,e,r)=>{var n=r(9026),o=r(5075),i=r(163),a=r(9099),c=r(8454),l=r(3110);t.exports=function(t,e,r){for(var u=-1,s=(e=n(e,t)).length,f=!1;++u<s;){var p=l(e[u]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++u!=s?f:!!(s=null==t?0:t.length)&&c(s)&&a(p,s)&&(i(t)||o(t))}},3608:(t,e,r)=>{var n=r(2596),o=r(5075),i=r(163),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},9099:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},8286:t=>{t.exports=function(){return!1}},726:(t,e,r)=>{var n=r(163),o=r(1995),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=t&&!o(t))||a.test(t)||!i.test(t)||null!=e&&t in Object(e)}},5782:t=>{t.exports=function(){return!1}},5385:(t,e,r)=>{var n=r(7709);t.exports=function(t){return t==t&&!n(t)}},1171:t=>{t.exports=function(){this.__data__=[],this.size=0}},7838:(t,e,r)=>{var n=r(3162),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0||(r==e.length-1?e.pop():o.call(e,r,1),--this.size,0))}},4859:(t,e,r)=>{var n=r(3162);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},4073:(t,e,r)=>{var n=r(3162);t.exports=function(t){return n(this.__data__,t)>-1}},8541:(t,e,r)=>{var n=r(3162);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},6878:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},2453:t=>{t.exports=function(t){return t}},5125:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},4280:(t,e,r)=>{var n=r(6082),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,c=o(i.length-e,0),l=Array(c);++a<c;)l[a]=i[e+a];a=-1;for(var u=Array(e+1);++a<e;)u[a]=i[a];return u[e]=r(l),n(t,this,u)}}},2373:(t,e,r)=>{var n=r(2117),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();t.exports=i},201:t=>{t.exports=function(t){return t}},7801:(t,e,r)=>{var n=r(2453),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=n((function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,(function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)})),e}));t.exports=a},3110:(t,e,r)=>{var n=r(1995);t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-1/0?"-0":e}},1893:(t,e,r)=>{var n=r(6317),o=r(788),i=r(8286),a=r(8855),c=Object.prototype,l=c.hasOwnProperty,u=n((function(t,e){t=Object(t);var r=-1,n=e.length,u=n>2?e[2]:void 0;for(u&&i(e[0],e[1],u)&&(n=1);++r<n;)for(var s=e[r],f=a(s),p=-1,d=f.length;++p<d;){var y=f[p],g=t[y];(void 0===g||o(g,c[y])&&!l.call(t,y))&&(t[y]=s[y])}return t}));t.exports=u},788:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},2645:(t,e,r)=>{var n=r(6457);t.exports=function(t){return null!=t&&t.length?n(t,1):[]}},9201:(t,e,r)=>{var n=r(4432);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},5066:(t,e,r)=>{var n=r(6776),o=r(3096);t.exports=function(t,e){return null!=t&&o(t,e,n)}},5346:t=>{t.exports=function(t){return t}},5075:t=>{t.exports=function(){return!1}},163:t=>{var e=Array.isArray;t.exports=e},981:(t,e,r)=>{var n=r(9642),o=r(8454);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},4801:t=>{t.exports=function(){return!1}},4155:(t,e,r)=>{var n=r(7190),o=r(9667),i=r(5075),a=r(163),c=r(981),l=r(4801),u=r(5782),s=r(4289),f=Object.prototype.hasOwnProperty;t.exports=function(t){if(null==t)return!0;if(c(t)&&(a(t)||"string"==typeof t||"function"==typeof t.splice||l(t)||s(t)||i(t)))return!t.length;var e=o(t);if("[object Map]"==e||"[object Set]"==e)return!t.size;if(u(t))return!n(t).length;for(var r in t)if(f.call(t,r))return!1;return!0}},9642:(t,e,r)=>{var n=r(563),o=r(7709);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},8454:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},7709:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},3474:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},3849:(t,e,r)=>{var n=r(563),o=r(9817),i=r(3474),a=Function.prototype,c=Object.prototype,l=a.toString,u=c.hasOwnProperty,s=l.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=u.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&l.call(r)==s}},1995:t=>{t.exports=function(){return!1}},4289:t=>{t.exports=function(){return!1}},7747:(t,e,r)=>{var n=r(5125)(Object.keys,Object);t.exports=n},8855:t=>{t.exports=function(t){var e=[];if(null!=t)for(var r in Object(t))e.push(r);return e}},1594:(t,e,r)=>{var n=r(503);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},4143:(t,e,r)=>{var n=r(8245),o=r(163);t.exports=function(t,e,r,i){return null==t?[]:(o(e)||(e=null==e?[]:[e]),o(r=i?void 0:r)||(r=null==r?[]:[r]),n(t,e,r))}},9082:(t,e,r)=>{var n=r(5436),o=r(5557)((function(t,e){return null==t?{}:n(t,e)}));t.exports=o},8532:(t,e,r)=>{var n=r(1600),o=r(3301),i=r(726),a=r(3110);t.exports=function(t){return i(t)?n(a(t)):o(t)}},7010:(t,e,r)=>{var n=r(6316);t.exports=function(t){return null==t?"":n(t)}},660:(t,e,r)=>{var n=r(7010),o=0;t.exports=function(t){var e=++o;return n(t)+e}},9787:e=>{"use strict";e.exports=t}},r={};function n(t){var o=r[t];if(void 0!==o)return o.exports;var i=r[t]={exports:{}};return e[t](i,i.exports,n),i.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var o={};return(()=>{"use strict";n.r(o),n.d(o,{Flyout:()=>ie,VictoryTooltip:()=>ge});var t=n(4143),e=n.n(t),r=n(3849),i=n.n(r),a=n(660),c=n.n(a),l=n(1893),u=n.n(l),s=n(9787),f=n.n(s);function p(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r={};for(var n in t)e.indexOf(n)>=0||Object.prototype.hasOwnProperty.call(t,n)&&(r[n]=t[n]);return r}function d(t){var e=function(t){return void 0!==t},r=t._x,n=t._x1,o=t._x0,i=t._voronoiX,a=t._y,c=t._y1,l=t._y0,s=t._voronoiY,f=e(n)?n:r,p=e(c)?c:a,d={x:e(i)?i:f,x0:e(o)?o:r,y:e(s)?s:p,y0:e(l)?l:a};return u()({},d,t)}function y(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"padding",r=t[e],n="number"==typeof r?r:0,o="object"==typeof r?r:{};return{top:o.top||n,bottom:o.bottom||n,left:o.left||n,right:o.right||n}}function g(t,e){return v(t)?t(e):t}function h(t,e){return e.disableInlineStyles?{}:t&&Object.keys(t).some((function(e){return v(t[e])}))?Object.keys(t).reduce((function(r,n){return r[n]=g(t[n],e),r}),{}):t}function v(t){return"function"==typeof t}function b(t,e){var r,n=d(e).x;return("number"==typeof(r=t.scale.x(n))?r/(Math.PI/180):r)%360}n(9082),n(8532);var O=["#252525","#525252","#737373","#969696","#bdbdbd","#d9d9d9","#f0f0f0"],m="#252525",x="#969696",j={width:450,height:300,padding:50,colorScale:O},w={fontFamily:"'Gill Sans', 'Seravek', 'Trebuchet MS', sans-serif",fontSize:14,letterSpacing:"normal",padding:10,fill:m,stroke:"transparent"},P=Object.assign({textAnchor:"middle"},w),S={area:Object.assign({style:{data:{fill:m},labels:w}},j),axis:Object.assign({style:{axis:{fill:"transparent",stroke:m,strokeWidth:1,strokeLinecap:"round",strokeLinejoin:"round"},axisLabel:Object.assign({},P,{padding:25}),grid:{fill:"none",stroke:"none",pointerEvents:"painted"},ticks:{fill:"transparent",size:1,stroke:"transparent"},tickLabels:w}},j),bar:Object.assign({style:{data:{fill:m,padding:8,strokeWidth:0},labels:w}},j),boxplot:Object.assign({style:{max:{padding:8,stroke:m,strokeWidth:1},maxLabels:Object.assign({},w,{padding:3}),median:{padding:8,stroke:m,strokeWidth:1},medianLabels:Object.assign({},w,{padding:3}),min:{padding:8,stroke:m,strokeWidth:1},minLabels:Object.assign({},w,{padding:3}),q1:{padding:8,fill:x},q1Labels:Object.assign({},w,{padding:3}),q3:{padding:8,fill:x},q3Labels:Object.assign({},w,{padding:3})},boxWidth:20},j),candlestick:Object.assign({style:{data:{stroke:m,strokeWidth:1},labels:Object.assign({},w,{padding:5})},candleColors:{positive:"#ffffff",negative:m}},j),chart:j,errorbar:Object.assign({borderWidth:8,style:{data:{fill:"transparent",stroke:m,strokeWidth:2},labels:w}},j),group:Object.assign({colorScale:O},j),histogram:Object.assign({style:{data:{fill:x,stroke:m,strokeWidth:2},labels:w}},j),legend:{colorScale:O,gutter:10,orientation:"vertical",titleOrientation:"top",style:{data:{type:"circle"},labels:w,title:Object.assign({},w,{padding:5})}},line:Object.assign({style:{data:{fill:"transparent",stroke:m,strokeWidth:2},labels:w}},j),pie:{style:{data:{padding:10,stroke:"transparent",strokeWidth:1},labels:Object.assign({},w,{padding:20})},colorScale:O,width:400,height:400,padding:50},scatter:Object.assign({style:{data:{fill:m,stroke:"transparent",strokeWidth:0},labels:w}},j),stack:Object.assign({colorScale:O},j),tooltip:{style:Object.assign({},w,{padding:0,pointerEvents:"none"}),flyoutStyle:{stroke:m,strokeWidth:1,fill:"#f0f0f0",pointerEvents:"none"},flyoutPadding:5,cornerRadius:5,pointerLength:10},voronoi:Object.assign({style:{data:{fill:"transparent",stroke:"transparent",strokeWidth:0},labels:Object.assign({},w,{padding:5,pointerEvents:"none"}),flyout:{stroke:m,strokeWidth:1,fill:"#f0f0f0",pointerEvents:"none"}}},j)},k=["#F4511E","#FFF59D","#DCE775","#8BC34A","#00796B","#006064"],A="#ECEFF1",E="#90A4AE",C="#455A64",_="#212121",z={width:350,height:350,padding:50},L={fontFamily:"'Helvetica Neue', 'Helvetica', sans-serif",fontSize:12,letterSpacing:"normal",padding:8,fill:C,stroke:"transparent",strokeWidth:0},W=Object.assign({textAnchor:"middle"},L),D="round",M="round",I={grayscale:S,material:{area:Object.assign({style:{data:{fill:_},labels:L}},z),axis:Object.assign({style:{axis:{fill:"transparent",stroke:E,strokeWidth:2,strokeLinecap:D,strokeLinejoin:M},axisLabel:Object.assign({},W,{padding:8,stroke:"transparent"}),grid:{fill:"none",stroke:A,strokeDasharray:"10, 5",strokeLinecap:D,strokeLinejoin:M,pointerEvents:"painted"},ticks:{fill:"transparent",size:5,stroke:E,strokeWidth:1,strokeLinecap:D,strokeLinejoin:M},tickLabels:Object.assign({},L,{fill:C})}},z),polarDependentAxis:Object.assign({style:{ticks:{fill:"transparent",size:1,stroke:"transparent"}}}),bar:Object.assign({style:{data:{fill:C,padding:8,strokeWidth:0},labels:L}},z),boxplot:Object.assign({style:{max:{padding:8,stroke:C,strokeWidth:1},maxLabels:Object.assign({},L,{padding:3}),median:{padding:8,stroke:C,strokeWidth:1},medianLabels:Object.assign({},L,{padding:3}),min:{padding:8,stroke:C,strokeWidth:1},minLabels:Object.assign({},L,{padding:3}),q1:{padding:8,fill:C},q1Labels:Object.assign({},L,{padding:3}),q3:{padding:8,fill:C},q3Labels:Object.assign({},L,{padding:3})},boxWidth:20},z),candlestick:Object.assign({style:{data:{stroke:C},labels:Object.assign({},L,{padding:5})},candleColors:{positive:"#ffffff",negative:C}},z),chart:z,errorbar:Object.assign({borderWidth:8,style:{data:{fill:"transparent",opacity:1,stroke:C,strokeWidth:2},labels:L}},z),group:Object.assign({colorScale:k},z),histogram:Object.assign({style:{data:{fill:C,stroke:_,strokeWidth:2},labels:L}},z),legend:{colorScale:k,gutter:10,orientation:"vertical",titleOrientation:"top",style:{data:{type:"circle"},labels:L,title:Object.assign({},L,{padding:5})}},line:Object.assign({style:{data:{fill:"transparent",opacity:1,stroke:C,strokeWidth:2},labels:L}},z),pie:Object.assign({colorScale:k,style:{data:{padding:8,stroke:A,strokeWidth:1},labels:Object.assign({},L,{padding:20})}},z),scatter:Object.assign({style:{data:{fill:C,opacity:1,stroke:"transparent",strokeWidth:0},labels:L}},z),stack:Object.assign({colorScale:k},z),tooltip:{style:Object.assign({},L,{padding:0,pointerEvents:"none"}),flyoutStyle:{stroke:_,strokeWidth:1,fill:"#f0f0f0",pointerEvents:"none"},flyoutPadding:5,cornerRadius:5,pointerLength:10},voronoi:Object.assign({style:{data:{fill:"transparent",stroke:"transparent",strokeWidth:0},labels:Object.assign({},L,{padding:5,pointerEvents:"none"}),flyout:{stroke:_,strokeWidth:1,fill:"#f0f0f0",pointerEvents:"none"}}},z)}},T=n(1594),H=n.n(T);function R(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i=[],a=!0,c=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){c=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(c)throw o}}return i}}(t,e)||F(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function F(t,e){if(t){if("string"==typeof t)return N(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?N(t,e):void 0}}function N(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var B={"American Typewriter":{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.25,.4203125,.3296875,.6,.6375,.8015625,.8203125,.1875,.45625,.45625,.6375,.5,.2734375,.309375,.2734375,.4390625,.6375,.6375,.6375,.6375,.6375,.6375,.6375,.6375,.6375,.6375,.2734375,.2734375,.5,.5,.5,.6,.6921875,.7640625,.6921875,.6375,.728125,.6734375,.6203125,.7109375,.784375,.3828125,.6421875,.7859375,.6375,.9484375,.7640625,.65625,.6375,.65625,.7296875,.6203125,.6375,.7109375,.740625,.940625,.784375,.7578125,.6203125,.4375,.5,.4375,.5,.5,.4921875,.5734375,.5890625,.5109375,.6,.528125,.43125,.5578125,.6375,.3109375,.40625,.6234375,.309375,.928125,.6375,.546875,.6,.58125,.4921875,.4921875,.4,.6203125,.625,.825,.6375,.640625,.528125,.5,.5,.5,.6671875],avg:.5793421052631578},Arial:{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.278125,.278125,.35625,.55625,.55625,.890625,.6671875,.1921875,.334375,.334375,.390625,.584375,.278125,.334375,.278125,.278125,.55625,.55625,.55625,.55625,.55625,.55625,.55625,.55625,.55625,.55625,.278125,.278125,.584375,.584375,.584375,.55625,1.015625,.6703125,.6671875,.7234375,.7234375,.6671875,.6109375,.778125,.7234375,.278125,.5,.6671875,.55625,.834375,.7234375,.778125,.6671875,.778125,.7234375,.6671875,.6109375,.7234375,.6671875,.9453125,.6671875,.6671875,.6109375,.278125,.278125,.278125,.4703125,.584375,.334375,.55625,.55625,.5,.55625,.55625,.3125,.55625,.55625,.2234375,.2703125,.5,.2234375,.834375,.55625,.55625,.55625,.55625,.346875,.5,.278125,.55625,.5,.7234375,.5,.5,.5,.334375,.2609375,.334375,.584375],avg:.528733552631579},"Arial Black":{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.33125,.334375,.5,.6609375,.6671875,1,.890625,.278125,.390625,.390625,.55625,.6609375,.334375,.334375,.334375,.28125,.6671875,.6671875,.6671875,.6671875,.6671875,.6671875,.6671875,.6671875,.6671875,.6671875,.334375,.334375,.6609375,.6609375,.6609375,.6109375,.7453125,.78125,.778125,.778125,.778125,.7234375,.6671875,.834375,.834375,.390625,.6671875,.834375,.6671875,.9453125,.834375,.834375,.7234375,.834375,.78125,.7234375,.7234375,.834375,.7796875,1.003125,.78125,.78125,.7234375,.390625,.28125,.390625,.6609375,.5125,.334375,.6671875,.6671875,.6671875,.6671875,.6671875,.41875,.6671875,.6671875,.334375,.384375,.6671875,.334375,1,.6671875,.6671875,.6671875,.6671875,.4703125,.6109375,.4453125,.6671875,.6140625,.946875,.6671875,.615625,.55625,.390625,.278125,.390625,.6609375],avg:.6213157894736842},Baskerville:{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.25,.25,.40625,.6671875,.490625,.875,.7015625,.178125,.2453125,.246875,.4171875,.6671875,.25,.3125,.25,.521875,.5,.5,.5,.5,.5,.5,.5,.5,.5,.5,.25,.25,.6671875,.6671875,.6671875,.396875,.9171875,.684375,.615625,.71875,.7609375,.625,.553125,.771875,.803125,.3546875,.515625,.78125,.6046875,.928125,.75,.8234375,.5625,.96875,.7296875,.5421875,.6984375,.771875,.7296875,.9484375,.771875,.678125,.6359375,.3640625,.521875,.3640625,.46875,.5125,.334375,.46875,.521875,.428125,.521875,.4375,.3890625,.4765625,.53125,.25,.359375,.4640625,.240625,.803125,.53125,.5,.521875,.521875,.365625,.334375,.2921875,.521875,.4640625,.678125,.4796875,.465625,.428125,.4796875,.5109375,.4796875,.6671875],avg:.5323519736842108},Courier:{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.5984375,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6078125,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.61875,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.615625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6140625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625],avg:.6020559210526316},"Courier New":{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.5984375,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625],avg:.6015296052631579},cursive:{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.1921875,.24375,.40625,.5671875,.3984375,.721875,.909375,.2328125,.434375,.365625,.4734375,.5578125,.19375,.3484375,.19375,.7734375,.503125,.4171875,.5453125,.45,.6046875,.4703125,.5984375,.55625,.503125,.5546875,.20625,.2,.5625,.5546875,.546875,.403125,.70625,.734375,.7078125,.64375,.85,.753125,.75,.6484375,1.0765625,.44375,.5359375,.8359375,.653125,1.0109375,1.1515625,.6796875,.6984375,1.0625,.8234375,.5125,.9234375,.8546875,.70625,.9109375,.7421875,.715625,.6015625,.4640625,.3359375,.4109375,.5421875,.5421875,.4328125,.5125,.5,.3859375,.7375,.359375,.75625,.540625,.5328125,.3203125,.5296875,.5015625,.484375,.7890625,.5640625,.4203125,.703125,.471875,.4734375,.35,.4125,.5640625,.471875,.6484375,.5296875,.575,.4140625,.415625,.20625,.3796875,.5421875],avg:.5604440789473684},fantasy:{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.215625,.2625,.3265625,.6109375,.534375,.7625,.7828125,.2,.4359375,.4359375,.3765625,.5109375,.2796875,.4609375,.2796875,.5296875,.6640625,.253125,.521875,.4765625,.6640625,.490625,.528125,.5546875,.496875,.5421875,.2796875,.2796875,.5625,.4609375,.5625,.4828125,.609375,.740625,.7234375,.740625,.8265625,.7234375,.6171875,.7359375,.765625,.240625,.5453125,.715625,.6078125,.8640625,.653125,.9125,.6484375,.946875,.6921875,.653125,.6953125,.8015625,.58125,.784375,.671875,.6265625,.690625,.4359375,.5296875,.4359375,.53125,.5,.2875,.5375,.603125,.4984375,.60625,.53125,.434375,.6421875,.56875,.209375,.4671875,.5484375,.2203125,.709375,.55,.5984375,.6140625,.5765625,.40625,.4734375,.3734375,.559375,.4421875,.6421875,.4890625,.578125,.4484375,.2546875,.2203125,.2546875,.55],avg:.536496710526316},Geneva:{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.3328125,.3046875,.5,.6671875,.6671875,.90625,.728125,.3046875,.446875,.446875,.5078125,.6671875,.3046875,.3796875,.3046875,.5390625,.6671875,.6671875,.6671875,.6671875,.6671875,.6671875,.6671875,.6671875,.6671875,.6671875,.3046875,.3046875,.6671875,.6671875,.6671875,.56875,.871875,.728125,.6375,.6515625,.7015625,.5765625,.5546875,.675,.690625,.2421875,.4921875,.6640625,.584375,.7890625,.709375,.7359375,.584375,.78125,.60625,.60625,.640625,.6671875,.728125,.946875,.6109375,.6109375,.5765625,.446875,.5390625,.446875,.6671875,.6671875,.5921875,.5546875,.6109375,.546875,.603125,.5765625,.390625,.6109375,.584375,.2359375,.334375,.5390625,.2359375,.8953125,.584375,.60625,.603125,.603125,.3875,.509375,.44375,.584375,.565625,.78125,.53125,.571875,.5546875,.4515625,.246875,.4515625,.6671875],avg:.5762664473684211},Georgia:{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2421875,.33125,.4125,.64375,.6109375,.81875,.7109375,.215625,.375,.375,.4734375,.64375,.2703125,.375,.2703125,.46875,.6140625,.4296875,.559375,.553125,.565625,.5296875,.5671875,.503125,.596875,.5671875,.3125,.3125,.64375,.64375,.64375,.4796875,.9296875,.715625,.6546875,.6421875,.75,.6546875,.6,.7265625,.815625,.390625,.51875,.7203125,.6046875,.928125,.7671875,.7453125,.6109375,.7453125,.7234375,.5625,.61875,.7578125,.70625,.99375,.7125,.6640625,.6015625,.375,.46875,.375,.64375,.65,.5,.5046875,.56875,.4546875,.575,.484375,.39375,.509375,.5828125,.29375,.3671875,.546875,.2875,.88125,.5921875,.5390625,.571875,.5640625,.4109375,.4328125,.3453125,.5765625,.5203125,.75625,.50625,.5171875,.4453125,.43125,.375,.43125,.64375],avg:.5551809210526316},"Gill Sans":{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2765625,.271875,.3546875,.584375,.5421875,.6765625,.625,.1890625,.3234375,.3234375,.4171875,.584375,.2203125,.3234375,.2203125,.28125,.5,.5,.5,.5,.5,.5,.5,.5,.5,.5,.2203125,.2296875,.584375,.584375,.584375,.334375,1.0109375,.6671875,.5640625,.709375,.75,.5,.4703125,.740625,.7296875,.25,.3125,.65625,.490625,.78125,.78125,.8234375,.5109375,.8234375,.6046875,.459375,.6046875,.709375,.6046875,1.0421875,.709375,.6046875,.646875,.334375,.28125,.334375,.4703125,.5828125,.334375,.428125,.5,.4390625,.5109375,.4796875,.296875,.428125,.5,.2203125,.2265625,.5,.2203125,.771875,.5,.553125,.5,.5,.3984375,.3859375,.334375,.5,.4390625,.7203125,.5,.4390625,.4171875,.334375,.2609375,.334375,.584375],avg:.4933717105263159},Helvetica:{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2796875,.2765625,.3546875,.5546875,.5546875,.8890625,.665625,.190625,.3328125,.3328125,.3890625,.5828125,.2765625,.3328125,.2765625,.3015625,.5546875,.5546875,.5546875,.5546875,.5546875,.5546875,.5546875,.5546875,.5546875,.5546875,.2765625,.2765625,.584375,.5828125,.584375,.5546875,1.0140625,.665625,.665625,.721875,.721875,.665625,.609375,.7765625,.721875,.2765625,.5,.665625,.5546875,.8328125,.721875,.7765625,.665625,.7765625,.721875,.665625,.609375,.721875,.665625,.94375,.665625,.665625,.609375,.2765625,.3546875,.2765625,.4765625,.5546875,.3328125,.5546875,.5546875,.5,.5546875,.5546875,.2765625,.5546875,.5546875,.221875,.240625,.5,.221875,.8328125,.5546875,.5546875,.5546875,.5546875,.3328125,.5,.2765625,.5546875,.5,.721875,.5,.5,.5,.3546875,.259375,.353125,.5890625],avg:.5279276315789471},"Helvetica Neue":{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.278125,.259375,.4265625,.55625,.55625,1,.6453125,.278125,.2703125,.26875,.353125,.6,.278125,.3890625,.278125,.36875,.55625,.55625,.55625,.55625,.55625,.55625,.55625,.55625,.55625,.55625,.278125,.278125,.6,.6,.6,.55625,.8,.6625,.6859375,.7234375,.7046875,.6125,.575,.759375,.7234375,.259375,.5203125,.6703125,.55625,.871875,.7234375,.7609375,.6484375,.7609375,.6859375,.6484375,.575,.7234375,.6140625,.9265625,.6125,.6484375,.6125,.259375,.36875,.259375,.6,.5,.25625,.5375,.59375,.5375,.59375,.5375,.2984375,.575,.55625,.2234375,.2375,.5203125,.2234375,.853125,.55625,.575,.59375,.59375,.334375,.5,.315625,.55625,.5,.759375,.51875,.5,.48125,.334375,.2234375,.334375,.6],avg:.5279440789473684},"Hoefler Text":{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2359375,.2234375,.3921875,.7125,.49375,.8859375,.771875,.2125,.3078125,.309375,.375,.4234375,.234375,.3125,.234375,.3,.5828125,.365625,.434375,.3921875,.5234375,.3984375,.5125,.4328125,.46875,.5125,.234375,.234375,.515625,.4234375,.515625,.340625,.7609375,.7359375,.6359375,.721875,.8125,.6375,.5875,.8078125,.853125,.4296875,.503125,.78125,.609375,.9609375,.8515625,.8140625,.6125,.8140625,.71875,.49375,.7125,.76875,.771875,1.125,.7765625,.7734375,.65625,.321875,.3078125,.321875,.3546875,.5,.3375,.446875,.5359375,.45,.5296875,.4546875,.425,.4921875,.54375,.2671875,.240625,.5390625,.25,.815625,.5375,.5234375,.5390625,.5421875,.365625,.36875,.35625,.5171875,.5015625,.75,.5,.509375,.44375,.2421875,.14375,.2421875,.35],avg:.5116447368421051},Montserrat:{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2625,.2609375,.3734375,.696875,.615625,.8296875,.6703125,.203125,.3296875,.3296875,.3875,.575,.2125,.3828125,.2125,.3953125,.6625,.3625,.56875,.5640625,.6625,.5671875,.609375,.5890625,.6390625,.609375,.2125,.2125,.575,.575,.575,.5671875,1.034375,.7171875,.7546875,.7203125,.8265625,.6703125,.634375,.7734375,.8140625,.303125,.5078125,.7125,.5890625,.95625,.8140625,.8390625,.71875,.8390625,.7234375,.615625,.575,.7921875,.6984375,1.1125,.65625,.6359375,.6515625,.31875,.396875,.31875,.5765625,.5,.6,.590625,.678125,.5640625,.678125,.6046875,.375,.6875,.678125,.2703125,.365625,.6015625,.2703125,1.0625,.678125,.628125,.678125,.678125,.4015625,.4890625,.40625,.6734375,.5421875,.8796875,.534375,.5671875,.5125,.334375,.2953125,.334375,.575],avg:.571792763157895},monospace:{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.5984375,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6078125,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.61875,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.615625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6140625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625,.6015625],avg:.6020559210526316},Overpass:{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2296875,.2765625,.4203125,.68125,.584375,.8515625,.7015625,.2203125,.3453125,.3453125,.53125,.63125,.2234375,.3953125,.2234375,.509375,.65,.4046875,.6171875,.60625,.6484375,.60625,.6015625,.5375,.615625,.6015625,.2234375,.2234375,.63125,.63125,.63125,.5015625,.8203125,.696875,.6671875,.65,.6859375,.6015625,.559375,.690625,.7078125,.2953125,.565625,.678125,.58125,.8046875,.7109375,.740625,.6421875,.740625,.6765625,.6046875,.590625,.696875,.6640625,.853125,.65,.6671875,.6625,.3734375,.509375,.3734375,.63125,.5125,.4,.5328125,.5625,.51875,.5625,.546875,.3359375,.5625,.565625,.25625,.3203125,.55,.265625,.85,.565625,.5671875,.5625,.5625,.4046875,.4765625,.3796875,.565625,.521875,.7265625,.53125,.5390625,.5125,.3671875,.275,.3671875,.63125],avg:.5430756578947369},Palatino:{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.25,.278125,.371875,.60625,.5,.840625,.778125,.209375,.334375,.334375,.390625,.60625,.2578125,.334375,.25,.60625,.5,.5,.5,.5,.5,.5,.5,.5,.5,.5,.25,.25,.60625,.60625,.60625,.4453125,.7484375,.778125,.6109375,.709375,.775,.6109375,.55625,.7640625,.8328125,.3375,.346875,.7265625,.6109375,.946875,.83125,.7875,.6046875,.7875,.66875,.525,.6140625,.778125,.7234375,1,.6671875,.6671875,.6671875,.334375,.60625,.334375,.60625,.5,.334375,.5,.565625,.4453125,.6109375,.4796875,.340625,.55625,.5828125,.2921875,.2671875,.5640625,.2921875,.8828125,.5828125,.546875,.6015625,.5609375,.3953125,.425,.3265625,.603125,.565625,.834375,.5171875,.55625,.5,.334375,.60625,.334375,.60625],avg:.5408552631578947},RedHatText:{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2328125,.2203125,.35625,.6890625,.55,.7390625,.6703125,.2140625,.4015625,.4015625,.4546875,.53125,.2203125,.45625,.2203125,.515625,.6609375,.3078125,.5484375,.5875,.61875,.5703125,.6203125,.559375,.6140625,.6203125,.2203125,.2234375,.465625,.534375,.465625,.5125,.7671875,.6609375,.6703125,.7265625,.728125,.6203125,.6109375,.8,.73125,.253125,.6,.6125,.6078125,.8625,.7390625,.8109375,.6546875,.809375,.6484375,.6234375,.6171875,.7125,.6609375,.8984375,.6546875,.646875,.60625,.3625,.5203125,.3625,.540625,.4609375,.5234375,.5265625,.584375,.509375,.5828125,.5578125,.3703125,.5828125,.553125,.2234375,.24375,.4890625,.2234375,.8453125,.553125,.58125,.584375,.5828125,.353125,.453125,.378125,.553125,.5015625,.6984375,.4875,.4984375,.459375,.3953125,.2921875,.3953125,.58125],avg:.5341940789473685},"sans-serif":{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.278125,.278125,.35625,.55625,.55625,.890625,.6671875,.1921875,.334375,.334375,.390625,.584375,.278125,.334375,.278125,.303125,.55625,.55625,.55625,.55625,.55625,.55625,.55625,.55625,.55625,.55625,.278125,.278125,.5859375,.584375,.5859375,.55625,1.015625,.6671875,.6671875,.7234375,.7234375,.6671875,.6109375,.778125,.7234375,.278125,.5,.6671875,.55625,.834375,.7234375,.778125,.6671875,.778125,.7234375,.6671875,.6109375,.7234375,.6671875,.9453125,.6671875,.6671875,.6109375,.278125,.35625,.278125,.478125,.55625,.334375,.55625,.55625,.5,.55625,.55625,.278125,.55625,.55625,.2234375,.2421875,.5,.2234375,.834375,.55625,.55625,.55625,.55625,.334375,.5,.278125,.55625,.5,.7234375,.5,.5,.5,.35625,.2609375,.3546875,.590625],avg:.5293256578947368},Seravek:{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.215625,.296875,.4171875,.6734375,.4953125,.9125,.740625,.2421875,.3375,.3375,.409375,.60625,.2609375,.35625,.25625,.41875,.5921875,.3515625,.475,.4875,.5375,.509375,.5484375,.4546875,.5421875,.5484375,.25625,.2546875,.5875,.6171875,.5875,.4578125,.8140625,.6765625,.5703125,.6109375,.684375,.5109375,.4953125,.678125,.6859375,.2625,.2625,.5859375,.4734375,.846875,.709375,.740625,.509375,.740625,.584375,.5015625,.528125,.675,.5953125,.9453125,.596875,.540625,.540625,.359375,.4203125,.359375,.5109375,.421875,.4046875,.5015625,.5421875,.446875,.5453125,.484375,.38125,.5140625,.5546875,.240625,.2640625,.490625,.2765625,.8625,.5546875,.546875,.5453125,.5453125,.3625,.41875,.3890625,.5453125,.4703125,.7546875,.4921875,.4609375,.453125,.4015625,.2640625,.4015625,.58125],avg:.5044078947368421},serif:{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2484375,.334375,.409375,.5,.5,.834375,.778125,.18125,.334375,.334375,.5,.5640625,.25,.334375,.25,.278125,.5,.5,.5,.5,.5,.5,.5,.5,.5,.5,.278125,.278125,.5640625,.5640625,.5640625,.4453125,.921875,.7234375,.6671875,.6671875,.7234375,.6109375,.55625,.7234375,.7234375,.334375,.390625,.7234375,.6109375,.890625,.7234375,.7234375,.55625,.7234375,.6671875,.55625,.6109375,.7234375,.7234375,.9453125,.7234375,.7234375,.6109375,.334375,.340625,.334375,.4703125,.5,.3453125,.4453125,.5,.4453125,.5,.4453125,.3828125,.5,.5,.278125,.3359375,.5,.278125,.778125,.5,.5,.5,.5,.3375,.390625,.2796875,.5,.5,.7234375,.5,.5,.4453125,.48125,.2015625,.48125,.5421875],avg:.5126315789473684},Tahoma:{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.3109375,.3328125,.4015625,.728125,.546875,.9765625,.70625,.2109375,.3828125,.3828125,.546875,.728125,.303125,.3640625,.303125,.3953125,.546875,.546875,.546875,.546875,.546875,.546875,.546875,.546875,.546875,.546875,.3546875,.3546875,.728125,.728125,.728125,.475,.909375,.6109375,.590625,.6015625,.6796875,.5625,.521875,.66875,.6765625,.3734375,.4171875,.6046875,.4984375,.771875,.66875,.7078125,.5515625,.7078125,.6375,.5578125,.5875,.65625,.60625,.903125,.58125,.5890625,.559375,.3828125,.39375,.3828125,.728125,.5625,.546875,.525,.553125,.4625,.553125,.5265625,.3546875,.553125,.5578125,.2296875,.328125,.51875,.2296875,.840625,.5578125,.54375,.553125,.553125,.3609375,.446875,.3359375,.5578125,.4984375,.7421875,.4953125,.4984375,.4453125,.48125,.3828125,.48125,.728125],avg:.5384374999999998},"Times New Roman":{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2484375,.334375,.409375,.5,.5,.834375,.778125,.18125,.334375,.334375,.5,.5640625,.25,.334375,.25,.28125,.5,.5,.5,.5,.5,.5,.5,.5,.5,.5,.278125,.278125,.5640625,.5640625,.5640625,.4453125,.921875,.7234375,.6671875,.6671875,.7234375,.6109375,.55625,.7234375,.7234375,.334375,.390625,.73125,.6109375,.890625,.7375,.7234375,.55625,.7234375,.6765625,.55625,.6109375,.7234375,.7234375,.9453125,.7234375,.7234375,.6109375,.334375,.28125,.334375,.4703125,.51875,.334375,.4453125,.503125,.4453125,.503125,.4453125,.4359375,.5,.5,.278125,.35625,.50625,.278125,.778125,.5,.5,.5046875,.5,.340625,.390625,.2796875,.5,.5,.7234375,.5,.5,.4453125,.48125,.2015625,.48125,.5421875],avg:.5134375},"Trebuchet MS":{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.3015625,.3671875,.325,.53125,.525,.6015625,.70625,.1609375,.3671875,.3671875,.3671875,.525,.3671875,.3671875,.3671875,.525,.525,.525,.525,.525,.525,.525,.525,.525,.525,.525,.3671875,.3671875,.525,.525,.525,.3671875,.771875,.590625,.5671875,.5984375,.6140625,.5359375,.525,.6765625,.6546875,.2796875,.4765625,.5765625,.5078125,.7109375,.6390625,.675,.5578125,.7421875,.5828125,.48125,.58125,.6484375,.5875,.853125,.5578125,.5703125,.5515625,.3671875,.3578125,.3671875,.525,.53125,.525,.5265625,.5578125,.4953125,.5578125,.546875,.375,.503125,.546875,.2859375,.3671875,.5046875,.2953125,.83125,.546875,.5375,.5578125,.5578125,.3890625,.40625,.396875,.546875,.490625,.7453125,.5015625,.49375,.475,.3671875,.525,.3671875,.525],avg:.5085197368421052},Verdana:{widths:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.35,.39375,.459375,.81875,.6359375,1.0765625,.759375,.26875,.4546875,.4546875,.6359375,.81875,.3640625,.4546875,.3640625,.4703125,.6359375,.6359375,.6359375,.6359375,.6359375,.6359375,.6359375,.6359375,.6359375,.6359375,.4546875,.4546875,.81875,.81875,.81875,.546875,1,.684375,.6859375,.6984375,.771875,.6328125,.575,.7765625,.7515625,.421875,.4546875,.69375,.5578125,.84375,.7484375,.7875,.603125,.7875,.7,.684375,.6171875,.7328125,.684375,.9890625,.6859375,.615625,.6859375,.4546875,.46875,.4546875,.81875,.6421875,.6359375,.6015625,.6234375,.521875,.6234375,.596875,.384375,.6234375,.6328125,.275,.3765625,.5921875,.275,.9734375,.6328125,.6078125,.6234375,.6234375,.43125,.521875,.3953125,.6328125,.5921875,.81875,.5921875,.5921875,.5265625,.6359375,.4546875,.6359375,.81875],avg:.6171875000000003}},V={mm:3.8,sm:38,pt:1.33,pc:16,in:96,px:1},q={em:1,ex:.5},U={lineHeight:1,letterSpacing:"0px",fontSize:0,angle:0,fontFamily:""},$=function(t){return Array.isArray(t)?t:t.toString().split(/\r\n|\r|\n/g)},G=function(t,e,r){var n=function(t){return t*Math.PI/180}(r);return Math.abs(Math.cos(n)*t)+Math.abs(Math.sin(n)*e)},K=function(t,e){var r,n=null===(r=t.match(/[a-zA-Z%]+/))||void 0===r?void 0:r[0],o=Number(t.match(/[0-9.,]+/));return n?V.hasOwnProperty(n)?o*V[n]:q.hasOwnProperty(n)?(e?o*e:o*U.fontSize)*q[n]:o:o||0},Z=function(t,e){var r=Array.isArray(t)?t[e]:t,n=u()({},r,U);return Object.assign({},n,{fontFamily:n.fontFamily,letterSpacing:"number"==typeof n.letterSpacing?n.letterSpacing:K(String(n.letterSpacing),n.fontSize),fontSize:"number"==typeof n.fontSize?n.fontSize:K(String(n.fontSize))})},X=function(t,e){if(void 0===t||""===t||null===t)return 0;var r,n=$(t).map((function(t,r){var n,o,i=t.toString().length,a=Z(e,r),c=a.fontSize,l=a.letterSpacing,u=(n=a.fontFamily,o=n.split(",").map((function(t){return t.replace(/'|"/g,"")})).find((function(t){return B[t]}))||"Helvetica",B[o]);return t.toString().split("").map((function(t){return t.charCodeAt(0)<u.widths.length?u.widths[t.charCodeAt(0)]:u.avg})).reduce((function(t,e){return e+t}),0)*c+l*Math.max(i-1,0)}));return Math.max.apply(Math,function(t){if(Array.isArray(t))return N(t)}(r=n)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(r)||F(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())},Y=function(t,e){var r=Array.isArray(e)?e[0]&&e[0].angle:e&&e.angle,n=function(t,e){return void 0===t||""===t||null===t?0:$(t).reduce((function(t,r,n){var o=Z(e,n),i=r.toString().match(/[(A-Z)(0-9)]/)?1.15*o.fontSize:o.fontSize;return t+o.lineHeight*i}),0)}(t,e),o=X(t,e);return{width:r?G(o,n,r):o,height:1.05*(r?G(n,o,r):n)}},J=H()((function(){var t=document.createElementNS("http://www.w3.org/2000/svg","svg");t.setAttribute("xlink","http://www.w3.org/1999/xlink"),t.setAttribute("width","300"),t.setAttribute("height","300"),t.setAttribute("viewBox","0 0 300 300"),t.setAttribute("aria-hidden","true");var e=document.createElementNS("http://www.w3.org/2000/svg","text");return t.appendChild(e),t.style.position="fixed",t.style.top="-9999px",t.style.left="-9999px",document.body.appendChild(t),e})),Q=function(t){return t?"".concat(t.angle,":").concat(t.fontFamily,":").concat(t.fontSize,":").concat(t.letterSpacing,":").concat(t.lineHeight):"null"},tt=H()((function(t,e){var r,n,o=J();o.isConnected||(null===(r=(n=J.cache).clear)||void 0===r||r.call(n),o=J());var i=$(t),a=0;for(var c of i.entries()){var l=R(c,2),u=l[0],s=l[1],f=document.createElementNS("http://www.w3.org/2000/svg","tspan"),p=Z(e,u);f.style.fontFamily=p.fontFamily,f.style.fontSize="".concat(p.fontSize,"px"),f.style.lineHeight=p.lineHeight,f.style.fontFamily=p.fontFamily,f.style.letterSpacing=p.letterSpacing,f.textContent=s,f.setAttribute("x","0"),f.setAttribute("y","".concat(a)),o.appendChild(f),a+=p.lineHeight*f.getBoundingClientRect().height}var d=o.getBoundingClientRect().width;return o.innerHTML="",{width:null!=e&&e.angle?G(d,a,null==e?void 0:e.angle):d,height:null!=e&&e.angle?G(a,d,null==e?void 0:e.angle):a}}),(function(t,e){var r=Array.isArray(t)?t.join():t,n=Array.isArray(e)?e.map(Q).join():Q(e);return"".concat(r,"::").concat(n)})),et=function(t,e){return function(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return"undefined"==typeof window||void 0===window.document||void 0===window.document.createElement||r?Y(t,e):tt(t,e)}(t,e)};var rt=f().createContext({});function nt(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ot(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function it(t,e){return it=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},it(t,e)}function at(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function ct(t){return ct=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ct(t)}rt.displayName="PortalContext";var lt=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&it(t,e)}(a,t);var e,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=ct(n);if(o){var r=ct(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return at(this,t)});function a(){var t;nt(this,a);for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return(t=i.call.apply(i,[this].concat(r))).checkedContext=void 0,t.portalKey=void 0,t.renderInPlace=void 0,t.element=void 0,t}return e=a,(r=[{key:"componentDidMount",value:function(){this.checkedContext||("function"!=typeof this.context.portalUpdate&&(this.renderInPlace=!0),this.checkedContext=!0),this.forceUpdate()}},{key:"componentDidUpdate",value:function(){this.renderInPlace||(this.portalKey=this.portalKey||this.context.portalRegister(),this.context.portalUpdate(this.portalKey,this.element))}},{key:"componentWillUnmount",value:function(){this.context&&this.context.portalDeregister&&this.context.portalDeregister(this.portalKey)}},{key:"renderPortal",value:function(t){return this.renderInPlace?t:(this.element=t,null)}},{key:"render",value:function(){var t=Array.isArray(this.props.children)?this.props.children[0]:this.props.children,e=this.props.groupComponent,r=t&&t.props||{},n=r.groupComponent?{groupComponent:e,standalone:!1}:{},o=u()(n,r,p(this.props,["children","groupComponent"])),i=t&&f().cloneElement(t,o);return this.renderPortal(i)}}])&&ot(e.prototype,r),Object.defineProperty(e,"prototype",{writable:!1}),a}(f().Component);lt.displayName="VictoryPortal",lt.role="portal",lt.defaultProps={groupComponent:f().createElement("g",null)},lt.contextType=rt;var ut=n(4155),st=n.n(ut),ft=["desc","id","tabIndex","origin"];function pt(){return pt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},pt.apply(this,arguments)}function dt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function yt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var gt=(0,s.forwardRef)((function(t,e){var r,n=t.desc,o=t.id,i=t.tabIndex,a=(t.origin,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,ft)),c=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dt(Object(r),!0).forEach((function(e){yt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({vectorEffect:"non-scaling-stroke",id:null===(r=g(o,t))||void 0===r?void 0:r.toString(),tabIndex:g(i,t)},a);return n?f().createElement("rect",pt({},c,{ref:e}),f().createElement("desc",null,n)):f().createElement("rect",pt({},c,{ref:e}))})),ht=["children","desc","id","origin","tabIndex","title"];function vt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function bt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Ot=function(t){var e,r=t.children,n=t.desc,o=t.id,i=(t.origin,t.tabIndex),a=t.title,c=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,ht),l=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?vt(Object(r),!0).forEach((function(e){bt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):vt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({id:null===(e=g(o,t))||void 0===e?void 0:e.toString(),tabIndex:g(i,t)},c);return f().createElement("text",l,a&&f().createElement("title",null,a),n&&f().createElement("desc",null,n),r)},mt=["desc","id","tabIndex","origin"];function xt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function jt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var wt=function(t){t.desc;var e,r=t.id,n=t.tabIndex,o=(t.origin,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,mt)),i=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?xt(Object(r),!0).forEach((function(e){jt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({id:null===(e=g(r,t))||void 0===e?void 0:e.toString(),tabIndex:g(n,t)},o);return f().createElement("tspan",i)},Pt=function(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];if(r.length>0)return r.reduce((function(t,e){return[t,Pt(e)].join(" ")}),Pt(t)).trim();if(null==t||"string"==typeof t)return t;var o=[];for(var i in t)if(t.hasOwnProperty(i)){var a=t[i];o.push("".concat(i,"(").concat(a,")"))}return o.join(" ").trim()};function St(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i=[],a=!0,c=!1;try{for(r=r.call(t);!(a=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(t){c=!0,o=t}finally{try{a||null==r.return||r.return()}finally{if(c)throw o}}return i}}(t,e)||function(t,e){if(t){if("string"==typeof t)return kt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?kt(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function kt(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function At(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Et(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Ct={startsWith:["data-","aria-"],exactMatch:[]};function _t(t,e){if(null==t)throw new Error(e)}var zt=function(t){var e=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?At(Object(r),!0).forEach((function(e){Et(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):At(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},t);return Object.fromEntries(Object.entries(e).filter((function(t){return function(t){return!(!function(t){var e=!1;return Ct.startsWith.forEach((function(r){new RegExp("\\b(".concat(r,")(\\w|-)+"),"g").test(t)&&(e=!0)})),e}(t)&&!function(t){return Ct.exactMatch.includes(t)}(t))}(St(t,1)[0])})).map((function(e){var r=St(e,2);return[r[0],g(r[1],t)]})))};function Lt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Wt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Lt(Object(r),!0).forEach((function(e){Dt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Lt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Dt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Mt(t){return function(t){if(Array.isArray(t))return It(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return It(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?It(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function It(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Tt={fill:"#252525",fontSize:14,fontFamily:"'Gill Sans', 'Gill Sans MT', 'Ser­avek', 'Trebuchet MS', sans-serif",stroke:"transparent"},Ht=function(t,e){if(!t.datum)return 0;var r=function(t,e){var r=t.scale,n=t.polar,o=t.horizontal,i=d(e),a=t.origin||{x:0,y:0},c=o?r.y(i.y):r.x(i.x),l=o?r.y(i.y0):r.x(i.x0),u=o?r.x(i.x):r.y(i.y),s=o?r.x(i.x0):r.y(i.y0);return{x:n?u*Math.cos(c)+a.x:c,x0:n?s*Math.cos(l)+a.x:l,y:n?-u*Math.sin(c)+a.y:u,y0:n?-s*Math.sin(l)+a.x:s}}(t,t.datum);return r[e]},Rt=function(t){var e=t&&t.fontSize;if("number"==typeof e)return e;if(null==e)return Tt.fontSize;if("string"==typeof e){var r=Number(e.replace("px",""));return isNaN(r)?Tt.fontSize:r}return Tt.fontSize},Ft=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return Array.isArray(t)?t[e]||t[0]:t},Nt=function(t){var e=t.backgroundStyle,r=t.backgroundPadding;return Array.isArray(e)&&!st()(e)||Array.isArray(r)&&!st()(r)},Bt=function(t,e){var r=t.direction,n=t.textAnchor,o=t.x,i=t.dx;if("rtl"===r)return o-e;switch(n){case"middle":return Math.round(o-e/2);case"end":return Math.round(o-e);default:return o+(i||0)}},Vt=function(t,e){var r=t.verticalAnchor,n=t.y,o=t.originalDy,i=n+(void 0===o?0:o);switch(r){case"start":return Math.floor(i);case"end":return Math.ceil(i-e);default:return Math.floor(i-e/2)}},qt=function(t,e){return Nt(t)?function(t,e){var r=t.dy,n=t.dx,o=t.transform,i=t.backgroundStyle,a=t.backgroundPadding,c=t.backgroundComponent,l=t.inline,s=t.y,p=e.map((function(t,o){var i=Ft(e,o-1),c=t.textSize,u=t.fontSize*t.lineHeight,f=Math.ceil(u),p=Ft(a,o),d=Ft(a,o-1),y=l&&n||0,g=o&&!l?i.fontSize*i.lineHeight+d.top+d.bottom:r-.5*u-(t.fontSize-t.capHeight);return{textHeight:f,labelSize:c,heightWithPadding:f+p.top+p.bottom,widthWithPadding:c.width+p.left+p.right+y,y:s,fontSize:t.fontSize,dy:g}}));return p.map((function(e,r){var n=Bt(t,e.labelSize.width),d=p.slice(0,r+1).reduce((function(t,e){return t+e.dy}),s),y=Ft(a,r),g=e.heightWithPadding,h=l?function(t,e,r){var n=t.textAnchor,o=e.map((function(t){return t.widthWithPadding})),i=-o.reduce((function(t,e){return t+e}),0)/2;switch(n){case"start":return o.reduce((function(t,e,n){return n<r?t+e:t}),0);case"end":return o.reduce((function(t,e,n){return n>r?t-e:t}),0);default:return o.reduce((function(t,e,n){return n===r?t+e/2:t+(n<r?e:0)}),i)}}(t,p,r)+n-y.left:n,v=l?Vt(t,g)-y.top:d,b={key:"tspan-background-".concat(r),height:g,style:Ft(i,r),width:e.widthWithPadding,transform:o,x:h-y.left,y:v};return f().cloneElement(c,u()({},c.props,b))}))}(t,e):function(t,e){var r=t.dx,n=void 0===r?0:r,o=t.transform,i=t.backgroundComponent,a=t.backgroundStyle,c=t.inline,l=t.backgroundPadding,s=t.capHeight,p=e.map((function(t){return t.textSize})),d=c?Math.max.apply(Math,Mt(p.map((function(t){return t.height})))):p.reduce((function(t,r,n){var o=n?0:s/2;return t+r.height*(e[n].lineHeight-o)}),0),y=c?p.reduce((function(t,e,r){var o=r?n:0;return t+e.width+o}),0):Math.max.apply(Math,Mt(p.map((function(t){return t.width})))),g=Bt(t,y),h=Vt(t,d),v={key:"background",height:d+l.top+l.bottom,style:a,transform:o,width:y+l.left+l.right,x:c?g-l.left:g+n-l.left,y:h};return f().cloneElement(i,u()({},i.props,v))}(t,e)},Ut=function(t,e,r){var n=e.inline,o=Ft(t,r);return r&&!n?function(t,e,r){var n=Ft(t,e),o=Ft(t,e-1),i=o.fontSize*o.lineHeight,a=n.fontSize*n.lineHeight,c=o.fontSize-o.capHeight,l=n.fontSize-n.capHeight,u=i-o.fontSize/2+n.fontSize/2-i/2+a/2-l/2+c/2;return Nt(r)?u+n.backgroundPadding.top+o.backgroundPadding.bottom:u}(t,r,e):n?0===r?o.backgroundPadding.top:void 0:o.backgroundPadding.top},$t=function(t){var e=g(t.ariaLabel,t),r=Ft(t.style),n=function(t){var e=g(t.lineHeight,t);return Array.isArray(e)&&st()(e)?[1]:e}(t),o=t.direction?g(t.direction,t):"inherit",i=t.textAnchor?g(t.textAnchor,t):r.textAnchor||"start",a=t.verticalAnchor?g(t.verticalAnchor,t):r.verticalAnchor||"middle",c=t.dx?g(t.dx,t):0,l=function(t,e,r){var n=t.dy?g(t.dy,t):0,o=t.inline?1:t.text.length,i=g(t.capHeight,t),a=e?g(e,t):"middle",c=Mt(Array(o).keys()).map((function(e){return Ft(t.style,e).fontSize})),l=Mt(Array(o).keys()).map((function(t){return Ft(r,t)}));if("start"===a)return n+(i/2+l[0]/2)*c[0];if(t.inline)return"end"===a?n+(i/2-l[0]/2)*c[0]:n+i/2*c[0];if(1===o)return"end"===a?n+(i/2+(.5-o)*l[0])*c[0]:n+(i/2+(.5-o/2)*l[0])*c[0];var u=Mt(Array(o).keys()).reduce((function(t,e){return t+(i/2+(.5-o)*l[e])*c[e]/o}),0);return"end"===a?n+u:n+u/2+i/2*l[o-1]*c[o-1]}(t,a,n),u=void 0!==t.x?t.x:Ht(t,"x"),s=void 0!==t.y?t.y:Ht(t,"y"),f=function(t,e,r){var n=t.polar,o=Ft(t.style),i=n?function(t,e){var r=t.labelPlacement,n=t.datum;if(!r||"vertical"===r)return 0;var o=b(t,n),i=0;return 0===o||180===o?i=90:o>0&&o<180?i=90-o:o>180&&o<360&&(i=270-o),i+(o>90&&o<180||o>270?1:-1)*("perpendicular"===r?0:90)}(t):0,a=void 0===o.angle?g(t.angle,t):o.angle,c=void 0===a?i:a,l=t.transform||o.transform,u=l&&g(l,t);return u||c?Pt(u,c&&{rotate:[c,e,r]}):void 0}(t,u,s);return Object.assign({},t,{ariaLabel:e,lineHeight:n,direction:o,textAnchor:i,verticalAnchor:a,dx:c,dy:l,originalDy:g(t.dy,t),transform:f,x:u,y:s})},Gt={backgroundComponent:f().createElement(gt,null),groupComponent:f().createElement("g",null),direction:"inherit",textComponent:f().createElement(Ot,null),tspanComponent:f().createElement(wt,null),capHeight:.71,lineHeight:1},Kt=function(t){var e=function(t){var e=function(t,e){if(null!=t){if(Array.isArray(t))return t.map((function(t){return g(t,e)}));var r=g(t,e);if(null!=r)return Array.isArray(r)?r:"".concat(r).split("\n")}}(t.text,t),r=function(t,e){if(e.disableInlineStyles){var r=h(t,e);return{fontSize:Rt(r)}}var n=function(t){var r=h(t?u()({},t,Tt):Tt,e);return Object.assign({},r,{fontSize:Rt(r)})};return Array.isArray(t)&&!st()(t)?t.map((function(t){return n(t)})):n(t)}(t.style,Object.assign({},t,{text:e})),n=function(t,e){if(t)return Array.isArray(t)&&!st()(t)?t.map((function(t){return h(t,e)})):h(t,e)}(t.backgroundStyle,Object.assign({},t,{text:e,style:r})),o=function(t){return t.backgroundPadding&&Array.isArray(t.backgroundPadding)?t.backgroundPadding.map((function(e){return y({padding:g(e,t)})})):y({padding:g(t.backgroundPadding,t)})}(Object.assign({},t,{text:e,style:r,backgroundStyle:n})),i=g(t.id,t);return Object.assign({},t,{backgroundStyle:n,backgroundPadding:o,style:r,text:e,id:i})}(Wt(Wt({},Gt),t));if(null===e.text||void 0===e.text)return null;var r=$t(e),n=r.text,o=r.style,i=r.capHeight,a=r.backgroundPadding,c=r.lineHeight,l=n.map((function(t,e){var r=Ft(o,e),n=K("".concat(i,"em"),r.fontSize),l=Ft(c,e);return{style:r,fontSize:r.fontSize||Tt.fontSize,capHeight:n,text:t,textSize:et(t,r),lineHeight:l,backgroundPadding:Ft(a,e)}})),s=function(t,e){var r=t.ariaLabel,n=t.inline,o=t.className,i=t.title,a=t.events,c=t.direction,l=t.text,u=t.textAnchor,s=t.dx,p=t.dy,d=t.transform,y=t.x,h=t.y,v=t.desc,b=t.id,O=t.tabIndex,m=t.tspanComponent,x=t.textComponent,j=zt(t),w=Wt(Wt({"aria-label":r,key:"text"},a),{},{direction:c,dx:s,x:y,y:h+p,transform:d,className:o,title:i,desc:g(v,t),tabIndex:g(O,t),id:b},j),P=l.map((function(r,o){var i=e[o].style,a={key:"".concat(b,"-key-").concat(o),x:n?void 0:y,dx:n?s+e[o].backgroundPadding.left:s,dy:Ut(e,t,o),textAnchor:i.textAnchor||u,style:i,children:r};return f().cloneElement(m,a)}));return f().cloneElement(x,w,P)}(r,l);if(e.backgroundStyle){var p=[qt(r,l),s],d=f().cloneElement(e.groupComponent,{},p);return e.renderInPortal?f().createElement(lt,null,d):d}return e.renderInPortal?f().createElement(lt,null,s):s};Kt.displayName="VictoryLabel",Kt.role="label",Kt.defaultStyles=Tt;var Zt=["desc","id","tabIndex","origin"];function Xt(){return Xt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Xt.apply(this,arguments)}function Yt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Jt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Qt=(0,s.forwardRef)((function(t,e){var r,n=t.desc,o=t.id,i=t.tabIndex,a=(t.origin,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,Zt)),c=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Yt(Object(r),!0).forEach((function(e){Jt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Yt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({id:null===(r=g(o,t))||void 0===r?void 0:r.toString(),tabIndex:g(i,t)},a);return n?f().createElement("path",Xt({},c,{ref:e}),f().createElement("desc",null,n)):f().createElement("path",Xt({},c,{ref:e}))}));function te(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ee(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?te(Object(r),!0).forEach((function(e){re(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):te(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function re(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var ne=function(t){var e=t.orientation||"top";return"left"===e||"right"===e?function(t){var e=t.pointerWidth,r=t.cornerRadius,n=t.orientation,o=t.width,i=t.height,a=t.center,c="left"===n?1:-1,l=t.x+(t.dx||0),u=t.y+(t.dy||0),s=a.x,f=a.y,p=s-c*(o/2),d=s+c*(o/2),y=f+i/2,g=f-i/2,h=c*(l-p)>0?0:t.pointerLength,v="left"===n?"0 0 0":"0 0 1",b="".concat(r," ").concat(r," ").concat(v);return"M ".concat(p,", ").concat(f-e/2,"\n    L ").concat(h?l:p,", ").concat(h?u:f+e/2,"\n    L ").concat(p,", ").concat(f+e/2,"\n    L ").concat(p,", ").concat(y-r,"\n    A ").concat(b," ").concat(p+c*r,", ").concat(y,"\n    L ").concat(d-c*r,", ").concat(y,"\n    A ").concat(b," ").concat(d,", ").concat(y-r,"\n    L ").concat(d,", ").concat(g+r,"\n    A ").concat(b," ").concat(d-c*r,", ").concat(g,"\n    L ").concat(p+c*r,", ").concat(g,"\n    A ").concat(b," ").concat(p,", ").concat(g+r,"\n    z")}(t):function(t){var e=t.pointerWidth,r=t.cornerRadius,n=t.orientation,o=t.width,i=t.height,a=t.center,c="bottom"===n?1:-1,l=t.x+(t.dx||0),u=t.y+(t.dy||0),s=a.x,f=a.y,p=f+c*(i/2),d=f-c*(i/2),y=s+o/2,g=s-o/2,h=c*(u-p)<0?0:t.pointerLength,v="bottom"===n?"0 0 0":"0 0 1",b="".concat(r," ").concat(r," ").concat(v);return"M ".concat(s-e/2,", ").concat(p,"\n    L ").concat(h?l:s+e/2,", ").concat(h?u:p,"\n    L ").concat(s+e/2,", ").concat(p,"\n    L ").concat(y-r,", ").concat(p,"\n    A ").concat(b," ").concat(y,", ").concat(p-c*r,"\n    L ").concat(y,", ").concat(d+c*r,"\n    A ").concat(b," ").concat(y-r,", ").concat(d,"\n    L ").concat(g+r,", ").concat(d,"\n    A ").concat(b," ").concat(g,", ").concat(d+c*r,"\n    L ").concat(g,", ").concat(p-c*r,"\n    A ").concat(b," ").concat(g+r,", ").concat(p,"\n    z")}(t)},oe={pathComponent:f().createElement(Qt,null),role:"presentation",shapeRendering:"auto"},ie=function(t){var e=function(t){var e=g(t.id,t),r=h(t.style,t);return ee(ee({},t),{},{id:e,style:r})}(ee(ee({},oe),t)),r=zt(e);_t(e.height,"Flyout props[height] is undefined"),_t(e.width,"Flyout props[width] is undefined"),_t(e.x,"Flyout props[x] is undefined"),_t(e.y,"Flyout props[y] is undefined");var n={center:e.center||{x:0,y:0},cornerRadius:e.cornerRadius||0,dx:e.dx,dy:e.dy,height:e.height,orientation:e.orientation||"top",pointerLength:e.pointerLength||0,pointerWidth:e.pointerWidth||0,width:e.width,x:e.x,y:e.y};return f().cloneElement(e.pathComponent,ee(ee(ee({},e.events),r),{},{style:e.style,d:ne(n),className:e.className,shapeRendering:e.shapeRendering,role:e.role,transform:e.transform,clipPath:e.clipPath}))};function ae(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ce(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function le(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ce(Object(r),!0).forEach((function(e){ue(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ce(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ue(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function se(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function fe(t,e){return fe=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},fe(t,e)}function pe(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function de(t){return de=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},de(t)}var ye={cornerRadius:5,pointerLength:10,pointerWidth:10},ge=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fe(t,e)}(s,t);var r,n,o,a,l=(o=s,a=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=de(o);if(a){var r=de(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return pe(this,t)});function s(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,s),(e=l.call(this,t)).id=void 0,e.id=void 0===t.id?c()("tooltip-"):t.id,e}return r=s,(n=[{key:"getDefaultOrientation",value:function(t){var e=t.datum,r=t.horizontal;if(!t.polar){var n=r?"right":"top",o=r?"left":"bottom";return e&&e.y<0?o:n}return this.getPolarOrientation(t)}},{key:"getPolarOrientation",value:function(t){var e=b(t,t.datum),r=t.labelPlacement||"vertical";return"vertical"===r?this.getVerticalOrientations(e):"parallel"===r?e<90||e>270?"right":"left":e>180?"bottom":"top"}},{key:"getVerticalOrientations",value:function(t){return t<45||t>315?"right":t>=45&&t<=135?"top":t>135&&t<225?"left":"bottom"}},{key:"getStyles",value:function(t){var e=t.theme||I.grayscale,r=e&&e.tooltip&&e.tooltip.style?e.tooltip.style:{},n=Array.isArray(t.style)?t.style.map((function(t){return u()({},t,r)})):u()({},t.style,r),o=e&&e.tooltip&&e.tooltip.flyoutStyle?e.tooltip.flyoutStyle:{},i=t.flyoutStyle?u()({},t.flyoutStyle,o):o,a=Array.isArray(n)?n.map((function(e){return h(e,t)})):h(n,t);return{style:a,flyoutStyle:h(i,Object.assign({},t,{style:a}))}}},{key:"getEvaluatedProps",value:function(t){var e=t.cornerRadius,r=t.centerOffset,n=t.dx,o=t.dy,a=g(t.active,t),c=g(t.text,Object.assign({},t,{active:a}));null==c&&(c=""),"number"==typeof c&&(c=c.toString());var l=this.getStyles(Object.assign({},t,{active:a,text:c})),u=l.style,s=l.flyoutStyle,f=g(t.orientation,Object.assign({},t,{active:a,text:c,style:u,flyoutStyle:s}))||this.getDefaultOrientation(t),p=y({padding:g(t.flyoutPadding,Object.assign({},t,{active:a,text:c,style:u,flyoutStyle:s,orientation:f}))||this.getLabelPadding(u)}),d=g(t.pointerWidth,Object.assign({},t,{active:a,text:c,style:u,flyoutStyle:s,orientation:f})),h=g(t.pointerLength,Object.assign({},t,{active:a,text:c,style:u,flyoutStyle:s,orientation:f})),v=et(c,u),b=this.getDimensions(Object.assign({},t,{style:u,flyoutStyle:s,active:a,text:c,orientation:f,flyoutPadding:p,pointerWidth:d,pointerLength:h}),v),O=b.flyoutHeight,m=b.flyoutWidth,x=Object.assign({},t,{active:a,text:c,style:u,flyoutStyle:s,orientation:f,flyoutHeight:O,flyoutWidth:m,flyoutPadding:p,pointerWidth:d,pointerLength:h}),j=i()(r)&&void 0!==(null==r?void 0:r.x)?g(r.x,x):0,w=i()(r)&&void 0!==(null==r?void 0:r.y)?g(r.y,x):0;return le(le({},x),{},{centerOffset:{x:j,y:w},dx:void 0!==n?g(n,x):0,dy:void 0!==o?g(o,x):0,cornerRadius:g(e,x)})}},{key:"getCalculatedValues",value:function(t){var e=t.style,r=t.text,n=t.flyoutStyle,o={height:t.flyoutHeight,width:t.flyoutWidth};return{style:e,flyoutStyle:n,labelSize:et(r,e),flyoutDimensions:o,flyoutCenter:this.getFlyoutCenter(t,o),transform:this.getTransform(t)}}},{key:"getTransform",value:function(t){var e=t.x,r=t.y,n=(t.style||{}).angle||t.angle||this.getDefaultAngle(t);return n?"rotate(".concat(n," ").concat(e," ").concat(r,")"):void 0}},{key:"getDefaultAngle",value:function(t){var e=t.polar,r=t.labelPlacement,n=t.orientation,o=t.datum;if(!e||!r||"vertical"===r)return 0;var i=b(t,o),a=0;return 0===i||180===i?a="top"===n&&180===i?270:90:i>0&&i<180?a=90-i:i>180&&i<360&&(a=270-i),a+(i>90&&i<180||i>270?1:-1)*("perpendicular"===r?0:90)}},{key:"constrainTooltip",value:function(t,e,r){var n=t.x,o=t.y,i=r.width,a=r.height,c=[0,e.width],l=[0,e.height],u=[n-i/2,n+i/2],s=[o-a/2,o+a/2],f=[u[0]<c[0]?c[0]-u[0]:0,u[1]>c[1]?u[1]-c[1]:0],p=[s[0]<l[0]?l[0]-s[0]:0,s[1]>l[1]?s[1]-l[1]:0];return{x:Math.round(n+f[0]-f[1]),y:Math.round(o+p[0]-p[1])}}},{key:"getFlyoutCenter",value:function(t,e){var r=t.x,n=t.y,o=t.dx,a=t.dy,c=t.pointerLength,l=t.orientation,u=t.constrainToVisibleArea,s=t.centerOffset,f=e.height,p=e.width,d="left"===l?-1:1,y="bottom"===l?-1:1,g={x:"left"===l||"right"===l?r+d*(c+p/2+d*o):r+o,y:"top"===l||"bottom"===l?n-y*(c+f/2-y*a):n+a},h=i()(t.center)&&void 0!==t.center.x?t.center.x:g.x,v=i()(t.center)&&void 0!==t.center.y?t.center.y:g.y,b={x:h+s.x,y:v+s.y};return u?this.constrainTooltip(b,t,e):b}},{key:"getLabelPadding",value:function(t){if(!t)return 0;var e,r=Array.isArray(t)?t.map((function(t){return t.padding})):[t.padding];return Math.max.apply(Math,(e=r,function(t){if(Array.isArray(t))return ae(t)}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return ae(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ae(t,e):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat([0]))}},{key:"getDimensions",value:function(t,e){var r,n,o,i,a=t.orientation,c=t.pointerLength,l=t.pointerWidth,u=t.flyoutHeight,s=t.flyoutWidth,f=t.flyoutPadding,p=g(t.cornerRadius,t);return{flyoutHeight:u?g(u,t):(o=e.height+f.top+f.bottom,i="top"===a||"bottom"===a?2*p:2*p+l,Math.max(i,o)),flyoutWidth:s?g(s,t):(r=e.width+f.left+f.right,n="left"===a||"right"===a?2*p+c:2*p,Math.max(n,r))}}},{key:"getLabelProps",value:function(t,e){var r=e.flyoutCenter,n=e.style,o=e.labelSize,i=e.dy,a=void 0===i?0:i,c=e.dx,l=void 0===c?0:c,s=t.text,f=t.datum,p=t.activePoints,d=t.labelComponent,y=t.index,g=t.flyoutPadding,h=(Array.isArray(n)&&n.length?n[0].textAnchor:n.textAnchor)||"middle";return u()({},d.props,{key:"".concat(this.id,"-label-").concat(y),text:s,datum:f,activePoints:p,textAnchor:h,dy:a,dx:l,style:n,x:function(){if(!h||"middle"===h)return r.x;var t="end"===h?-1:1;return r.x-t*(o.width/2)}()+(g.left-g.right)/2,y:r.y+(g.top-g.bottom)/2,verticalAnchor:"middle",angle:n.angle})}},{key:"getPointerOrientation",value:function(t,r,n){var o=r.y+n.height/2,i=r.y-n.height/2,a=r.x-n.width/2,c=r.x+n.width/2,l=[{side:"top",val:i>t.y?i-t.y:-1},{side:"bottom",val:o<t.y?t.y-o:-1},{side:"right",val:c<t.x?t.x-c:-1},{side:"left",val:a>t.x?a-t.x:-1}];return e()(l,"val","desc")[0].side}},{key:"getFlyoutProps",value:function(t,e){var r=e.flyoutDimensions,n=e.flyoutStyle,o=e.flyoutCenter,i=t.x,a=t.y,c=t.dx,l=t.dy,s=t.datum,f=t.activePoints,p=t.index,d=t.pointerLength,y=t.pointerWidth,h=t.cornerRadius,v=t.events,b=t.flyoutComponent,O=g(t.pointerOrientation,t);return u()({},b.props,{x:i,y:a,dx:c,dy:l,datum:s,activePoints:f,index:p,pointerLength:d,pointerWidth:y,cornerRadius:h,events:v,orientation:O||this.getPointerOrientation({x:i,y:a},o,r),key:"".concat(this.id,"-tooltip-").concat(p),width:r.width,height:r.height,style:n,center:o})}},{key:"renderTooltip",value:function(t){var e=g(t.active,t),r=t.renderInPortal;if(!e)return r?f().createElement(lt,null):null;var n=this.getEvaluatedProps(t),o=n.flyoutComponent,i=n.labelComponent,a=n.groupComponent,c=this.getCalculatedValues(n),l=[f().cloneElement(o,this.getFlyoutProps(n,c)),f().cloneElement(i,this.getLabelProps(n,c))],u=f().cloneElement(a,{role:"presentation",transform:c.transform},l);return r?f().createElement(lt,null,u):u}},{key:"render",value:function(){var t=function(t,e,r){var n=p(t.theme&&t.theme[r]?t.theme[r]:{},["style"]),o=function(t){if(void 0!==t.horizontal||!t.children)return t.horizontal;var e=function(t){return t.reduce((function(t,r){var n=r.props||{};return t||n.horizontal||!n.children?t||n.horizontal:e(f().Children.toArray(n.children))}),!1)};return e(f().Children.toArray(t.children))}(t),i=void 0===o?{}:{horizontal:o};return u()(i,t,n,e)}(this.props,ye,"tooltip");return this.renderTooltip(t)}}])&&se(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),s}(f().Component);ge.displayName="VictoryTooltip",ge.role="tooltip",ge.defaultProps={active:!1,renderInPortal:!0,labelComponent:f().createElement(Kt,null),flyoutComponent:f().createElement(ie,null),groupComponent:f().createElement("g",null)},ge.defaultEvents=function(t){var e=t.activateData?[{target:"labels",mutation:function(){return{active:!0}}},{target:"data",mutation:function(){return{active:!0}}}]:[{target:"labels",mutation:function(){return{active:!0}}}],r=t.activateData?[{target:"labels",mutation:function(){return{active:void 0}}},{target:"data",mutation:function(){return{active:void 0}}}]:[{target:"labels",mutation:function(){return{active:void 0}}}];return[{target:"data",eventHandlers:{onMouseOver:function(){return e},onFocus:function(){return e},onTouchStart:function(){return e},onMouseOut:function(){return r},onBlur:function(){return r},onTouchEnd:function(){return r}}}]}})(),o})()));
//# sourceMappingURL=victory-tooltip.min.js.map