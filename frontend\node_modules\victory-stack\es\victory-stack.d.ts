import React from "react";
import { CategoryPropType, ColorScalePropType, DomainPropType, EventPropTypeInterface, StringOrNumberOrCallback, VictoryCommonProps, VictoryLabelableProps, VictoryMultiLabelableProps, VictoryStyleInterface, VictoryComponentConfiguration } from "victory-core";
export declare type VictoryStackTTargetType = "data" | "labels" | "parent";
export interface VictoryStackProps extends VictoryCommonProps, VictoryLabelableProps, VictoryMultiLabelableProps {
    bins?: number | number[] | Date[];
    categories?: CategoryPropType;
    children?: React.ReactNode | React.ReactNode[];
    colorScale?: ColorScalePropType;
    domain?: DomainPropType;
    events?: EventPropTypeInterface<VictoryStackTTargetType, StringOrNumberOrCallback>[];
    eventKey?: StringOrNumberOrCallback;
    fillInMissingData?: boolean;
    style?: VictoryStyleInterface;
    xOffset?: number;
}
export declare const VictoryStack: React.NamedExoticComponent<VictoryStackProps> & {
    readonly type: (initialProps: VictoryStackProps) => JSX.Element;
} & VictoryComponentConfiguration<VictoryStackProps>;
//# sourceMappingURL=victory-stack.d.ts.map