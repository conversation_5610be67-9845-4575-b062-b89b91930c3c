!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("react")):"function"==typeof define&&define.amd?define(["react"],e):"object"==typeof exports?exports.VictorySharedEvents=e(require("react")):t.VictorySharedEvents=e(t.React)}(self,(t=>(()=>{var e={2516:(t,e)=>{function r(t,e){var r=[],n=[];return null==e&&(e=function(t,e){return r[0]===e?"[Circular ~]":"[Circular ~."+n.slice(0,r.indexOf(e)).join(".")+"]"}),function(o,i){if(r.length>0){var u=r.indexOf(this);~u?r.splice(u+1):r.push(this),~u?n.splice(u,1/0,o):n.push(o),~r.indexOf(i)&&(i=e.call(this,o,i))}else r.push(i);return null==t?i:t.call(this,o,i)}}(t.exports=function(t,e,n,o){return JSON.stringify(t,r(e,o),n)}).getSerialize=r},3273:(t,e,r)=>{var n=r(163);t.exports=function(){if(!arguments.length)return[];var t=arguments[0];return n(t)?t:[t]}},8708:(t,e,r)=>{var n=r(1171),o=r(7838),i=r(4859),u=r(4073),a=r(8541);function c(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=u,c.prototype.set=a,t.exports=c},2596:(t,e,r)=>{var n=r(2373).Symbol;t.exports=n},6082:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},5510:(t,e,r)=>{var n=r(6123);t.exports=function(t,e){return!(null==t||!t.length)&&n(t,e,0)>-1}},9955:t=>{t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},8644:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},9559:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},1634:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}},4132:(t,e,r)=>{var n=r(8347),o=r(788),i=Object.prototype.hasOwnProperty;t.exports=function(t,e,r){var u=t[e];i.call(t,e)&&o(u,r)&&(void 0!==r||e in t)||n(t,e,r)}},3162:(t,e,r)=>{var n=r(788);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return-1}},8347:(t,e,r)=>{var n=r(5525);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},6457:(t,e,r)=>{var n=r(9559),o=r(3608);t.exports=function t(e,r,i,u,a){var c=-1,s=e.length;for(i||(i=o),a||(a=[]);++c<s;){var l=e[c];r>0&&i(l)?r>1?t(l,r-1,i,u,a):n(a,l):u||(a[a.length]=l)}return a}},4432:(t,e,r)=>{var n=r(9026),o=r(3110);t.exports=function(t,e){for(var r=0,i=(e=n(e,t)).length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},563:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},6776:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},6123:t=>{t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return-1}},9070:(t,e,r)=>{var n=r(7028),o=r(3474);t.exports=function t(e,r,i,u,a){return e===r||(null==e||null==r||!o(e)&&!o(r)?e!=e&&r!=r:n(e,r,i,u,t,a))}},7028:(t,e,r)=>{var n=r(8708),o=r(8705),i=r(2813),u=r(6682),a=r(9667),c=r(163),s=r(4801),l=r(4289),f="[object Arguments]",p="[object Array]",v="[object Object]",h=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,d,b){var g=c(t),m=c(e),x=g?p:a(t),j=m?p:a(e),O=(x=x==f?v:x)==v,E=(j=j==f?v:j)==v,w=x==j;if(w&&s(t)){if(!s(e))return!1;g=!0,O=!1}if(w&&!O)return b||(b=new n),g||l(t)?o(t,e,r,y,d,b):i(t,e,x,r,y,d,b);if(!(1&r)){var A=O&&h.call(t,"__wrapped__"),_=E&&h.call(e,"__wrapped__");if(A||_){var k=A?t.value():t,S=_?e.value():e;return b||(b=new n),d(k,S,r,y,b)}}return!!w&&(b||(b=new n),u(t,e,r,y,d,b))}},9548:(t,e,r)=>{var n=r(8708),o=r(9070);t.exports=function(t,e,r,i){var u=r.length,a=u,c=!i;if(null==t)return!a;for(t=Object(t);u--;){var s=r[u];if(c&&s[2]?s[1]!==t[s[0]]:!(s[0]in t))return!1}for(;++u<a;){var l=(s=r[u])[0],f=t[l],p=s[1];if(c&&s[2]){if(void 0===f&&!(l in t))return!1}else{var v=new n;if(i)var h=i(f,p,l,t,e,v);if(!(void 0===h?o(p,f,3,i,v):h))return!1}}return!0}},3472:(t,e,r)=>{var n=r(758),o=r(5223),i=r(5346),u=r(163),a=r(8532);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?u(t)?o(t[0],t[1]):n(t):a(t)}},7190:(t,e,r)=>{var n=r(5125)(Object.keys,Object);t.exports=n},758:(t,e,r)=>{var n=r(9548),o=r(597),i=r(6878);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},5223:(t,e,r)=>{var n=r(9070),o=r(9201),i=r(5066),u=r(726),a=r(5385),c=r(6878),s=r(3110);t.exports=function(t,e){return u(t)&&a(e)?c(s(t),e):function(r){var u=o(r,t);return void 0===u&&u===e?i(r,t):n(e,u,3)}}},5436:(t,e,r)=>{var n=r(6371),o=r(5066);t.exports=function(t,e){return n(t,e,(function(e,r){return o(t,r)}))}},6371:(t,e,r)=>{var n=r(4432),o=r(6539),i=r(9026);t.exports=function(t,e,r){for(var u=-1,a=e.length,c={};++u<a;){var s=e[u],l=n(t,s);r(l,s)&&o(c,i(s,t),l)}return c}},1600:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},3301:(t,e,r)=>{var n=r(4432);t.exports=function(t){return function(e){return n(e,t)}}},6317:(t,e,r)=>{var n=r(5346),o=r(4280),i=r(201);t.exports=function(t,e){return i(o(t,e,n),t+"")}},6539:(t,e,r)=>{var n=r(4132),o=r(9026),i=r(9099),u=r(7709),a=r(3110);t.exports=function(t,e,r,c){if(!u(t))return t;for(var s=-1,l=(e=o(e,t)).length,f=l-1,p=t;null!=p&&++s<l;){var v=a(e[s]),h=r;if("__proto__"===v||"constructor"===v||"prototype"===v)return t;if(s!=f){var y=p[v];void 0===(h=c?c(y,v,p):void 0)&&(h=u(y)?y:i(e[s+1])?[]:{})}n(p,v,h),p=p[v]}return t}},6316:(t,e,r)=>{var n=r(2596),o=r(8644),i=r(163),u=r(1995),a=n?n.prototype:void 0,c=a?a.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(u(e))return c?c.call(e):"";var r=e+"";return"0"==r&&1/e==-1/0?"-0":r}},946:(t,e,r)=>{var n=r(3273),o=r(5510),i=r(9955),u=r(9914),a=r(6602),c=r(6226);t.exports=function(t,e,r){var s=-1,l=o,f=t.length,p=!0,v=[],h=v;if(r)p=!1,l=i;else if(f>=200){var y=e?null:a(t);if(y)return c(y);p=!1,l=u,h=new n}else h=e?[]:v;t:for(;++s<f;){var d=t[s],b=e?e(d):d;if(d=r||0!==d?d:0,p&&b==b){for(var g=h.length;g--;)if(h[g]===b)continue t;e&&h.push(b),v.push(d)}else l(h,b,r)||(h!==v&&h.push(b),v.push(d))}return v}},9914:(t,e,r)=>{var n=r(6123);t.exports=function(t,e){return!(null==t||!t.length)&&n(t,e,0)>-1}},9026:(t,e,r)=>{var n=r(163),o=r(726),i=r(7801),u=r(7010);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(u(t))}},6602:t=>{t.exports=function(){}},5525:(t,e,r)=>{var n=r(3743),o=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();t.exports=o},8705:(t,e,r)=>{var n=r(3273),o=r(1634),i=r(9914);t.exports=function(t,e,r,u,a,c){var s=1&r,l=t.length,f=e.length;if(l!=f&&!(s&&f>l))return!1;var p=c.get(t),v=c.get(e);if(p&&v)return p==e&&v==t;var h=-1,y=!0,d=2&r?new n:void 0;for(c.set(t,e),c.set(e,t);++h<l;){var b=t[h],g=e[h];if(u)var m=s?u(g,b,h,e,t,c):u(b,g,h,t,e,c);if(void 0!==m){if(m)continue;y=!1;break}if(d){if(!o(e,(function(t,e){if(!i(d,e)&&(b===t||a(b,t,r,u,c)))return d.push(e)}))){y=!1;break}}else if(b!==g&&!a(b,g,r,u,c)){y=!1;break}}return c.delete(t),c.delete(e),y}},2813:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},6682:(t,e,r)=>{var n=r(8143),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,u,a){var c=1&r,s=n(t),l=s.length;if(l!=n(e).length&&!c)return!1;for(var f=l;f--;){var p=s[f];if(!(c?p in e:o.call(e,p)))return!1}var v=a.get(t),h=a.get(e);if(v&&h)return v==e&&h==t;var y=!0;a.set(t,e),a.set(e,t);for(var d=c;++f<l;){var b=t[p=s[f]],g=e[p];if(i)var m=c?i(g,b,p,e,t,a):i(b,g,p,t,e,a);if(!(void 0===m?b===g||u(b,g,r,i,a):m)){y=!1;break}d||(d="constructor"==p)}if(y&&!d){var x=t.constructor,j=e.constructor;x==j||!("constructor"in t)||!("constructor"in e)||"function"==typeof x&&x instanceof x&&"function"==typeof j&&j instanceof j||(y=!1)}return a.delete(t),a.delete(e),y}},5557:(t,e,r)=>{var n=r(2645),o=r(4280),i=r(201);t.exports=function(t){return i(o(t,void 0,n),t+"")}},2117:(t,e,r)=>{var n="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;t.exports=n},8143:(t,e,r)=>{var n=r(5125)(Object.keys,Object);t.exports=n},7271:t=>{t.exports=function(t){var e=[];if(null!=t)for(var r in Object(t))e.push(r);return e}},597:(t,e,r)=>{var n=r(5385),o=r(7747);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],u=t[i];e[r]=[i,u,n(u)]}return e}},3743:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},9667:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},3096:(t,e,r)=>{var n=r(9026),o=r(5075),i=r(163),u=r(9099),a=r(8454),c=r(3110);t.exports=function(t,e,r){for(var s=-1,l=(e=n(e,t)).length,f=!1;++s<l;){var p=c(e[s]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++s!=l?f:!!(l=null==t?0:t.length)&&a(l)&&u(p,l)&&(i(t)||o(t))}},3608:(t,e,r)=>{var n=r(2596),o=r(5075),i=r(163),u=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(u&&t&&t[u])}},9099:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},8286:t=>{t.exports=function(){return!1}},726:(t,e,r)=>{var n=r(163),o=r(1995),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,u=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=t&&!o(t))||u.test(t)||!i.test(t)||null!=e&&t in Object(e)}},5782:t=>{t.exports=function(){return!1}},5385:(t,e,r)=>{var n=r(7709);t.exports=function(t){return t==t&&!n(t)}},1171:t=>{t.exports=function(){this.__data__=[],this.size=0}},7838:(t,e,r)=>{var n=r(3162),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0||(r==e.length-1?e.pop():o.call(e,r,1),--this.size,0))}},4859:(t,e,r)=>{var n=r(3162);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},4073:(t,e,r)=>{var n=r(3162);t.exports=function(t){return n(this.__data__,t)>-1}},8541:(t,e,r)=>{var n=r(3162);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},6878:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},2453:t=>{t.exports=function(t){return t}},5125:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},4280:(t,e,r)=>{var n=r(6082),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,u=-1,a=o(i.length-e,0),c=Array(a);++u<a;)c[u]=i[e+u];u=-1;for(var s=Array(e+1);++u<e;)s[u]=i[u];return s[e]=r(c),n(t,this,s)}}},2373:(t,e,r)=>{var n=r(2117),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();t.exports=i},6226:t=>{t.exports=function(){return[]}},201:t=>{t.exports=function(t){return t}},7801:(t,e,r)=>{var n=r(2453),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,u=n((function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,(function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)})),e}));t.exports=u},3110:(t,e,r)=>{var n=r(1995);t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-1/0?"-0":e}},1893:(t,e,r)=>{var n=r(6317),o=r(788),i=r(8286),u=r(8855),a=Object.prototype,c=a.hasOwnProperty,s=n((function(t,e){t=Object(t);var r=-1,n=e.length,s=n>2?e[2]:void 0;for(s&&i(e[0],e[1],s)&&(n=1);++r<n;)for(var l=e[r],f=u(l),p=-1,v=f.length;++p<v;){var h=f[p],y=t[h];(void 0===y||o(y,a[h])&&!c.call(t,h))&&(t[h]=l[h])}return t}));t.exports=s},788:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},2645:(t,e,r)=>{var n=r(6457);t.exports=function(t){return null!=t&&t.length?n(t,1):[]}},7314:t=>{t.exports=function(t){for(var e=-1,r=null==t?0:t.length,n={};++e<r;){var o=t[e];n[o[0]]=o[1]}return n}},9201:(t,e,r)=>{var n=r(4432);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},5066:(t,e,r)=>{var n=r(6776),o=r(3096);t.exports=function(t,e){return null!=t&&o(t,e,n)}},5346:t=>{t.exports=function(t){return t}},5075:t=>{t.exports=function(){return!1}},163:t=>{var e=Array.isArray;t.exports=e},981:(t,e,r)=>{var n=r(9642),o=r(8454);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},4801:t=>{t.exports=function(){return!1}},4155:(t,e,r)=>{var n=r(7190),o=r(9667),i=r(5075),u=r(163),a=r(981),c=r(4801),s=r(5782),l=r(4289),f=Object.prototype.hasOwnProperty;t.exports=function(t){if(null==t)return!0;if(a(t)&&(u(t)||"string"==typeof t||"function"==typeof t.splice||c(t)||l(t)||i(t)))return!t.length;var e=o(t);if("[object Map]"==e||"[object Set]"==e)return!t.size;if(s(t))return!n(t).length;for(var r in t)if(f.call(t,r))return!1;return!0}},9642:(t,e,r)=>{var n=r(563),o=r(7709);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},8454:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},7709:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},3474:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},1995:t=>{t.exports=function(){return!1}},4289:t=>{t.exports=function(){return!1}},7747:(t,e,r)=>{var n=r(5125)(Object.keys,Object);t.exports=n},8855:t=>{t.exports=function(t){var e=[];if(null!=t)for(var r in Object(t))e.push(r);return e}},6051:t=>{t.exports=function(t){if("function"!=typeof t)throw new TypeError("Expected a function");return function(){var e=arguments;switch(e.length){case 0:return!t.call(this);case 1:return!t.call(this,e[0]);case 2:return!t.call(this,e[0],e[1]);case 3:return!t.call(this,e[0],e[1],e[2])}return!t.apply(this,e)}}},1616:(t,e,r)=>{var n=r(3472),o=r(6051),i=r(4912);t.exports=function(t,e){return i(t,o(n(e)))}},9082:(t,e,r)=>{var n=r(5436),o=r(5557)((function(t,e){return null==t?{}:n(t,e)}));t.exports=o},4912:(t,e,r)=>{var n=r(8644),o=r(3472),i=r(6371),u=r(7271);t.exports=function(t,e){if(null==t)return{};var r=n(u(t),(function(t){return[t]}));return e=o(e),i(t,r,(function(t,r){return e(t,r[0])}))}},8532:(t,e,r)=>{var n=r(1600),o=r(3301),i=r(726),u=r(3110);t.exports=function(t){return i(t)?n(u(t)):o(t)}},7010:(t,e,r)=>{var n=r(6316);t.exports=function(t){return null==t?"":n(t)}},9602:(t,e,r)=>{var n=r(946);t.exports=function(t){return t&&t.length?n(t):[]}},7491:t=>{var e="undefined"!=typeof Element,r="function"==typeof Map,n="function"==typeof Set,o="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;function i(t,u){if(t===u)return!0;if(t&&u&&"object"==typeof t&&"object"==typeof u){if(t.constructor!==u.constructor)return!1;var a,c,s,l;if(Array.isArray(t)){if((a=t.length)!=u.length)return!1;for(c=a;0!=c--;)if(!i(t[c],u[c]))return!1;return!0}if(r&&t instanceof Map&&u instanceof Map){if(t.size!==u.size)return!1;for(l=t.entries();!(c=l.next()).done;)if(!u.has(c.value[0]))return!1;for(l=t.entries();!(c=l.next()).done;)if(!i(c.value[1],u.get(c.value[0])))return!1;return!0}if(n&&t instanceof Set&&u instanceof Set){if(t.size!==u.size)return!1;for(l=t.entries();!(c=l.next()).done;)if(!u.has(c.value[0]))return!1;return!0}if(o&&ArrayBuffer.isView(t)&&ArrayBuffer.isView(u)){if((a=t.length)!=u.length)return!1;for(c=a;0!=c--;)if(t[c]!==u[c])return!1;return!0}if(t.constructor===RegExp)return t.source===u.source&&t.flags===u.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===u.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===u.toString();if((a=(s=Object.keys(t)).length)!==Object.keys(u).length)return!1;for(c=a;0!=c--;)if(!Object.prototype.hasOwnProperty.call(u,s[c]))return!1;if(e&&t instanceof Element)return!1;for(c=a;0!=c--;)if(("_owner"!==s[c]&&"__v"!==s[c]&&"__o"!==s[c]||!t.$$typeof)&&!i(t[s[c]],u[s[c]]))return!1;return!0}return t!=t&&u!=u}t.exports=function(t,e){try{return i(t,e)}catch(t){if((t.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw t}}},9787:e=>{"use strict";e.exports=t}},r={};function n(t){var o=r[t];if(void 0!==o)return o.exports;var i=r[t]={exports:{}};return e[t](i,i.exports,n),i.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var o={};return(()=>{"use strict";n.r(o),n.d(o,{VictorySharedEvents:()=>ct});var t=n(7314),e=n.n(t),r=n(4155),i=n.n(r),u=n(1893),a=n.n(u),c=n(9787),s=n.n(c),l=n(9602),f=n.n(l),p=n(1616),v=n.n(p),h=n(4912),y=n.n(h),d=n(9082),b=n.n(d);function g(t){return"function"==typeof t}function m(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function x(t){return function(t){if(Array.isArray(t))return j(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return j(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?j(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}n(8532);var O=/^onGlobal(.*)$/;function E(t,e,r,n){var o,i=this,u=function(t){var n,o=(n=t.reduce((function(t,r){return void 0!==r.target?(Array.isArray(r.target)?r.target.includes(e):"".concat(r.target)==="".concat(e))?t.concat(r):t:t.concat(r)}),[]),void 0!==r&&"parent"!==e?n.filter((function(t){var e=t.eventKey,n=function(t){return!t||"".concat(t)==="".concat(r)};return Array.isArray(e)?e.some((function(t){return n(t)})):n(e)})):n);return Array.isArray(o)&&o.reduce((function(t,e){return e?Object.assign(t,e.eventHandlers):t}),{})},a=Array.isArray(i.componentEvents)?Array.isArray(t.events)?(o=i.componentEvents).concat.apply(o,x(t.events)):i.componentEvents:t.events,c=a&&g(n)?n(u(a),e):void 0;if(!t.sharedEvents)return c;var s=t.sharedEvents.getEvents,l=t.sharedEvents.events&&s(u(t.sharedEvents.events),e);return Object.assign({},l,c)}function w(t,e,r,n){var o=this;if(i()(t))return{};var u=n||this.baseProps,a=function(t,e){var r=t.childName,n=t.target,i=t.key,a="props"===e?u:o.state||{},c=null!=r&&a[r]?a[r]:a;return"parent"===i?c.parent:c[i]&&c[i][n]},c=function(t,n){var i="parent"===e?t.childName:t.childName||r,c=t.target||e,s=function(e,r){var n=o.state||{};if(!g(t.mutation))return n;var i=a({childName:r,key:e,target:c},"props"),s=a({childName:r,key:e,target:c},"state"),l=t.mutation(Object.assign({},i,s),u),f=n[r]||{},p=function(t){return l?function(t){return"parent"===c?Object.assign(t,m({},e,Object.assign(t[e]||{},l))):Object.assign(t,m({},e,Object.assign(t[e]||{},m({},c,l))))}(t):function(t){return t[e]&&t[e][c]&&delete t[e][c],t[e]&&!Object.keys(t[e]).length&&delete t[e],t}(t)};return null!=r?Object.assign(n,m({},r,p(f))):p(n)},l=function(e){var r=function(e){return"parent"===c?"parent":"all"===t.eventKey?u[e]?Object.keys(u[e]).filter((function(t){return"parent"!==t})):Object.keys(u).filter((function(t){return"parent"!==t})):void 0===t.eventKey&&"parent"===n?u[e]?Object.keys(u[e]):Object.keys(u):void 0!==t.eventKey?t.eventKey:n}(e);return Array.isArray(r)?r.reduce((function(t,r){return Object.assign(t,s(r,e))}),{}):s(r,e)},f="all"===i?Object.keys(u).filter((function(t){return"parent"!==t})):i;return Array.isArray(f)?f.reduce((function(t,e){return Object.assign(t,l(e))}),{}):l(f)},s=function(e,r,n,u){var a=t[u](e,r,n,o);if(!i()(a)){var s=function(t){var e=function(t){return g(t.callback)&&t.callback},r=(Array.isArray(t)?t.map((function(t){return e(t)})):[e(t)]).filter((function(t){return!1!==t}));return r.length?function(){return r.forEach((function(t){return t()}))}:void 0}(a);o.setState(function(t,e){return Array.isArray(t)?t.reduce((function(t,r){return Object.assign({},t,c(r,e))}),{}):c(t,e)}(a,n),s)}};return Object.keys(t).reduce((function(t,e){return t[e]=s,t}),{})}function A(t,e,r){var n=this.state||{};return r?n[r]&&n[r][t]&&n[r][t][e]:"parent"===t?n[t]&&n[t][e]||n[t]:n[t]&&n[t][e]}function _(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=arguments.length>3?arguments[3]:void 0,o=Object.keys(e);return o.reduce((function(o,u){var a=r[u]||{},c=e[u]||{};if("parent"===u){var s=k(t,c,a,{eventKey:u,target:"parent"});o[u]=void 0!==s?Object.assign({},a,s):a}else{var l=f()(Object.keys(c).concat(Object.keys(a)));o[u]=l.reduce((function(e,r){var o={eventKey:u,target:r,childName:n},s=k(t,c[r],a[r],o);return e[r]=void 0!==s?Object.assign({},a[r],s):a[r],y()(e,(function(t){return!i()(t)}))}),{})}return y()(o,(function(t){return!i()(t)}))}),{})}function k(t,e,r,n){var o=function(t,e){return"string"==typeof t[e]?"all"===t[e]||t[e]===n[e]:!!Array.isArray(t[e])&&t[e].map((function(t){return"".concat(t)})).includes(n[e])},u=Array.isArray(t)?t:[t];n.childName&&(u=t.filter((function(t){return o(t,"childName")})));var a=u.filter((function(t){return o(t,"target")}));if(!i()(a)){var c=a.filter((function(t){return o(t,"eventKey")}));if(!i()(c))return c.reduce((function(t,n){var o=(n&&g(n.mutation)?n.mutation:function(){})(Object.assign({},e,r));return Object.assign({},t,o)}),{})}}function S(t){var e=t.match(O);return e&&e[1]&&e[1].toLowerCase()}function P(t,e){return t&&e?t.filter((function(t){return!e.includes(t)})):[]}var C,T,G=0,M=0,K=0,N=0,B=0,z=0,I="object"==typeof performance&&performance.now?performance:Date,F="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function L(){return B||(F($),B=I.now()+z)}function $(){B=0}function R(){this._call=this._time=this._next=null}function V(t,e,r){var n=new R;return n.restart(t,e,r),n}function D(){B=(N=I.now())+z,G=M=0;try{!function(){L(),++G;for(var t,e=C;e;)(t=B-e._time)>=0&&e._call.call(void 0,t),e=e._next;--G}()}finally{G=0,function(){for(var t,e,r=C,n=1/0;r;)r._call?(n>r._time&&(n=r._time),t=r,r=r._next):(e=r._next,r._next=null,r=t?t._next=e:C=e);T=t,q(n)}(),B=0}}function U(){var t=I.now(),e=t-N;e>1e3&&(z-=e,N=t)}function q(t){G||(M&&(M=clearTimeout(M)),t-B>24?(t<1/0&&(M=setTimeout(D,t-I.now()-z)),K&&(K=clearInterval(K))):(K||(N=I.now(),K=setInterval(U,1e3)),G=1,F(D)))}function H(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}R.prototype=V.prototype={constructor:R,restart:function(t,e,r){if("function"!=typeof t)throw new TypeError("callback is not a function");r=(null==r?L():+r)+(null==e?0:+e),this._next||T===this||(T?T._next=this:C=this,T=this),this._call=t,this._time=r,q()},stop:function(){this._call&&(this._call=null,this._time=1/0,q())}};var J=function(){function t(){var e=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.shouldAnimate=void 0,this.subscribers=void 0,this.activeSubscriptions=void 0,this.timer=void 0,this.loop=function(){e.subscribers.forEach((function(t){t.callback(L()-t.startTime,t.duration)}))},this.shouldAnimate=!0,this.subscribers=[],this.timer=null,this.activeSubscriptions=0}var e,r;return e=t,(r=[{key:"bypassAnimation",value:function(){this.shouldAnimate=!1}},{key:"resumeAnimation",value:function(){this.shouldAnimate=!0}},{key:"start",value:function(){this.timer||(this.timer=V(this.loop))}},{key:"stop",value:function(){this.timer&&(this.timer.stop(),this.timer=null)}},{key:"subscribe",value:function(t,e){var r=this.subscribers.push({startTime:L(),callback:t,duration:this.shouldAnimate?e:0});return this.activeSubscriptions++,this.start(),r}},{key:"unsubscribe",value:function(t){null!==t&&this.subscribers[t-1]&&(delete this.subscribers[t-1],this.activeSubscriptions--),0===this.activeSubscriptions&&this.stop()}}])&&H(e.prototype,r),Object.defineProperty(e,"prototype",{writable:!1}),t}(),W=s().createContext({transitionTimer:new J,animationTimer:new J});W.displayName="TimerContext";const Q=W;var X=n(7491),Y=n.n(X),Z=n(2516),tt=n.n(Z);function et(t,e){if(t){if("string"==typeof t)return rt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?rt(t,e):void 0}}function rt(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function nt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function ot(t,e){return ot=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},ot(t,e)}function it(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return ut(t)}function ut(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function at(t){return at=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},at(t)}var ct=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ot(t,e)}(f,t);var r,n,o,u,l=(o=f,u=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=at(o);if(u){var r=at(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return it(this,t)});function f(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,f),(e=l.call(this,t)).getScopedEvents=void 0,e.getEventState=void 0,e.baseProps=void 0,e.sharedEventsCache=void 0,e.globalEvents=void 0,e.prevGlobalEventKeys=void 0,e.boundGlobalEvents=void 0,e.getScopedEvents=w.bind(ut(e)),e.getEventState=A.bind(ut(e)),e.state=e.state||{},e.sharedEventsCache={},e.globalEvents={},e.prevGlobalEventKeys=[],e.boundGlobalEvents={},e.baseProps=e.getBaseProps(t),e}return r=f,n=[{key:"shouldComponentUpdate",value:function(t){if(!Y()(this.props,t)){this.baseProps=this.getBaseProps(t);var e=this.getExternalMutations(t,this.baseProps);this.applyExternalMutations(t,e)}return!0}},{key:"componentDidMount",value:function(){var t=this,e=Object.keys(this.globalEvents);e.forEach((function(e){return t.addGlobalListener(e)})),this.prevGlobalEventKeys=e}},{key:"componentDidUpdate",value:function(){var t=this,e=Object.keys(this.globalEvents);P(this.prevGlobalEventKeys,e).forEach((function(e){return t.removeGlobalListener(e)})),P(e,this.prevGlobalEventKeys).forEach((function(e){return t.addGlobalListener(e)})),this.prevGlobalEventKeys=e}},{key:"componentWillUnmount",value:function(){var t=this;this.prevGlobalEventKeys.forEach((function(e){return t.removeGlobalListener(e)}))}},{key:"addGlobalListener",value:function(t){var e=this,r=function(r){var n=e.globalEvents[t];return n&&n(function(t){return Object.assign(t,{nativeEvent:t})}(r))};this.boundGlobalEvents[t]=r,window.addEventListener(S(t),r)}},{key:"removeGlobalListener",value:function(t){window.removeEventListener(S(t),this.boundGlobalEvents[t])}},{key:"getAllEvents",value:function(t){var e,r=function(t,e){var r=Array.isArray(e)&&e.reduce((function(e,r){var n=t[r],o=n&&n.type&&n.type.defaultEvents,i=g(o)?o(n.props):o;return Array.isArray(i)?e.concat.apply(e,x(i)):e}),[]);return r&&r.length?r:void 0}(t,["container","groupComponent"]);return Array.isArray(r)?Array.isArray(t.events)?r.concat.apply(r,function(t){if(Array.isArray(t))return rt(t)}(e=t.events)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||et(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):r:t.events}},{key:"applyExternalMutations",value:function(t,e){if(!i()(e)){var r=t.externalEventMutations.reduce((function(t,e){return g(e.callback)?t.concat(e.callback):t}),[]),n=r.length?function(){r.forEach((function(t){return t()}))}:void 0;this.setState(e,n)}}},{key:"getExternalMutations",value:function(t,e){return i()(t.externalEventMutations)?void 0:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return(arguments.length>3?arguments[3]:void 0).reduce((function(n,o){var u=r[o],a=_(t,e[o],r[o],o);return n[o]=a||u,y()(n,(function(t){return!i()(t)}))}),{})}(t.externalEventMutations,e,this.state,Object.keys(e))}},{key:"cacheSharedEvents",value:function(t,e,r){this.sharedEventsCache[t]=[e,r]}},{key:"getCachedSharedEvents",value:function(t,e){var r,n,o=(r=this.sharedEventsCache[t]||[],n=2,function(t){if(Array.isArray(t))return t}(r)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i=[],u=!0,a=!1;try{for(r=r.call(t);!(u=(n=r.next()).done)&&(i.push(n.value),!e||i.length!==e);u=!0);}catch(t){a=!0,o=t}finally{try{u||null==r.return||r.return()}finally{if(a)throw o}}return i}}(r,n)||et(r,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),i=o[0],u=o[1];if(i&&Y()(e,u))return i}},{key:"getBaseProps",value:function(t){var e=t.container,r=s().Children.toArray(this.props.children),n=this.getBasePropsFromChildren(r),o=e?e.props:{};return Object.assign({},n,{parent:o})}},{key:"getBasePropsFromChildren",value:function(t){var r=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:function(t,e){return t.concat(e)},i=["data","domain","categories","polar","startAngle","endAngle","minDomain","maxDomain","horizontal"],u=function(t,a,c){return t.reduce((function(t,n,l){var f=t,p=n.type&&n.type.role,v=n.props.name||"".concat(p,"-").concat(a[l]);if(n.props&&n.props.children){var h=Object.assign({},n.props,b()(r,i)),y=n.type&&"stack"===n.type.role&&g(n.type.getChildren)?n.type.getChildren(h):s().Children.toArray(n.props.children).map((function(t){var e=Object.assign({},t.props,b()(h,i));return s().cloneElement(t,e)})),d=y.map((function(t,e){return"".concat(v,"-").concat(e)})),m=u(y,d,n);f=o(f,m)}else{var x=e(n,v,c);x&&(f=o(f,x))}return f}),n)},a=t.filter(c.isValidElement),l=a.map((function(t,e){return e}));return u(a,l)}(t,(function(t,e){if(t.type&&g(t.type.getBaseProps)){var r=t.props&&t.type.getBaseProps(t.props);return r?[[e,r]]:null}return null}));return e()(r)}},{key:"getNewChildren",value:function(t,e){var r=this,n=t.events,o=t.eventKey,i=function(t,u){return t.reduce((function(t,a,c){if(a.props.children){var l=s().Children.toArray(a.props.children),f=u.slice(c,c+l.length),p=s().cloneElement(a,a.props,i(l,f));return t.concat(p)}if("parent"!==u[c]&&a.type&&g(a.type.getBaseProps)){var v=a.props.name||u[c],h=Array.isArray(n)&&n.filter((function(t){return"parent"!==t.target&&(Array.isArray(t.childName)?t.childName.indexOf(v)>-1:t.childName===v||"all"===t.childName)})),y=[v,e,h,tt()(r.state[v])],d=r.getCachedSharedEvents(v,y)||{events:h,getEvents:function(t,n){return r.getScopedEvents(t,n,v,e)},getEventState:function(t,e){return r.getEventState(t,e,v)}};return r.cacheSharedEvents(v,d,y),t.concat(s().cloneElement(a,Object.assign({key:"events-".concat(v),sharedEvents:d,eventKey:o,name:v},a.props)))}return t.concat(a)}),[])},u=Object.keys(e),a=s().Children.toArray(t.children);return i(a,u)}},{key:"getContainer",value:function(t,e,r){var n=this,o=this.getNewChildren(t,e),i=Array.isArray(r)?r.filter((function(t){return"parent"===t.target})):[],u=i.length>0?{events:i,getEvents:function(t,r){return n.getScopedEvents(t,r,null,e)},getEventState:this.getEventState}:null,c=t.container||t.groupComponent,l=c.type&&c.type.role,f=c.props||{},p=E.bind(this),h=u&&p({sharedEvents:u},"parent"),d=a()({},this.getEventState("parent","parent"),f,e.parent,{children:o}),b=a()({},function(t,e,r){return t?Object.keys(t).reduce((function(e,n){return e[n]=function(e){return t[n](e,r,"parent",n)},e}),{}):{}}(h,0,d),f.events);this.globalEvents=function(t){return y()(t,(function(t,e){return O.test(e)}))}(b);var g=function(t){return v()(t,(function(t,e){return O.test(e)}))}(b);return"container"===l?s().cloneElement(c,Object.assign({},d,{events:g})):s().cloneElement(c,g,o)}},{key:"render",value:function(){var t=this.getAllEvents(this.props);return t?this.getContainer(this.props,this.baseProps,t):s().cloneElement(this.props.container,{children:this.props.children})}}],n&&nt(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),f}(s().Component);ct.displayName="VictorySharedEvents",ct.role="shared-event-wrapper",ct.contextType=Q,ct.defaultProps={groupComponent:s().createElement("g",null)}})(),o})()));
//# sourceMappingURL=victory-shared-events.min.js.map