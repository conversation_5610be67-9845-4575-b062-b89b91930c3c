import CalendarLocale from "rc-picker/es/locale/tr_TR";
import TimePickerLocale from '../../time-picker/locale/tr_TR';
// Merge into a locale object
const locale = {
  lang: Object.assign({
    placeholder: '<PERSON><PERSON><PERSON> seç',
    yearPlaceholder: '<PERSON><PERSON><PERSON> seç',
    quarterPlaceholder: '<PERSON><PERSON><PERSON> seç',
    monthPlaceholder: '<PERSON><PERSON> seç',
    weekPlaceholder: 'Hafta seç',
    rangePlaceholder: ['<PERSON>şlangıç tarihi', 'Bitiş tarihi'],
    rangeYearPlaceholder: ['<PERSON><PERSON>lang<PERSON><PERSON> yılı', 'Bitiş yılı'],
    rangeMonthPlaceholder: ['Başlangıç ayı', 'Bit<PERSON>ş ayı'],
    rangeWeekPlaceholder: ['Başlangıç haftası', 'Bitiş haftası']
  }, CalendarLocale),
  timePickerLocale: Object.assign({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;