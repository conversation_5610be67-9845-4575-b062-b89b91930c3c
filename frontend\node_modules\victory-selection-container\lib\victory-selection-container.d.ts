import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>ontaine<PERSON>, VictoryContainerProps } from "victory-core";
export interface VictorySelectionContainerProps extends VictoryContainerProps {
    activateSelectedData?: boolean;
    allowSelection?: boolean;
    disable?: boolean;
    onSelection?: (points: {
        childName?: string | string[];
        eventKey?: string | number;
        data?: Datum[];
    }[], bounds: {
        x: number | Date;
        y: number | Date;
    }[], props: VictorySelectionContainerProps) => void;
    horizontal?: boolean;
    onSelectionCleared?: (props: VictorySelectionContainerProps) => void;
    selectionBlacklist?: string[];
    selectionComponent?: React.ReactElement;
    selectionDimension?: "x" | "y";
    selectionStyle?: React.CSSProperties;
}
declare type ComponentClass<TProps> = {
    new (props: TProps): React.Component<TProps>;
};
export declare function selectionContainerMixin<TBase extends ComponentClass<TProps>, TProps extends VictorySelectionContainerProps>(Base: TBase): {
    new (props: TProps): {
        getRect(props: any): React.DetailedReactHTMLElement<{
            key: string;
            x: number;
            y: number;
            width: number;
            height: number;
            style: any;
        }, HTMLElement> | null;
        getChildren(props: TProps): (string | number | React.ReactElement<any, string | React.JSXElementConstructor<any>> | React.ReactFragment | null)[];
        context: unknown;
        setState<K extends never>(state: {} | ((prevState: Readonly<{}>, props: Readonly<TProps>) => {} | Pick<{}, K> | null) | Pick<{}, K> | null, callback?: (() => void) | undefined): void;
        forceUpdate(callback?: (() => void) | undefined): void;
        render(): React.ReactNode;
        readonly props: Readonly<TProps>;
        state: Readonly<{}>;
        refs: {
            [key: string]: React.ReactInstance;
        };
        componentDidMount?(): void;
        shouldComponentUpdate?(nextProps: Readonly<TProps>, nextState: Readonly<{}>, nextContext: any): boolean;
        componentWillUnmount?(): void;
        componentDidCatch?(error: Error, errorInfo: React.ErrorInfo): void;
        getSnapshotBeforeUpdate?(prevProps: Readonly<TProps>, prevState: Readonly<{}>): any;
        componentDidUpdate?(prevProps: Readonly<TProps>, prevState: Readonly<{}>, snapshot?: any): void;
        componentWillMount?(): void;
        UNSAFE_componentWillMount?(): void;
        componentWillReceiveProps?(nextProps: Readonly<TProps>, nextContext: any): void;
        UNSAFE_componentWillReceiveProps?(nextProps: Readonly<TProps>, nextContext: any): void;
        componentWillUpdate?(nextProps: Readonly<TProps>, nextState: Readonly<{}>, nextContext: any): void;
        UNSAFE_componentWillUpdate?(nextProps: Readonly<TProps>, nextState: Readonly<{}>, nextContext: any): void;
    };
    displayName: string;
    defaultProps: {
        activateSelectedData: boolean;
        allowSelection: boolean;
        selectionComponent: JSX.Element;
        selectionStyle: {
            stroke: string;
            fill: string;
            fillOpacity: number;
        };
        className: string;
        portalComponent: JSX.Element;
        portalZIndex: number;
        responsive: boolean;
        role: string;
    };
    defaultEvents: (props: TProps) => {
        target: string;
        eventHandlers: {
            onMouseDown: (evt: any, targetProps: any) => {};
            onTouchStart: (evt: any, targetProps: any) => {};
            onMouseMove: (evt: any, targetProps: any) => {} | null | undefined;
            onTouchMove: (evt: any, targetProps: any) => {} | null | undefined;
            onMouseUp: (evt: any, targetProps: any) => {} | null;
            onTouchEnd: (evt: any, targetProps: any) => {} | null;
        };
    }[];
} & TBase;
export declare const VictorySelectionContainer: {
    new (props: VictorySelectionContainerProps): {
        getRect(props: any): React.DetailedReactHTMLElement<{
            key: string;
            x: number;
            y: number;
            width: number;
            height: number;
            style: any;
        }, HTMLElement> | null;
        getChildren(props: VictorySelectionContainerProps): (string | number | React.ReactElement<any, string | React.JSXElementConstructor<any>> | React.ReactFragment | null)[];
        context: unknown;
        setState<K extends never>(state: {} | ((prevState: Readonly<{}>, props: Readonly<VictorySelectionContainerProps>) => {} | Pick<{}, K> | null) | Pick<{}, K> | null, callback?: (() => void) | undefined): void;
        forceUpdate(callback?: (() => void) | undefined): void;
        render(): React.ReactNode;
        readonly props: Readonly<VictorySelectionContainerProps>;
        state: Readonly<{}>;
        refs: {
            [key: string]: React.ReactInstance;
        };
        componentDidMount?(): void;
        shouldComponentUpdate?(nextProps: Readonly<VictorySelectionContainerProps>, nextState: Readonly<{}>, nextContext: any): boolean;
        componentWillUnmount?(): void;
        componentDidCatch?(error: Error, errorInfo: React.ErrorInfo): void;
        getSnapshotBeforeUpdate?(prevProps: Readonly<VictorySelectionContainerProps>, prevState: Readonly<{}>): any;
        componentDidUpdate?(prevProps: Readonly<VictorySelectionContainerProps>, prevState: Readonly<{}>, snapshot?: any): void;
        componentWillMount?(): void;
        UNSAFE_componentWillMount?(): void;
        componentWillReceiveProps?(nextProps: Readonly<VictorySelectionContainerProps>, nextContext: any): void;
        UNSAFE_componentWillReceiveProps?(nextProps: Readonly<VictorySelectionContainerProps>, nextContext: any): void;
        componentWillUpdate?(nextProps: Readonly<VictorySelectionContainerProps>, nextState: Readonly<{}>, nextContext: any): void;
        UNSAFE_componentWillUpdate?(nextProps: Readonly<VictorySelectionContainerProps>, nextState: Readonly<{}>, nextContext: any): void;
    };
    displayName: string;
    defaultProps: {
        activateSelectedData: boolean;
        allowSelection: boolean;
        selectionComponent: JSX.Element;
        selectionStyle: {
            stroke: string;
            fill: string;
            fillOpacity: number;
        };
        className: string;
        portalComponent: JSX.Element;
        portalZIndex: number;
        responsive: boolean;
        role: string;
    };
    defaultEvents: (props: VictorySelectionContainerProps) => {
        target: string;
        eventHandlers: {
            onMouseDown: (evt: any, targetProps: any) => {};
            onTouchStart: (evt: any, targetProps: any) => {};
            onMouseMove: (evt: any, targetProps: any) => {} | null | undefined;
            onTouchMove: (evt: any, targetProps: any) => {} | null | undefined;
            onMouseUp: (evt: any, targetProps: any) => {} | null;
            onTouchEnd: (evt: any, targetProps: any) => {} | null;
        };
    }[];
} & typeof VictoryContainer;
export {};
//# sourceMappingURL=victory-selection-container.d.ts.map