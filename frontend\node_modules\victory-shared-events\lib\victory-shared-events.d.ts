/// <reference types="lodash" />
import React from "react";
import { EventCallbackInterface, EventMixinCalculatedValues, EventPropTypeInterface, StringOrNumberOrCallback } from "victory-core";
export declare type VictorySharedEventsProps = {
    children?: React.ReactElement | React.ReactElement[];
    container?: React.ReactElement;
    groupComponent?: React.ReactElement;
    events?: EventPropTypeInterface<string, StringOrNumberOrCallback>[];
    eventKey?: StringOrNumberOrCallback;
    externalEventMutations?: EventCallbackInterface<string | string[], string | number | (string | number)[]>[];
};
export interface VictorySharedEvents extends EventMixinCalculatedValues {
}
export declare class VictorySharedEvents extends React.Component<VictorySharedEventsProps> {
    static displayName: string;
    static role: string;
    static contextType: React.Context<{
        transitionTimer: import("victory-core").Timer;
        animationTimer: import("victory-core").Timer;
    }>;
    static defaultProps: {
        groupComponent: JSX.Element;
    };
    getScopedEvents: any;
    getEventState: any;
    baseProps: any;
    sharedEventsCache: any;
    globalEvents: any;
    prevGlobalEventKeys: any;
    boundGlobalEvents: any;
    constructor(props: VictorySharedEventsProps);
    shouldComponentUpdate(nextProps: any): boolean;
    componentDidMount(): void;
    componentDidUpdate(): void;
    componentWillUnmount(): void;
    addGlobalListener(key: any): void;
    removeGlobalListener(key: any): void;
    getAllEvents(props: any): any;
    applyExternalMutations(props: any, externalMutations: any): void;
    getExternalMutations(props: any, baseProps: any): any;
    cacheSharedEvents(name: any, sharedEvents: any, cacheValues: any): void;
    getCachedSharedEvents(name: any, cacheValues: any): any;
    getBaseProps(props: any): import("lodash").Dictionary<any> & {
        parent: any;
    };
    getBasePropsFromChildren(childComponents: any): import("lodash").Dictionary<any>;
    getNewChildren(props: any, baseProps: any): any;
    getContainer(props: any, baseProps: any, events: any): React.DetailedReactHTMLElement<any, HTMLElement>;
    render(): React.ReactElement;
}
//# sourceMappingURL=victory-shared-events.d.ts.map