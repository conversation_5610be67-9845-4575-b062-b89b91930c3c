import _orderBy from "lodash/orderBy";

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

import React from "react";
import { Helpers, Scale, Wrapper } from "victory-core";
import isEqual from "react-fast-compare";
var fallbackProps = {
  width: 450,
  height: 300,
  padding: 50
}; // Assumes data in `datasets` is sorted by `Data.getData`.

function fillData(props, datasets) {
  var fillInMissingData = props.fillInMissingData;
  var xMap = datasets.reduce(function (prev, dataset) {
    dataset.forEach(function (datum) {
      prev[datum._x instanceof Date ? datum._x.getTime() : datum._x] = true;
    });
    return prev;
  }, {});
  var xKeys = Object.keys(xMap).map(function (k) {
    return Number(k);
  });

  var xArr = _orderBy(xKeys);

  return datasets.map(function (dataset) {
    var indexOffset = 0;
    var isDate = dataset[0] && dataset[0]._x instanceof Date;
    var filledInData = xArr.map(function (x, index) {
      var parsedX = Number(x);
      var datum = dataset[index - indexOffset];

      if (datum) {
        var x1 = isDate ? datum._x.getTime() : datum._x;

        if (x1 === parsedX) {
          return datum;
        }

        indexOffset++;

        var _y = fillInMissingData ? 0 : null;

        parsedX = isDate ? new Date(parsedX) : parsedX;
        return {
          x: parsedX,
          y: _y,
          _x: parsedX,
          _y: _y
        };
      }

      var y = fillInMissingData ? 0 : null;
      parsedX = isDate ? new Date(parsedX) : parsedX;
      return {
        x: parsedX,
        y: y,
        _x: parsedX,
        _y: y
      };
    });
    return filledInData;
  });
}

function getY0(datum, index, datasets) {
  if (datum.y0) {
    return datum.y0;
  }

  var y = datum._y;
  var group = datum._group;
  var firstDatasetBaseline = datasets[0].map(function (d) {
    return d.y0;
  });
  var previousDatasets = datasets.slice(0, index);
  var previousPoints = previousDatasets.reduce(function (prev, dataset) {
    return prev.concat(dataset.filter(function (previousDatum) {
      return datum._x instanceof Date ? previousDatum._x.getTime() === datum._x.getTime() : previousDatum._x === datum._x;
    }).map(function (previousDatum) {
      return previousDatum._y || 0;
    }));
  }, []);
  var y0 = previousPoints.length && previousPoints.reduce(function (memo, value) {
    var sameSign = y < 0 && value < 0 || y >= 0 && value >= 0;
    return sameSign ? Number(value) + memo : memo;
  }, firstDatasetBaseline[group] || 0);
  return previousPoints.some(function (point) {
    return point instanceof Date;
  }) ? new Date(y0) : y0;
}
/* eslint-disable no-nested-ternary */


function addLayoutData(props, datasets, index) {
  var xOffset = props.xOffset || 0;
  return datasets[index].map(function (datum) {
    var yOffset = getY0(datum, index, datasets) || 0;
    return Object.assign({}, datum, {
      _y0: !(datum._y instanceof Date) ? yOffset : yOffset ? new Date(yOffset) : datum._y,
      _y1: datum._y === null ? null : datum._y instanceof Date ? new Date(Number(datum._y) + Number(yOffset)) : datum._y + yOffset,
      _x1: datum._x === null ? null : datum._x instanceof Date ? new Date(Number(datum._x) + Number(xOffset)) : datum._x + xOffset
    });
  });
}
/* eslint-enable no-nested-ternary */


function stackData(props, childComponents) {
  var dataFromChildren = Wrapper.getDataFromChildren(props, childComponents);
  var filterNullChildData = dataFromChildren.map(function (dataset) {
    return dataset.filter(function (datum) {
      return datum._x !== null && datum._y !== null;
    });
  });
  var datasets = fillData(props, filterNullChildData);
  return datasets.map(function (d, i) {
    return addLayoutData(props, datasets, i);
  });
}

export function getCalculatedProps(initialProps, childComponents) {
  var children = childComponents || React.Children.toArray(initialProps.children);
  var role = "stack";
  var props = Helpers.modifyProps(initialProps, fallbackProps, role);
  var style = Wrapper.getStyle(props.theme, props.style, role);
  var categories = props.categories || Wrapper.getCategories(props, children);
  var datasets = props.datasets || stackData(props, children);
  var clonedChildren = children.map(function (c, i) {
    return /*#__PURE__*/React.cloneElement(c, {
      data: datasets[i]
    });
  });
  var domain = {
    x: Wrapper.getDomain(Object.assign({}, props, {
      categories: categories
    }), "x", clonedChildren),
    y: Wrapper.getDomain(Object.assign({}, props, {
      categories: categories
    }), "y", clonedChildren)
  };
  var range = props.range || {
    x: Helpers.getRange(props, "x"),
    y: Helpers.getRange(props, "y")
  };
  var baseScale = {
    x: Scale.getScaleFromProps(props, "x") || Wrapper.getScale(props, "x"),
    y: Scale.getScaleFromProps(props, "y") || Wrapper.getScale(props, "y")
  };
  var scale = {
    x: baseScale.x.domain(domain.x).range(props.horizontal ? range.y : range.x),
    y: baseScale.y.domain(domain.y).range(props.horizontal ? range.x : range.y)
  };
  var colorScale = props.colorScale,
      horizontal = props.horizontal;
  return {
    datasets: datasets,
    categories: categories,
    range: range,
    domain: domain,
    horizontal: horizontal,
    scale: scale,
    style: style,
    colorScale: colorScale,
    role: role
  };
} // We need to remove sharedEvents in order to memoize the calculated data
// With shared events, the props change on every event, and every value is re-calculated

var withoutSharedEvents = function (props) {
  var children = props.children;
  var modifiedChildren = React.Children.toArray(children).map(function (_child) {
    var child = _child;
    return _objectSpread(_objectSpread({}, child), {}, {
      props: Helpers.omit(child.props, ["sharedEvents"])
    });
  });
  props.children = modifiedChildren;
  return props;
};

export function useMemoizedProps(initialProps) {
  var modifiedProps = withoutSharedEvents(initialProps);

  var _React$useState = React.useState(modifiedProps),
      _React$useState2 = _slicedToArray(_React$useState, 2),
      props = _React$useState2[0],
      setProps = _React$useState2[1]; // React.memo uses shallow equality to compare objects. This way props
  // will only be re-calculated when they change.


  React.useEffect(function () {
    if (!isEqual(modifiedProps, props)) {
      setProps(modifiedProps);
    }
  }, [props, setProps, modifiedProps]);
  return React.useMemo(function () {
    return getCalculatedProps(props, props.children);
  }, [props]);
}

function getLabels(props, datasets, index) {
  if (!props.labels) {
    return undefined;
  }

  return datasets.length === index + 1 ? props.labels : undefined;
}

export function getChildProps(props, calculatedProps) {
  var categories = calculatedProps.categories,
      domain = calculatedProps.domain,
      range = calculatedProps.range,
      scale = calculatedProps.scale,
      horizontal = calculatedProps.horizontal;
  return {
    height: props.height,
    width: props.width,
    padding: Helpers.getPadding(props),
    standalone: false,
    theme: props.theme,
    categories: categories,
    domain: domain,
    range: range,
    scale: scale,
    horizontal: horizontal
  };
}

function getColorScale(props, child) {
  var role = child.type && child.type.role;
  var colorScaleOptions = child.props.colorScale || props.colorScale;

  if (role !== "group" && role !== "stack") {
    return undefined;
  }

  return props.theme ? colorScaleOptions || props.theme.props.colorScale : colorScaleOptions;
}

export function getChildren(initialProps, childComponents, calculatedProps) {
  var props = Helpers.modifyProps(initialProps, fallbackProps, "stack");
  var children = childComponents || React.Children.toArray(props.children);
  var newCalculatedProps = calculatedProps || getCalculatedProps(props, children);
  var datasets = newCalculatedProps.datasets;
  var childProps = getChildProps(props, newCalculatedProps);
  var parentName = props.name || "stack";
  return children.map(function (child, index) {
    var role = child.type && child.type.role;
    var data = datasets[index];
    var style = Wrapper.getChildStyle(child, index, newCalculatedProps);
    var labels = props.labels ? getLabels(props, datasets, index) : child.props.labels;
    var name = child.props.name || "".concat(parentName, "-").concat(role, "-").concat(index);
    return /*#__PURE__*/React.cloneElement(child, Object.assign({
      key: "".concat(name, "-key-").concat(index),
      labels: labels,
      name: name,
      domainPadding: child.props.domainPadding || props.domainPadding,
      theme: props.theme,
      labelComponent: props.labelComponent || child.props.labelComponent,
      style: style,
      colorScale: getColorScale(props, child),
      data: data,
      polar: props.polar
    }, childProps));
  });
}