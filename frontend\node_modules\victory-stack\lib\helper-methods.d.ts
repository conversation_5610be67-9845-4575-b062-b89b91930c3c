export declare function getCalculatedProps(initialProps: any, childComponents: any): {
    datasets: any;
    categories: any;
    range: any;
    domain: {
        x: any;
        y: any;
    };
    horizontal: any;
    scale: {
        x: import("victory-core").D3Scale<any>;
        y: import("victory-core").D3Scale<any>;
    };
    style: any;
    colorScale: any;
    role: string;
};
export declare function useMemoizedProps(initialProps: any): {
    datasets: any;
    categories: any;
    range: any;
    domain: {
        x: any;
        y: any;
    };
    horizontal: any;
    scale: {
        x: import("victory-core").D3Scale<any>;
        y: import("victory-core").D3Scale<any>;
    };
    style: any;
    colorScale: any;
    role: string;
};
export declare function getChildProps(props: any, calculatedProps: any): {
    height: any;
    width: any;
    padding: {
        top: any;
        bottom: any;
        left: any;
        right: any;
    };
    standalone: boolean;
    theme: any;
    categories: any;
    domain: any;
    range: any;
    scale: any;
    horizontal: any;
};
export declare function getChildren(initialProps: any, childComponents: any, calculatedProps: any): any;
//# sourceMappingURL=helper-methods.d.ts.map