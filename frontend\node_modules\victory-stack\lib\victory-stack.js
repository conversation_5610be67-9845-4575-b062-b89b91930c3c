"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.VictoryStack = void 0;

var _isEmpty2 = _interopRequireDefault(require("lodash/isEmpty"));

var _defaults2 = _interopRequireDefault(require("lodash/defaults"));

var _react = _interopRequireDefault(require("react"));

var _victoryCore = require("victory-core");

var _victorySharedEvents = require("victory-shared-events");

var _helperMethods = require("./helper-methods");

var _reactFastCompare = _interopRequireDefault(require("react-fast-compare"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

var fallbackProps = {
  width: 450,
  height: 300,
  padding: 50
};
var defaultProps = {
  containerComponent: /*#__PURE__*/_react.default.createElement(_victoryCore.VictoryContainer, null),
  groupComponent: /*#__PURE__*/_react.default.createElement("g", null),
  standalone: true,
  theme: _victoryCore.VictoryTheme.grayscale,
  fillInMissingData: true
};

var VictoryStackBase = function (initialProps) {
  var role = VictoryStack.role;

  var propsWithDefaults = _react.default.useMemo(function () {
    return _objectSpread(_objectSpread({}, defaultProps), initialProps);
  }, [initialProps]);

  var _Hooks$useAnimationSt = _victoryCore.Hooks.useAnimationState(),
      setAnimationState = _Hooks$useAnimationSt.setAnimationState,
      getAnimationProps = _Hooks$useAnimationSt.getAnimationProps,
      getProps = _Hooks$useAnimationSt.getProps;

  var props = getProps(propsWithDefaults);

  var modifiedProps = _victoryCore.Helpers.modifyProps(props, fallbackProps, role);

  var eventKey = modifiedProps.eventKey,
      containerComponent = modifiedProps.containerComponent,
      standalone = modifiedProps.standalone,
      groupComponent = modifiedProps.groupComponent,
      externalEventMutations = modifiedProps.externalEventMutations,
      width = modifiedProps.width,
      height = modifiedProps.height,
      theme = modifiedProps.theme,
      polar = modifiedProps.polar,
      horizontal = modifiedProps.horizontal,
      name = modifiedProps.name;

  var childComponents = _react.default.Children.toArray(modifiedProps.children);

  var calculatedProps = (0, _helperMethods.useMemoizedProps)(modifiedProps);
  var domain = calculatedProps.domain,
      scale = calculatedProps.scale,
      style = calculatedProps.style;

  var newChildren = _react.default.useMemo(function () {
    var children = (0, _helperMethods.getChildren)(props, childComponents, calculatedProps);
    var orderedChildren = children.map(function (child, index) {
      var childProps = Object.assign({
        animate: getAnimationProps(props, child, index)
      }, child.props);
      return /*#__PURE__*/_react.default.cloneElement(child, childProps);
    });
    /*
      reverse render order for children of `VictoryStack` so that higher children in the stack
      are rendered behind lower children. This looks nicer for stacked bars with cornerRadius, and
      areas with strokes
    */

    return orderedChildren.reverse();
  }, [props, childComponents, calculatedProps, getAnimationProps]);

  var containerProps = _react.default.useMemo(function () {
    if (standalone) {
      return {
        domain: domain,
        scale: scale,
        width: width,
        height: height,
        standalone: standalone,
        theme: theme,
        style: style.parent,
        horizontal: horizontal,
        polar: polar,
        name: name
      };
    }

    return {};
  }, [standalone, domain, scale, width, height, theme, style, horizontal, polar, name]);

  var userProps = _react.default.useMemo(function () {
    return _victoryCore.UserProps.getSafeUserProps(propsWithDefaults);
  }, [propsWithDefaults]);

  var container = _react.default.useMemo(function () {
    if (standalone) {
      var defaultContainerProps = (0, _defaults2.default)({}, containerComponent.props, containerProps, userProps);
      return /*#__PURE__*/_react.default.cloneElement(containerComponent, defaultContainerProps);
    }

    return /*#__PURE__*/_react.default.cloneElement(groupComponent, userProps);
  }, [groupComponent, standalone, containerComponent, containerProps, userProps]);

  var events = _react.default.useMemo(function () {
    return _victoryCore.Wrapper.getAllEvents(props);
  }, [props]);

  var previousProps = _victoryCore.Hooks.usePreviousProps(propsWithDefaults);

  _react.default.useEffect(function () {
    // This is called before dismount to keep state in sync
    return function () {
      if (propsWithDefaults.animate) {
        setAnimationState(previousProps, propsWithDefaults);
      }
    };
  }, [setAnimationState, previousProps, propsWithDefaults]);

  if (!(0, _isEmpty2.default)(events)) {
    return /*#__PURE__*/_react.default.createElement(_victorySharedEvents.VictorySharedEvents, {
      container: container,
      eventKey: eventKey,
      events: events,
      externalEventMutations: externalEventMutations
    }, newChildren);
  }

  return /*#__PURE__*/_react.default.cloneElement(container, container.props, newChildren);
};

var componentConfig = {
  role: "stack",
  expectedComponents: ["groupComponent", "containerComponent", "labelComponent"],
  getChildren: _helperMethods.getChildren
};
var VictoryStack = Object.assign( /*#__PURE__*/_react.default.memo(VictoryStackBase, _reactFastCompare.default), componentConfig);
exports.VictoryStack = VictoryStack;
VictoryStack.displayName = "VictoryStack";