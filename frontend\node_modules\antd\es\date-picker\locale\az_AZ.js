import CalendarLocale from "rc-picker/es/locale/az_AZ";
import TimePickerLocale from '../../time-picker/locale/az_AZ';
const locale = {
  lang: Object.assign({
    placeholder: '<PERSON><PERSON> seçin',
    rangePlaceholder: ['<PERSON><PERSON><PERSON><PERSON> tarixi', '<PERSON><PERSON><PERSON> tarixi'],
    yearPlaceholder: 'İl seçin',
    quarterPlaceholder: '<PERSON>ü<PERSON> seçin',
    monthPlaceholder: 'Ay seçin',
    weekPlaceholder: 'Həftə seçin',
    rangeYearPlaceholder: ['<PERSON><PERSON>lama il', 'Bitmə il'],
    rangeQuarterPlaceholder: ['<PERSON><PERSON><PERSON>a rüb', '<PERSON><PERSON><PERSON> rüb'],
    rangeMonthPlaceholder: ['Başlama ay', 'Bitmə ay'],
    rangeWeekPlaceholder: ['<PERSON><PERSON><PERSON>a həftə', 'Bitmə həftə']
  }, CalendarLocale),
  timePickerLocale: Object.assign({}, TimePickerLocale)
};
export default locale;