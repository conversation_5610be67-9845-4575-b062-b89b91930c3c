import _throttle from "lodash/throttle";
import _defaults from "lodash/defaults";

function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }

function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }

function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }

import { Selection, Data, Helpers } from "victory-core";
import React from "react";
var ON_MOUSE_MOVE_THROTTLE_MS = 16;

var SelectionHelpersClass = /*#__PURE__*/function () {
  function SelectionHelpersClass() {
    var _this = this;

    _classCallCheck(this, SelectionHelpersClass);

    this.onMouseDown = function (evt, targetProps) {
      evt.preventDefault();
      var activateSelectedData = targetProps.activateSelectedData,
          allowSelection = targetProps.allowSelection,
          polar = targetProps.polar,
          selectedData = targetProps.selectedData;

      if (!allowSelection) {
        return {};
      }

      var dimension = _this.getDimension(targetProps);

      var parentSVG = targetProps.parentSVG || Selection.getParentSVG(evt);

      var _Selection$getSVGEven = Selection.getSVGEventCoordinates(evt, parentSVG),
          x = _Selection$getSVGEven.x,
          y = _Selection$getSVGEven.y;

      var x1 = polar || dimension !== "y" ? x : Selection.getDomainCoordinates(targetProps).x[0];
      var y1 = polar || dimension !== "x" ? y : Selection.getDomainCoordinates(targetProps).y[0];
      var x2 = polar || dimension !== "y" ? x : Selection.getDomainCoordinates(targetProps).x[1];
      var y2 = polar || dimension !== "x" ? y : Selection.getDomainCoordinates(targetProps).y[1];
      var mutatedProps = {
        x1: x1,
        y1: y1,
        select: true,
        x2: x2,
        y2: y2,
        parentSVG: parentSVG
      };

      if (selectedData && Helpers.isFunction(targetProps.onSelectionCleared)) {
        targetProps.onSelectionCleared(_defaults({}, mutatedProps, targetProps));
      }

      var parentMutation = [{
        target: "parent",
        mutation: function () {
          return mutatedProps;
        }
      }];
      var dataMutation = selectedData && activateSelectedData ? selectedData.map(function (d) {
        return {
          childName: d.childName,
          eventKey: d.eventKey,
          target: "data",
          mutation: function () {
            return null;
          }
        };
      }) : [];
      return parentMutation.concat.apply(parentMutation, _toConsumableArray(dataMutation));
    };

    this.handleMouseMove = function (evt, targetProps) {
      var allowSelection = targetProps.allowSelection,
          select = targetProps.select,
          polar = targetProps.polar;

      var dimension = _this.getDimension(targetProps);

      if (!allowSelection || !select) {
        return null;
      }

      var parentSVG = targetProps.parentSVG || Selection.getParentSVG(evt);

      var _Selection$getSVGEven2 = Selection.getSVGEventCoordinates(evt, parentSVG),
          x = _Selection$getSVGEven2.x,
          y = _Selection$getSVGEven2.y;

      var x2 = polar || dimension !== "y" ? x : Selection.getDomainCoordinates(targetProps).x[1];
      var y2 = polar || dimension !== "x" ? y : Selection.getDomainCoordinates(targetProps).y[1];
      return {
        target: "parent",
        mutation: function () {
          return {
            x2: x2,
            y2: y2,
            parentSVG: parentSVG
          };
        }
      };
    };

    this.onMouseMove = _throttle(this.handleMouseMove, ON_MOUSE_MOVE_THROTTLE_MS, {
      leading: true,
      trailing: false
    });

    this.onMouseUp = function (evt, targetProps) {
      var activateSelectedData = targetProps.activateSelectedData,
          allowSelection = targetProps.allowSelection,
          x2 = targetProps.x2,
          y2 = targetProps.y2;

      if (!allowSelection) {
        return null;
      }

      if (!x2 || !y2) {
        return [{
          target: "parent",
          mutation: function () {
            return {
              select: false,
              x1: null,
              x2: null,
              y1: null,
              y2: null
            };
          }
        }];
      }

      var datasets = _this.getDatasets(targetProps);

      var bounds = Selection.getBounds(targetProps);

      var selectedData = _this.filterDatasets(targetProps, datasets);

      var mutatedProps = {
        selectedData: selectedData,
        datasets: datasets,
        select: false,
        x1: null,
        x2: null,
        y1: null,
        y2: null
      };
      var callbackMutation = selectedData && Helpers.isFunction(targetProps.onSelection) ? targetProps.onSelection(selectedData, bounds, _defaults({}, mutatedProps, targetProps)) : {};
      var parentMutation = [{
        target: "parent",
        mutation: function () {
          return mutatedProps;
        }
      }];
      var dataMutation = selectedData && activateSelectedData ? selectedData.map(function (d) {
        return {
          childName: d.childName,
          eventKey: d.eventKey,
          target: "data",
          mutation: function () {
            return Object.assign({
              active: true
            }, callbackMutation);
          }
        };
      }) : [];
      return parentMutation.concat(dataMutation);
    };
  }

  _createClass(SelectionHelpersClass, [{
    key: "getDimension",
    value: function getDimension(props) {
      var horizontal = props.horizontal,
          selectionDimension = props.selectionDimension;

      if (!horizontal || !selectionDimension) {
        return selectionDimension;
      }

      return selectionDimension === "x" ? "y" : "x";
    }
  }, {
    key: "getDatasets",
    value: function getDatasets(props) {
      if (props.data) {
        return [{
          data: props.data
        }];
      }

      var getData = function (childProps) {
        var data = Data.getData(childProps);
        return Array.isArray(data) && data.length > 0 ? data : undefined;
      };

      var iteratee = function (child, childName, parent) {
        var blacklist = props.selectionBlacklist || [];
        var childElement;

        if (!Data.isDataComponent(child) || blacklist.includes(childName)) {
          return null;
        } else if (child.type && Helpers.isFunction(child.type.getData)) {
          childElement = parent ? /*#__PURE__*/React.cloneElement(child, parent.props) : child;

          var _childData = childElement.props && childElement.type.getData(childElement.props);

          return _childData ? {
            childName: childName,
            data: _childData
          } : null;
        }

        var childData = getData(childElement.props);
        return childData ? {
          childName: childName,
          data: childData
        } : null;
      };

      return Helpers.reduceChildren(React.Children.toArray(props.children), iteratee, props);
    }
  }, {
    key: "filterDatasets",
    value: function filterDatasets(props, datasets) {
      var _this2 = this;

      var filtered = datasets.reduce(function (memo, dataset) {
        var selectedData = _this2.getSelectedData(props, dataset.data);

        return selectedData ? memo.concat({
          childName: dataset.childName,
          eventKey: selectedData.eventKey,
          data: selectedData.data
        }) : memo;
      }, []);
      return filtered.length ? filtered : null;
    }
  }, {
    key: "getSelectedData",
    value: function getSelectedData(props, dataset) {
      var x1 = props.x1,
          y1 = props.y1,
          x2 = props.x2,
          y2 = props.y2;

      var withinBounds = function (d) {
        var scaledPoint = Helpers.scalePoint(props, d);
        return scaledPoint.x >= Math.min(x1, x2) && scaledPoint.x <= Math.max(x1, x2) && scaledPoint.y >= Math.min(y1, y2) && scaledPoint.y <= Math.max(y1, y2);
      };

      var eventKey = [];
      var data = [];
      var count = 0;

      for (var index = 0, len = dataset.length; index < len; index++) {
        var datum = dataset[index];

        if (withinBounds(datum)) {
          data[count] = datum;
          eventKey[count] = datum.eventKey === undefined ? index : datum.eventKey;
          count++;
        }
      }

      return count > 0 ? {
        eventKey: eventKey,
        data: data
      } : null;
    }
  }]);

  return SelectionHelpersClass;
}();

export var SelectionHelpers = new SelectionHelpersClass();