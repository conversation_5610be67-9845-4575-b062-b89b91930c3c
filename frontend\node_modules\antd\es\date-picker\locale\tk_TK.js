import CalendarLocale from "rc-picker/es/locale/tk_TK";
import TimePickerLocale from '../../time-picker/locale/tk_TK';
const locale = {
  lang: Object.assign({
    placeholder: '<PERSON><PERSON>t saýlaň',
    rangePlaceholder: ['<PERSON><PERSON><PERSON><PERSON><PERSON> wagty', '<PERSON><PERSON><PERSON><PERSON><PERSON> wagty'],
    yearPlaceholder: 'Ýyl saýlaň',
    quarterPlaceholder: '<PERSON>ärýek saýlaň',
    monthPlaceholder: '<PERSON>ý saýlaň',
    weekPlaceholder: '<PERSON><PERSON><PERSON> sa<PERSON>la<PERSON>',
    rangeYearPlaceholder: ['<PERSON><PERSON><PERSON><PERSON><PERSON> ýyly', '<PERSON><PERSON><PERSON><PERSON><PERSON> ýyly'],
    rangeQuarterPlaceholder: ['<PERSON><PERSON><PERSON><PERSON><PERSON> çärýegi', '<PERSON><PERSON><PERSON><PERSON><PERSON> çä<PERSON>'],
    rangeMonthPlaceholder: ['<PERSON><PERSON><PERSON><PERSON><PERSON> aýy', '<PERSON><PERSON><PERSON><PERSON><PERSON> aýy'],
    rangeWeekPlaceholder: ['<PERSON><PERSON><PERSON>ý<PERSON> hepdesi', '<PERSON><PERSON><PERSON><PERSON><PERSON> hepdesi']
  }, CalendarLocale),
  timePickerLocale: Object.assign({}, TimePickerLocale)
};
export default locale;