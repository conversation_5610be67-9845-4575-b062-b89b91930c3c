import CalendarLocale from "rc-picker/es/locale/pt_PT";
import TimePickerLocale from '../../time-picker/locale/pt_PT';
// Merge into a locale object
const locale = {
  lang: Object.assign(Object.assign({}, CalendarLocale), {
    placeholder: 'Data',
    rangePlaceholder: ['Data inicial', 'Data final'],
    today: 'Hoje',
    now: 'Agora',
    backToToday: 'Hoje',
    ok: 'OK',
    clear: '<PERSON><PERSON>',
    month: 'Mês',
    year: 'Ano',
    timeSelect: 'Hora',
    dateSelect: 'Selecionar data',
    monthSelect: 'Selecionar mês',
    yearSelect: 'Selecionar ano',
    decadeSelect: 'Selecionar década',
    yearFormat: 'YYYY',
    monthFormat: 'MMMM',
    monthBeforeYear: false,
    previousMonth: 'Mês anterior (PageUp)',
    nextMonth: '<PERSON><PERSON><PERSON> seguin<PERSON> (PageDown)',
    previousYear: 'Ano anterior (Control + left)',
    nextYear: 'Ano seguin<PERSON> (Control + right)',
    previousDecade: 'Última década',
    nextDecade: 'Próxima década',
    previousCentury: 'Último século',
    nextCentury: 'Próximo século'
  }),
  timePickerLocale: Object.assign(Object.assign({}, TimePickerLocale), {
    placeholder: 'Hora'
  })
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;