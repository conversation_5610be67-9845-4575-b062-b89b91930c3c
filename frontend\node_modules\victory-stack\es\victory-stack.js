import _isEmpty from "lodash/isEmpty";
import _defaults from "lodash/defaults";

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

import React from "react";
import { Helpers, Hooks, UserProps, VictoryContainer, VictoryTheme, Wrapper } from "victory-core";
import { VictorySharedEvents } from "victory-shared-events";
import { getChildren, useMemoizedProps } from "./helper-methods";
import isEqual from "react-fast-compare";
var fallbackProps = {
  width: 450,
  height: 300,
  padding: 50
};
var defaultProps = {
  containerComponent: /*#__PURE__*/React.createElement(VictoryContainer, null),
  groupComponent: /*#__PURE__*/React.createElement("g", null),
  standalone: true,
  theme: VictoryTheme.grayscale,
  fillInMissingData: true
};

var VictoryStackBase = function (initialProps) {
  var role = VictoryStack.role;
  var propsWithDefaults = React.useMemo(function () {
    return _objectSpread(_objectSpread({}, defaultProps), initialProps);
  }, [initialProps]);

  var _Hooks$useAnimationSt = Hooks.useAnimationState(),
      setAnimationState = _Hooks$useAnimationSt.setAnimationState,
      getAnimationProps = _Hooks$useAnimationSt.getAnimationProps,
      getProps = _Hooks$useAnimationSt.getProps;

  var props = getProps(propsWithDefaults);
  var modifiedProps = Helpers.modifyProps(props, fallbackProps, role);
  var eventKey = modifiedProps.eventKey,
      containerComponent = modifiedProps.containerComponent,
      standalone = modifiedProps.standalone,
      groupComponent = modifiedProps.groupComponent,
      externalEventMutations = modifiedProps.externalEventMutations,
      width = modifiedProps.width,
      height = modifiedProps.height,
      theme = modifiedProps.theme,
      polar = modifiedProps.polar,
      horizontal = modifiedProps.horizontal,
      name = modifiedProps.name;
  var childComponents = React.Children.toArray(modifiedProps.children);
  var calculatedProps = useMemoizedProps(modifiedProps);
  var domain = calculatedProps.domain,
      scale = calculatedProps.scale,
      style = calculatedProps.style;
  var newChildren = React.useMemo(function () {
    var children = getChildren(props, childComponents, calculatedProps);
    var orderedChildren = children.map(function (child, index) {
      var childProps = Object.assign({
        animate: getAnimationProps(props, child, index)
      }, child.props);
      return /*#__PURE__*/React.cloneElement(child, childProps);
    });
    /*
      reverse render order for children of `VictoryStack` so that higher children in the stack
      are rendered behind lower children. This looks nicer for stacked bars with cornerRadius, and
      areas with strokes
    */

    return orderedChildren.reverse();
  }, [props, childComponents, calculatedProps, getAnimationProps]);
  var containerProps = React.useMemo(function () {
    if (standalone) {
      return {
        domain: domain,
        scale: scale,
        width: width,
        height: height,
        standalone: standalone,
        theme: theme,
        style: style.parent,
        horizontal: horizontal,
        polar: polar,
        name: name
      };
    }

    return {};
  }, [standalone, domain, scale, width, height, theme, style, horizontal, polar, name]);
  var userProps = React.useMemo(function () {
    return UserProps.getSafeUserProps(propsWithDefaults);
  }, [propsWithDefaults]);
  var container = React.useMemo(function () {
    if (standalone) {
      var defaultContainerProps = _defaults({}, containerComponent.props, containerProps, userProps);

      return /*#__PURE__*/React.cloneElement(containerComponent, defaultContainerProps);
    }

    return /*#__PURE__*/React.cloneElement(groupComponent, userProps);
  }, [groupComponent, standalone, containerComponent, containerProps, userProps]);
  var events = React.useMemo(function () {
    return Wrapper.getAllEvents(props);
  }, [props]);
  var previousProps = Hooks.usePreviousProps(propsWithDefaults);
  React.useEffect(function () {
    // This is called before dismount to keep state in sync
    return function () {
      if (propsWithDefaults.animate) {
        setAnimationState(previousProps, propsWithDefaults);
      }
    };
  }, [setAnimationState, previousProps, propsWithDefaults]);

  if (!_isEmpty(events)) {
    return /*#__PURE__*/React.createElement(VictorySharedEvents, {
      container: container,
      eventKey: eventKey,
      events: events,
      externalEventMutations: externalEventMutations
    }, newChildren);
  }

  return /*#__PURE__*/React.cloneElement(container, container.props, newChildren);
};

var componentConfig = {
  role: "stack",
  expectedComponents: ["groupComponent", "containerComponent", "labelComponent"],
  getChildren: getChildren
};
export var VictoryStack = Object.assign( /*#__PURE__*/React.memo(VictoryStackBase, isEqual), componentConfig);
VictoryStack.displayName = "VictoryStack";