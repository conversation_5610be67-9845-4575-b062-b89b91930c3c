import CalendarLocale from "rc-picker/es/locale/lt_LT";
import TimePickerLocale from '../../time-picker/locale/lt_LT';
// Merge into a locale object
const locale = {
  lang: Object.assign({
    placeholder: 'Pasirinkite datą',
    yearPlaceholder: 'Pasirinkite metus',
    quarterPlaceholder: 'Pasirinkite ketvirtį',
    monthPlaceholder: 'Pasirinkite mėnesį',
    weekPlaceholder: 'Pasirinkite savaitę',
    rangePlaceholder: ['Pradžios data', 'Pabaigos data'],
    rangeYearPlaceholder: ['Pradžios metai', 'Pabaigos metai'],
    rangeQuarterPlaceholder: ['<PERSON><PERSON><PERSON><PERSON> ketvirtis', 'Pabaigos ketvirtis'],
    rangeMonthPlaceholder: ['Pradž<PERSON> mėnesis', 'Pabaigos mėnesis'],
    rangeWeekPlaceholder: ['Prad<PERSON><PERSON> savaitė', 'Pabaigos savaitė']
  }, CalendarLocale),
  timePickerLocale: Object.assign({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;